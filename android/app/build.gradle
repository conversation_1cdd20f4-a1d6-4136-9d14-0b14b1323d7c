plugins {
    id "com.android.application"
    id "com.facebook.react"
    id "org.jetbrains.kotlin.android"
    id "com.google.gms.google-services"
}

// React Native Config
apply from: project(':react-native-config').projectDir.getPath() + "/dotenv.gradle"

/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */
react {
    // Custom configuration goes here.
    autolinkLibrariesWithApp()
    
    // Hermes enabled by default in React Native 0.79
    hermesEnabled = true
    
    // Bundle compression disabled for faster startup (React Native 0.79 default)
    enableBundleCompression = false
}

def projectRoot = rootDir.getAbsoluteFile().getParentFile().getAbsolutePath()

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = (findProperty('android.enableProguardInReleaseBuilds') ?: false).toBoolean()

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 * Not used when Her<PERSON> is enabled
 */
def jscFlavor = 'org.webkit:android-jsc:+'

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion

    namespace "com.fishivo"
    defaultConfig {
        applicationId "com.fishivo"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 1
        versionName "1.0"
        
        // React Native Config - Environment Variables
        resValue "string", "build_config_package", "com.fishivo"
        

    }

    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            // Caution! In production, you need to generate your own keystore file.
            // see https://reactnative.dev/docs/signed-apk-android.
            signingConfig signingConfigs.debug
            shrinkResources (findProperty('android.enableShrinkResourcesInReleaseBuilds')?.toBoolean() ?: false)
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        }
    }

    packagingOptions {
        pickFirst "**/libc++_shared.so"
        pickFirst "**/libjsc.so"
        pickFirst "**/libhermes.so"
        pickFirst "**/libfbjni.so"
    }
}

dependencies {
    implementation("com.facebook.react:react-android")
    implementation("androidx.swiperefreshlayout:swiperefreshlayout:1.0.0")
    implementation("androidx.core:core-splashscreen:1.0.0")

    // Google Services
    implementation("com.google.android.gms:play-services-auth:20.7.0")
    implementation("com.google.firebase:firebase-auth:22.3.0")

    // Bundled Hermes for React Native 0.79+ (hermesEnabled=true by default)
    implementation("com.facebook.react:hermes-engine:+") {
        exclude group:'com.facebook.fbjni'
    }
}
