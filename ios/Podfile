require File.join(File.dirname(`node --print "require.resolve('react-native/package.json')"`), "scripts/react_native_pods")

require 'json'
podfile_properties = JSON.parse(File.read(File.join(__dir__, 'Podfile.properties.json'))) rescue {}

ENV['RCT_NEW_ARCH_ENABLED'] = podfile_properties['newArchEnabled'] == 'true' ? '1' : '0'

platform :ios, '16.0'
install! 'cocoapods',
  :deterministic_uuids => false

prepare_react_native_project!

# Mapbox Maps configuration
$RNMapboxMapsDownloadToken = '*****************************************************************************************'
$RNMapboxMapsVersion = '11.0.0'

target 'Fishivo' do
  config = use_native_modules!

  use_frameworks! :linkage => podfile_properties['ios.useFrameworks'].to_sym if podfile_properties['ios.useFrameworks']
  use_frameworks! :linkage => ENV['USE_FRAMEWORKS'].to_sym if ENV['USE_FRAMEWORKS']

  # Mapbox Maps pre-install
  pre_install do |installer|
    $RNMapboxMaps.pre_install(installer)
  end

  use_react_native!(
    :path => config[:reactNativePath],
    :hermes_enabled => podfile_properties['hermes'] != 'false',
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/..",
    :privacy_file_aggregation_enabled => podfile_properties['apple.privacyManifestAggregationEnabled'] != 'false',
  )

  post_install do |installer|
    # Mapbox Maps post-install
    $RNMapboxMaps.post_install(installer)
    
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      :ccache_enabled => podfile_properties['apple.ccacheEnabled'] == 'true',
    )
  end
end
