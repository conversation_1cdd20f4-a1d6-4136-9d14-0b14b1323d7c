import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_CONFIG } from '../config';

// API Response types
interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: string;
  message?: string;
}

interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

interface Post {
  id: number;
  user_id: string;
  content: string;
  image_url?: string;
  images?: string[];
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
    city?: string;
    country?: string;
  };
  spot_id?: number;
  trip_id?: number;
  catch_details?: {
    species_id?: number;
    species_name?: string;
    weight?: number;
    length?: number;
    bait_used?: string;
    technique?: string;
    weather_conditions?: string;
    water_temperature?: number;
    time_of_day?: 'morning' | 'afternoon' | 'evening' | 'night';
    equipment_used?: string[];
  };
  likes_count: number;
  comments_count: number;
  created_at: string;
  updated_at: string;
  user?: {
    id: string;
    username: string;
    full_name: string;
    avatar_url?: string;
    location?: string;
  };
  spot?: {
    id: number;
    name: string;
    location: {
      latitude: number;
      longitude: number;
      address?: string;
    };
  };
}

class ApiService {
  private baseURL = API_CONFIG.API_URL;

  private async getAuthToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem('userToken');
    } catch (error) {
      console.error('Error getting auth token:', error);
      return null;
    }
  }

  // Token management methods for AuthContext
  async getToken(): Promise<string | null> {
    return this.getAuthToken();
  }

  async setToken(token: string): Promise<void> {
    try {
      await AsyncStorage.setItem('userToken', token);
    } catch (error) {
      console.error('Error setting auth token:', error);
      throw error;
    }
  }

  async removeToken(): Promise<void> {
    try {
      await AsyncStorage.removeItem('userToken');
    } catch (error) {
      console.error('Error removing auth token:', error);
      throw error;
    }
  }

  async getCurrentUser(): Promise<ApiResponse<any>> {
    try {
      const response = await this.makeRequest<any>('/auth/me');
      return response;
    } catch (error) {
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const token = await this.getAuthToken();
      
      const headers: { [key: string]: string } = {
        'Content-Type': 'application/json',
        ...((options.headers as { [key: string]: string }) || {}),
      };

      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      console.log(`🌐 API Request: ${endpoint}`);
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        ...options,
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.error || `HTTP ${response.status}`);
      }

      const result = await response.json();
      console.log(`✅ API Response: ${endpoint}`, result);
      return result;
    } catch (error) {
      console.error(`❌ API Error [${endpoint}]:`, error);
      throw error;
    }
  }

  // 🎣 Catches API - Ana balık avları endpoint'i
  async getCatches(page = 1, limit = 10, filters?: {
    userId?: string;
    spotId?: number;
    tripId?: number;
  }): Promise<PaginatedResponse<Post>> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (filters?.userId) params.append('user_id', filters.userId);
    if (filters?.spotId) params.append('spot_id', filters.spotId.toString());
    if (filters?.tripId) params.append('trip_id', filters.tripId.toString());

    const response = await this.makeRequest<PaginatedResponse<Post>>(
      `/posts?${params.toString()}`
    );
    
    return response.data;
  }

  // 🔄 Geriye uyumluluk için
  async getPosts(page = 1, limit = 10, filters?: {
    userId?: string;
    spotId?: number;
    tripId?: number;
  }): Promise<PaginatedResponse<Post>> {
    return this.getCatches(page, limit, filters);
  }

  async getPostById(postId: number): Promise<Post> {
    const response = await this.makeRequest<Post>(`/posts/${postId}`);
    return response.data;
  }

  async createPost(postData: {
    content: string;
    image_url?: string;
    images?: string[];
    location?: {
      latitude: number;
      longitude: number;
      address?: string;
      city?: string;
      country?: string;
    };
    spot_id?: number;
    trip_id?: number;
    catch_details?: {
      species_id?: number;
      species_name?: string;
      weight?: number;
      length?: number;
      bait_used?: string;
      technique?: string;
      weather_conditions?: string;
      water_temperature?: number;
      time_of_day?: 'morning' | 'afternoon' | 'evening' | 'night';
      equipment_used?: string[];
    };
  }): Promise<Post> {
    const response = await this.makeRequest<Post>('/posts', {
      method: 'POST',
      body: JSON.stringify(postData),
    });
    return response.data;
  }

  async likePost(postId: number): Promise<void> {
    await this.makeRequest(`/posts/${postId}/like`, {
      method: 'POST',
    });
  }

  async unlikePost(postId: number): Promise<void> {
    await this.makeRequest(`/posts/${postId}/like`, {
      method: 'DELETE',
    });
  }

  // Spots API - Backend format'ında zaten doğru
  async getSpots(page = 1, limit = 10): Promise<PaginatedResponse<any>> {
    const response = await this.makeRequest<PaginatedResponse<any>>(
      `/spots?page=${page}&limit=${limit}`
    );
    return response.data;
  }

  async searchSpots(query: string, page = 1, limit = 10): Promise<PaginatedResponse<any>> {
    const response = await this.makeRequest<PaginatedResponse<any>>(
      `/spots/search?query=${encodeURIComponent(query)}&page=${page}&limit=${limit}`
    );
    return response.data;
  }

  async getSpotById(spotId: number): Promise<any> {
    const response = await this.makeRequest<any>(`/spots/${spotId}`);
    return response.data;
  }

  // Auth API
  async loginWithGoogle(token: string): Promise<{ user: any; token: string }> {
    const response = await this.makeRequest<{ user: any; token: string }>('/auth/google', {
      method: 'POST',
      body: JSON.stringify({ token }),
    });
    return response.data;
  }

  async loginWithFacebook(token: string): Promise<{ user: any; token: string }> {
    const response = await this.makeRequest<{ user: any; token: string }>('/auth/facebook', {
      method: 'POST',
      body: JSON.stringify({ token }),
    });
    return response.data;
  }

  // Upload API
  async uploadImage(imageUri: string): Promise<string> {
    const response = await this.makeRequest<{ url: string }>('/upload', {
      method: 'POST',
      body: JSON.stringify({ imageUri }),
    });
    return response.data.url;
  }

  // Species API
  async getSpecies(): Promise<any[]> {
    const response = await this.makeRequest<any[]>('/species');
    return response.data;
  }

  async getSpeciesById(id: number): Promise<any> {
    const response = await this.makeRequest<any>(`/species/${id}`);
    return response.data;
  }

  // Notifications API
  async getNotifications(userId: string, page = 1, limit = 20): Promise<any[]> {
    const response = await this.makeRequest<any[]>(
      `/notifications?userId=${userId}&page=${page}&limit=${limit}`
    );
    return response.data;
  }

  // User Equipment API
  async getUserEquipment(): Promise<any[]> {
    const response = await this.makeRequest<any[]>('/users/me/equipment');
    return response.data;
  }

  async addUserEquipment(equipmentData: any): Promise<any> {
    const response = await this.makeRequest<any>('/users/me/equipment', {
      method: 'POST',
      body: JSON.stringify(equipmentData),
    });
    return response.data;
  }

  // Profile API
  async updateProfile(profileData: any): Promise<any> {
    const response = await this.makeRequest<any>('/users/me', {
      method: 'PUT',
      body: JSON.stringify(profileData),
    });
    return response.data;
  }

  // Blocked Users API
  async getBlockedUsers(page = 1, limit = 20): Promise<PaginatedResponse<any>> {
    const response = await this.makeRequest<PaginatedResponse<any>>(
      `/users/me/blocked?page=${page}&limit=${limit}`
    );
    return response.data;
  }

  async blockUser(userId: string): Promise<void> {
    await this.makeRequest<void>(`/users/block/${userId}`, {
      method: 'POST',
    });
  }

  async unblockUser(userId: string): Promise<void> {
    await this.makeRequest<void>(`/users/block/${userId}`, {
      method: 'DELETE',
    });
  }

  // Search API
  async searchUsers(query: string, page = 1, limit = 10): Promise<PaginatedResponse<any>> {
    const response = await this.makeRequest<PaginatedResponse<any>>(
      `/search/users?query=${encodeURIComponent(query)}&page=${page}&limit=${limit}`
    );
    return response.data;
  }

  async searchSpecies(query: string): Promise<any[]> {
    const response = await this.makeRequest<any[]>(
      `/species/search?query=${encodeURIComponent(query)}`
    );
    return response.data;
  }

  async searchEquipment(query: string): Promise<any[]> {
    const response = await this.makeRequest<any[]>(
      `/equipment/search?query=${encodeURIComponent(query)}`
    );
    return response.data;
  }

  // Weather API
  async getCurrentWeather(lat: number, lon: number): Promise<any> {
    const response = await this.makeRequest<any>(`/weather/current?lat=${lat}&lon=${lon}`);
    return response.data;
  }

  async getWeatherForecast(lat: number, lon: number, days = 5): Promise<any> {
    const response = await this.makeRequest<any>(`/weather/forecast?lat=${lat}&lon=${lon}&days=${days}`);
    return response.data;
  }

  // Equipment/Gear API
  async getGearDatabase(page = 1, limit = 50, category?: string): Promise<any> {
    const params = new URLSearchParams({ page: page.toString(), limit: limit.toString() });
    if (category) params.append('category', category);

    const response = await this.makeRequest<any>(`/equipment?${params.toString()}`);
    return response.data;
  }

  async getEquipmentCategories(): Promise<any> {
    const response = await this.makeRequest<any>('/equipment/categories');
    return response.data;
  }

  // User catches API
  async getUserCatches(userId: string, page = 1, limit = 10): Promise<any> {
    const response = await this.makeRequest<any>(`/users/${userId}/catches?page=${page}&limit=${limit}`);
    return response.data;
  }

  // Fishing Techniques API
  async getFishingTechniques(): Promise<any[]> {
    const response = await this.makeRequest<any[]>('/techniques');
    return response.data;
  }

  async searchFishingTechniques(query: string): Promise<any[]> {
    const response = await this.makeRequest<any[]>(`/techniques/search?q=${encodeURIComponent(query)}`);
    return response.data;
  }

  async getFishingTechniqueById(id: number): Promise<any> {
    const response = await this.makeRequest<any>(`/techniques/${id}`);
    return response.data;
  }

  // User Locations API
  async getUserLocations(): Promise<any[]> {
    const response = await this.makeRequest<any[]>('/users/me/locations');
    return response.data;
  }
}

export const apiService = new ApiService();
export type { Post, PaginatedResponse, ApiResponse }; 