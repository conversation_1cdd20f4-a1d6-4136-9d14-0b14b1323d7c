import { Platform } from 'react-native';

// Environment detection
const isDevelopment = __DEV__;

// Development IP addresses - update these based on your network
const DEVELOPMENT_IPS = {
  // Your local machine IP - update this when your IP changes
  LOCAL: '***********',
  // Alternative IPs for different networks
  OFFICE: '**********', // Example office network
  HOME: '***********',  // Example home network
};

// API Configuration
export const API_CONFIG = {
  // Base URLs for different environments
  BASE_URLS: {
    DEVELOPMENT: `http://${DEVELOPMENT_IPS.LOCAL}:4000`,
    STAGING: 'https://staging-api.fishivo.com', // Future staging server
    PRODUCTION: 'https://api.fishivo.com',      // Future production server
  },
  
  // Current environment
  get CURRENT_BASE_URL() {
    if (isDevelopment) {
      return this.BASE_URLS.DEVELOPMENT;
    }
    // In future, you can add staging/production logic here
    return this.BASE_URLS.PRODUCTION;
  },
  
  // API endpoints
  ENDPOINTS: {
    API: '/api',
    AUTH: '/auth',
    POSTS: '/api/posts',
    USERS: '/api/users',
    LOCATIONS: '/api/users/me/locations',
    WEATHER: '/api/weather',
    UPLOAD: '/api/upload',
  },
  
  // Full API URL
  get API_URL() {
    return `${this.CURRENT_BASE_URL}${this.ENDPOINTS.API}`;
  },
  
  // Auth URLs
  get AUTH_URL() {
    return `${this.CURRENT_BASE_URL}${this.ENDPOINTS.AUTH}`;
  },
  
  // OAuth URLs
  OAUTH: {
    get GOOGLE_URL() {
      return `${API_CONFIG.CURRENT_BASE_URL}/auth/google/mobile`;
    },
    get FACEBOOK_URL() {
      return `${API_CONFIG.CURRENT_BASE_URL}/auth/facebook/mobile`;
    },
  },
  
  // Timeout settings
  TIMEOUT: {
    DEFAULT: 10000,    // 10 seconds
    UPLOAD: 30000,     // 30 seconds for file uploads
    DOWNLOAD: 60000,   // 60 seconds for downloads
  },
  
  // Platform specific settings
  PLATFORM: {
    IS_IOS: Platform.OS === 'ios',
    IS_ANDROID: Platform.OS === 'android',
  },
};

// Helper functions
export const ApiHelpers = {
  // Update development IP when network changes
  updateDevelopmentIP(newIP: string) {
    DEVELOPMENT_IPS.LOCAL = newIP;
    console.log(`🌐 Development IP updated to: ${newIP}`);
  },
  
  // Get full URL for any endpoint
  getFullURL(endpoint: string): string {
    return `${API_CONFIG.CURRENT_BASE_URL}${endpoint}`;
  },
  
  // Check if we're in development mode
  isDevelopment(): boolean {
    return isDevelopment;
  },
  
  // Get current environment name
  getCurrentEnvironment(): string {
    if (isDevelopment) return 'DEVELOPMENT';
    return 'PRODUCTION'; // Will be expanded for staging later
  },
  
  // Log current configuration (for debugging)
  logConfig() {
    console.log('🔧 API Configuration:', {
      environment: this.getCurrentEnvironment(),
      baseURL: API_CONFIG.CURRENT_BASE_URL,
      apiURL: API_CONFIG.API_URL,
      platform: Platform.OS,
    });
  },
};

// Export default configuration
export default API_CONFIG;

// Type definitions for TypeScript
export interface ApiEndpoint {
  url: string;
  timeout?: number;
}

export interface ApiEnvironment {
  name: string;
  baseURL: string;
  apiURL: string;
} 