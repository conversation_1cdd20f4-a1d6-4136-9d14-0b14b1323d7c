import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { UserProfile } from '../services/authService';
import { apiService } from '../services/api';

interface AuthContextType {
  user: UserProfile | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  token: string | null;
  login: (userProfile: UserProfile, token?: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

const USER_STORAGE_KEY = '@fishivo_user';
const TOKEN_STORAGE_KEY = 'userToken';

function mapBackendUserToProfile(data: any): UserProfile {
  return {
    id: data.id,
    email: data.email,
    name: data.full_name || data.username,
    picture: data.avatar_url,
    provider: data.provider || 'unknown',
    username: data.username,
    full_name: data.full_name,
    avatar_url: data.avatar_url,
    isPremium: data.isPremium || false,
  };
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = user !== null && token !== null;

  // Uygulama başlatıldığında kullanıcı durumunu kontrol et
  useEffect(() => {
    checkUserStatus();
  }, []);

  const checkUserStatus = async () => {
    try {
      const [userData, storedToken] = await Promise.all([
        AsyncStorage.getItem(USER_STORAGE_KEY),
        AsyncStorage.getItem(TOKEN_STORAGE_KEY),
      ]);

      if (userData && storedToken) {
        const parsedUser = JSON.parse(userData) as UserProfile;
        setUser(parsedUser);
        setToken(storedToken);
        apiService.setToken(storedToken);

        // Token geçerliliğini kontrol et
        const response = await apiService.getCurrentUser();
        if (!response.success && response.error?.toLowerCase().includes('401')) {
          // Token geçersiz, kullanıcıyı çıkış yaptır
          await logout();
        } else if (response.data) {
          // Kullanıcı bilgilerini güncelle
          const updatedUser = mapBackendUserToProfile(response.data);
          setUser(updatedUser);
          await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(updatedUser));
        }
      }
    } catch (error) {
      console.error('Error checking user status:', error);
      // Ağ hatası logout'a sebep olmasın
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (userProfile: UserProfile, authToken?: string) => {
    try {
      setUser(userProfile);
      await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(userProfile));

      if (authToken) {
        setToken(authToken);
        await AsyncStorage.setItem(TOKEN_STORAGE_KEY, authToken);
        apiService.setToken(authToken);
      }
    } catch (error) {
      console.error('Error saving user data:', error);
      throw new Error('Kullanıcı bilgileri kaydedilemedi');
    }
  };

  const logout = async () => {
    try {
      setUser(null);
      setToken(null);
      apiService.setToken(null);
      await Promise.all([
        AsyncStorage.removeItem(USER_STORAGE_KEY),
        AsyncStorage.removeItem(TOKEN_STORAGE_KEY),
      ]);
    } catch (error) {
      console.error('Error during logout:', error);
      throw new Error('Çıkış yapılırken hata oluştu');
    }
  };

  const refreshUser = async () => {
    if (!token) return;

    try {
      apiService.setToken(token);
      const response = await apiService.getCurrentUser();
      if (response.success && response.data) {
        const updatedUser = mapBackendUserToProfile(response.data);
        setUser(updatedUser);
        await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(updatedUser));
      }
    } catch (error) {
      console.error('Error refreshing user:', error);
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    token,
    login,
    logout,
    refreshUser,
  };

  if (isLoading) {
    return null; // veya <LoadingScreen />
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 