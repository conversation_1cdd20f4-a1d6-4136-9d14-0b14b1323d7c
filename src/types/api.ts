// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  results: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// User Types
export interface User {
  id: string;
  email: string;
  username: string;
  full_name?: string;
  avatar_url?: string;
  bio?: string;
  provider?: string;
  google_id?: string;
  facebook_id?: string;
  followers_count?: number;
  following_count?: number;
  catches_count?: number;

  equipments?: Equipment[];
  created_at: string;
  updated_at?: string;
}

// Equipment Types
export interface Equipment {
  id: string;
  name: string;
  brand?: string;
  type?: string;
  description?: string;
}

// Post Types
export interface Post {
  id: number;
  content: string;
  user_id: string;
  image_url?: string;
  location?: Location;
  catch_details?: CatchDetails;
  likes_count: number;
  comments_count: number;
  is_liked?: boolean;
  user?: User;
  created_at: string;
  updated_at?: string;
}

export interface CreatePostRequest {
  content: string;
  image_url?: string;
  location?: Location;
  catch_details?: CatchDetails;
}

// Location Types
export interface Location {
  latitude: number;
  longitude: number;
  address?: string;
  city?: string;
  country?: string;
}

// Catch Details Types
export interface CatchDetails {
  species_id?: number;
  species_name?: string;
  weight?: number;
  length?: number;
  bait_used?: string;
  weather_conditions?: string;
  water_temperature?: number;
  time_of_day?: 'morning' | 'afternoon' | 'evening' | 'night';
}

// Species Types
export interface Species {
  id: number;
  name: string;
  scientific_name?: string;
  description?: string;
  image_url?: string;
  habitat?: string;
  average_size?: string;
  tips?: string;
  created_at: string;
}

// Spot Types
export interface Spot {
  id: number;
  name: string;
  description?: string;
  location: Location;
  spot_type: 'fishing' | 'marina' | 'bait_shop' | 'restaurant';
  user_id: string;
  image_url?: string;
  rating?: number;
  depth?: number;
  facilities?: SpotFacilities;
  user?: User;
  created_at: string;
}

export interface SpotFacilities {
  parking?: boolean;
  restroom?: boolean;
  bait_shop?: boolean;
  restaurant?: boolean;
  equipment_rental?: boolean;
  boat_launch?: boolean;
  [key: string]: any;
}

export interface CreateSpotRequest {
  name: string;
  description?: string;
  location: Location;
  spot_type: 'fishing' | 'marina' | 'bait_shop' | 'restaurant';
  image_url?: string;
  depth?: number;
  facilities?: SpotFacilities;
}

// Comment Types
export interface Comment {
  id: number;
  content: string;
  user_id: string;
  post_id: number;
  user?: User;
  created_at: string;
  updated_at?: string;
}

// Like Types
export interface Like {
  id: number;
  user_id: string;
  post_id: number;
  created_at: string;
}

// Follow Types
export interface Follow {
  id: number;
  follower_id: string;
  following_id: string;
  created_at: string;
}

// Message Types
export interface Message {
  id: number;
  content: string;
  sender_id: string;
  receiver_id: string;
  read: boolean;
  sender?: User;
  receiver?: User;
  created_at: string;
}

// Search Types
export interface SearchParams {
  query: string;
  type?: 'posts' | 'users' | 'spots' | 'species';
  page?: number;
  limit?: number;
  [key: string]: any;
}



// Notification Types
export interface Notification {
  id: number;
  user_id: string;
  type: 'like' | 'comment' | 'follow' | 'message';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  created_at: string;
} 