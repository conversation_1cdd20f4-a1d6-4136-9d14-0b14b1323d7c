import React from 'react';
import {
  ArrowLeft,
  MoreHorizontal,
  MoreVertical,
  MapPin,
  Share2,
  Flag,
  UserX,
  X,
  Smartphone,
  Copy,
  Link,
  Download,
  Camera,
  Fish,
  Package,
  Settings,
  Search,
  Bell,
  Heart,
  MessageCircle,
  Plus,
  CheckCircle,
  Check,
  Edit2,
  ChevronRight,
  Thermometer,
  Wind,
  Activity,
  Sun,
  Moon,
  Clock,
  Wrench,
  Briefcase,
  Box,
  Backpack,
  ShoppingBag,
  Home,
  Map,
  User,
  Satellite,
  Trees,
  Navigation,
  LocateFixed,
  Award,
  CreditCard,
  RotateCcw,
  Mail,
  Globe,
  Users,
  Trash2,
  UserPlus,
  AtSign,
  Cloud,
  Tag,
  Star,
  Sliders,
  Ruler,
  Play,
  Info,
  HelpCircle,
  LogOut,
  Anchor,
  Zap,
  Circle,
  Layers,
  Disc,
  Minus,
  Shield,
  Droplets,
  CloudRain,
  Crosshair,
  Compass,
  Edit,
  Bookmark,
  RotateCw,
  RefreshCw,
  Rotate3d,
  AlertTriangle
} from 'lucide-react-native';
import { FishingLure, FishingHook, FishingLine, FishingReel, FishingRod, FishingNet, FishingVest } from './FishingIcons';

interface IconProps {
  name: string;
  size?: number;
  color?: string;
}

const iconMap = {
  'arrow-left': ArrowLeft,
  'more-horizontal': MoreHorizontal,
  'more-vertical': MoreVertical,
  'map-pin': MapPin,
  'share': Share2,
  'flag': Flag,
  'user-x': UserX,
  'x': X,
  'smartphone': Smartphone,
  'copy': Copy,
  'link': Link,
  'download': Download,
  'camera': Camera,
  'fish': Fish,
  'package': Package,
  'settings': Settings,
  'search': Search,
  'bell': Bell,
  'heart': Heart,
  'message-circle': MessageCircle,
  'plus': Plus,
  'check-circle': CheckCircle,
  'check': Check,
  'edit-2': Edit2,
  'chevron-right': ChevronRight,
  'thermometer': Thermometer,
  'wind': Wind,
  'activity': Activity,
  'sun': Sun,
  'moon': Moon,
  'clock': Clock,
  'wrench': Wrench,
  'briefcase': Briefcase,
  'box': Box,
  'backpack': Backpack,
  'shopping-bag': ShoppingBag,
  'home': Home,
  'map': Map,
  'user': User,
  'satellite': Satellite,
  'trees': Trees,
  'navigation': Navigation,
  'my-location': LocateFixed,
  'place': MapPin,
  'close': X,
  'content-copy': Copy,
  'share-icon': Share2,
  'trophy': Award,
  // Yeni eklenenler
  'credit-card': CreditCard,
  'refresh-cw': RotateCcw,
  'mail': Mail,
  'globe': Globe,
  'users': Users,
  'trash-2': Trash2,
  'user-plus': UserPlus,
  'at-sign': AtSign,
  'cloud': Cloud,
  'tag': Tag,
  'star': Star,
  'sliders': Sliders,
  'ruler': Ruler,
  'play': Play,
  'info': Info,
  'help-circle': HelpCircle,
  'log-out': LogOut,
  'anchor': Anchor,
  'zap': Zap,
  'circle': Circle,
  'layers': Layers,
  'disc': Disc,
  'minus': Minus,
  'tool': Wrench,
  'shield': Shield,
  'droplets': Droplets,
  'cloud-rain': CloudRain,
  'crosshair': Crosshair,
  'compass': Compass,
  'edit': Edit,
  'fishing-lure': FishingLure,
  'fishing-hook': FishingHook,
  'fishing-line': FishingLine,
  'fishing-reel': FishingReel,
  'fishing-rod': FishingRod,
  'fishing-net': FishingNet,
  'fishing-vest': FishingVest,
  'bookmark': Bookmark,
  // 3D and rotation controls
  '3d-rotation': Rotate3d,
  'rotate-right': RotateCw,
  'refresh': RefreshCw,
  'moon-stars': Moon,
  'alert-triangle': AlertTriangle
};

export const Icon: React.FC<IconProps> = ({ name, size = 20, color = '#000' }) => {
  const iconName = name as keyof typeof iconMap;
  const IconComponent = iconMap[iconName];
  
  if (!IconComponent) {
    console.warn(`Icon "${name}" not found`);
    return null;
  }

  // Custom SVG fishing icons
  if (name.startsWith('fishing-')) {
    return <IconComponent size={size} color={color} />;
  }

  // Regular Lucide icons
  return <IconComponent size={size} color={color} />;
};

export default Icon; 