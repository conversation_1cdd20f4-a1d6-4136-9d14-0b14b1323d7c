import React from 'react';
import { View, Text, ScrollView, StyleSheet } from 'react-native';
import Icon from './Icon';
import { theme } from '../theme';

interface HourlyData {
  time: string;
  condition: string;
  temp: number;
  rain: number;
}

interface HourlyForecastProps {
  data: HourlyData[];
  getConditionIcon: (condition: string) => string;
}

const HourlyForecast: React.FC<HourlyForecastProps> = ({ data, getConditionIcon }) => {
  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Saatlik Tahmin</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.hourlyScroll}>
        {data.map((hour, index) => (
          <View key={index} style={styles.hourlyItem}>
            <Text style={styles.hourlyTime}>{hour.time}</Text>
            <Icon name={getConditionIcon(hour.condition)} size={18} color={theme.colors.primary} />
            <Text style={styles.hourlyTemp}>{hour.temp}°</Text>
            <View style={styles.rainInfo}>
              <Icon name="droplets" size={10} color={theme.colors.secondary} />
              <Text style={styles.rainText}>{hour.rain}%</Text>
            </View>
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginTop: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.semibold,
    marginBottom: theme.spacing.md,
  },
  hourlyScroll: {
    marginHorizontal: -theme.screen.paddingHorizontal, // ScreenContainer padding'ini iptal et
    paddingLeft: theme.screen.paddingHorizontal, // Sol tarafta global padding
    paddingRight: theme.screen.paddingHorizontal, // Sağ tarafta global padding
  },
  hourlyItem: {
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginRight: theme.spacing.sm,
    width: 70,
    gap: theme.spacing.sm,
  },
  hourlyTime: {
    fontSize: theme.typography.xs,
    color: theme.colors.textSecondary,
    fontWeight: theme.typography.medium,
  },
  hourlyTemp: {
    fontSize: theme.typography.sm,
    color: theme.colors.text,
    fontWeight: theme.typography.semibold,
  },
  rainInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  rainText: {
    fontSize: theme.typography.xs,
    color: theme.colors.secondary,
  },
});

export default HourlyForecast; 