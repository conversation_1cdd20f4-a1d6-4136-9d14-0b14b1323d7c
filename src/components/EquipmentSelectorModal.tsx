import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  TextInput,
} from 'react-native';
import Icon from './Icon';
import BottomSheetModal from './BottomSheetModal';
import { theme } from '../theme/index';
// Mock JSON dosyası kaldırıldı - API'den gelecek

interface EquipmentSelectorModalProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (equipment: string[]) => void;
  selectedEquipment: string[];
  navigation: any; // For navigation to add equipment screen
}

interface UserEquipment {
  id: string;
  name: string;
  category: string;
  brand: string;
}

const EquipmentSelectorModal: React.FC<EquipmentSelectorModalProps> = ({
  visible,
  onClose,
  onSelect,
  selectedEquipment,
  navigation,
}) => {
  const [searchText, setSearchText] = useState('');
  const [filteredEquipment, setFilteredEquipment] = useState<UserEquipment[]>([]);
  const [tempSelectedEquipment, setTempSelectedEquipment] = useState<string[]>([]);

  // Mock data kaldırıldı - API'den gelecek
  const userEquipment: UserEquipment[] = [];

  useEffect(() => {
    if (visible) {
      setTempSelectedEquipment(selectedEquipment);
      setSearchText('');
      
      // Kullanıcının ekipmanı yoksa ekipman ekleme ekranına yönlendir
      if (userEquipment.length === 0) {
        handleNoEquipment();
        return;
      }
      
      setFilteredEquipment(userEquipment);
    }
  }, [visible, selectedEquipment]);

  useEffect(() => {
    if (searchText.trim() === '') {
      setFilteredEquipment(userEquipment);
    } else {
      const filtered = userEquipment.filter(equipment =>
        equipment.name.toLowerCase().includes(searchText.toLowerCase()) ||
        equipment.category.toLowerCase().includes(searchText.toLowerCase()) ||
        equipment.brand.toLowerCase().includes(searchText.toLowerCase())
      );
      setFilteredEquipment(filtered);
    }
  }, [searchText]);

  const handleNoEquipment = () => {
    onClose();
    // Kısa bir delay ile profildeki ekipman ekleme ekranına yönlendir
    setTimeout(() => {
      navigation.navigate('AddGear');
    }, 300);
  };

  const handleToggleSelection = (equipmentName: string) => {
    setTempSelectedEquipment(prev => {
      if (prev.includes(equipmentName)) {
        return prev.filter(item => item !== equipmentName);
      } else {
        return [...prev, equipmentName];
      }
    });
  };

  const handleConfirm = () => {
    onSelect(tempSelectedEquipment);
    onClose();
  };

  const renderEquipmentItem = ({ item }: { item: UserEquipment }) => {
    const isSelected = tempSelectedEquipment.includes(item.name);
    
    return (
      <TouchableOpacity
        style={[
          styles.equipmentItem,
          isSelected && styles.selectedEquipmentItem
        ]}
        onPress={() => handleToggleSelection(item.name)}
        activeOpacity={0.7}
      >
        <View style={styles.equipmentContent}>
          <View style={styles.equipmentIcon}>
            <Icon name="package" size={20} color={theme.colors.primary} />
          </View>
          <View style={styles.equipmentInfo}>
            <Text style={[
              styles.equipmentName,
              isSelected && styles.selectedEquipmentName
            ]}>
              {item.name}
            </Text>
            <Text style={styles.equipmentDetails}>
              {item.category} • {item.brand}
            </Text>
          </View>
        </View>
        <View style={[
          styles.checkbox,
          isSelected && styles.checkboxSelected
        ]}>
          {isSelected && (
            <Icon name="check" size={16} color="#FFFFFF" />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  // Kullanıcının ekipmanı yoksa gösterilecek ekran
  if (userEquipment.length === 0) {
    return (
      <BottomSheetModal
        visible={visible}
        onClose={onClose}
        maxHeight="50%"
      >
        <View style={styles.noEquipmentContainer}>
          <View style={styles.noEquipmentIcon}>
            <Icon name="package" size={48} color={theme.colors.textSecondary} />
          </View>
          <Text style={styles.noEquipmentTitle}>Henüz Ekipmanınız Yok</Text>
          <Text style={styles.noEquipmentSubtitle}>
            Önce profil bölümünden ekipmanlarınızı ekleyin, sonra burada seçebilirsiniz.
          </Text>
          
          <TouchableOpacity
            style={styles.addEquipmentButton}
            onPress={handleNoEquipment}
            activeOpacity={0.7}
          >
            <Icon name="plus" size={20} color="#FFFFFF" />
            <Text style={styles.addEquipmentButtonText}>Ekipman Ekle</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={onClose}
            activeOpacity={0.7}
          >
            <Text style={styles.cancelText}>İptal</Text>
          </TouchableOpacity>
        </View>
      </BottomSheetModal>
    );
  }

  return (
    <BottomSheetModal
      visible={visible}
      onClose={onClose}
      maxHeight="80%"
    >
      <Text style={styles.title}>Ekipman Seç</Text>
      <Text style={styles.subtitle}>
        Kullandığınız ekipmanları seçin ({tempSelectedEquipment.length} seçili)
      </Text>

      {/* Arama */}
      <View style={styles.searchContainer}>
        <Icon name="search" size={16} color={theme.colors.textSecondary} />
        <TextInput
          style={styles.searchInput}
          value={searchText}
          onChangeText={setSearchText}
          placeholder="Ekipman ara..."
          placeholderTextColor={theme.colors.textSecondary}
        />
        {searchText.length > 0 && (
          <TouchableOpacity onPress={() => setSearchText('')}>
            <Icon name="x" size={16} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        )}
      </View>

      {/* Ekipman Listesi */}
      <View style={styles.listContainer}>
        {filteredEquipment.length > 0 ? (
          <FlatList
            data={filteredEquipment}
            renderItem={renderEquipmentItem}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            style={styles.equipmentList}
          />
        ) : (
          <View style={styles.noResultsContainer}>
            <Icon name="search" size={32} color={theme.colors.textSecondary} />
            <Text style={styles.noResultsText}>Ekipman bulunamadı</Text>
            <Text style={styles.noResultsSubtext}>
              Farklı anahtar kelimeler deneyin
            </Text>
          </View>
        )}
      </View>

      {/* Alt Butonlar */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.addNewButton}
          onPress={handleNoEquipment}
          activeOpacity={0.7}
        >
          <Icon name="plus" size={16} color={theme.colors.primary} />
          <Text style={styles.addNewButtonText}>Yeni Ekipman Ekle</Text>
        </TouchableOpacity>
        
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={onClose}
            activeOpacity={0.7}
          >
            <Text style={styles.cancelText}>İptal</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.confirmButton,
              tempSelectedEquipment.length === 0 && styles.confirmButtonDisabled
            ]}
            onPress={handleConfirm}
            activeOpacity={0.7}
            disabled={tempSelectedEquipment.length === 0}
          >
            <Text style={[
              styles.confirmButtonText,
              tempSelectedEquipment.length === 0 && styles.confirmButtonTextDisabled
            ]}>
              Seç ({tempSelectedEquipment.length})
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </BottomSheetModal>
  );
};

const styles = StyleSheet.create({
  title: {
    fontSize: theme.typography.xl,
    fontWeight: theme.typography.bold,
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    fontSize: theme.typography.base,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    paddingHorizontal: theme.spacing.sm,
    marginBottom: theme.spacing.lg,
    gap: theme.spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: theme.typography.base,
    color: theme.colors.text,
    paddingVertical: theme.spacing.sm,
  },
  listContainer: {
    flex: 1,
    maxHeight: 300,
  },
  equipmentList: {
    flex: 1,
  },
  equipmentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    marginBottom: theme.spacing.sm,
  },
  selectedEquipmentItem: {
    backgroundColor: `${theme.colors.primary}15`,
    borderColor: theme.colors.primary,
  },
  equipmentContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  equipmentIcon: {
    width: 36,
    height: 36,
    borderRadius: theme.borderRadius.sm,
    backgroundColor: `${theme.colors.primary}15`,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.sm,
  },
  equipmentInfo: {
    flex: 1,
  },
  equipmentName: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
    marginBottom: 2,
  },
  selectedEquipmentName: {
    color: theme.colors.primary,
  },
  equipmentDetails: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: theme.colors.border,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxSelected: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  noResultsContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.xl,
  },
  noResultsText: {
    fontSize: theme.typography.lg,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.xs,
  },
  noResultsSubtext: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  // No Equipment States
  noEquipmentContainer: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
  },
  noEquipmentIcon: {
    marginBottom: theme.spacing.lg,
  },
  noEquipmentTitle: {
    fontSize: theme.typography.xl,
    color: theme.colors.text,
    fontWeight: theme.typography.bold,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  noEquipmentSubtitle: {
    fontSize: theme.typography.base,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
    lineHeight: 22,
  },
  addEquipmentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    marginBottom: theme.spacing.md,
    gap: theme.spacing.sm,
  },
  addEquipmentButtonText: {
    fontSize: theme.typography.base,
    color: '#FFFFFF',
    fontWeight: theme.typography.medium,
  },
  // Buttons
  buttonContainer: {
    marginTop: theme.spacing.lg,
    gap: theme.spacing.md,
  },
  addNewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.primary,
    paddingVertical: theme.spacing.sm,
    gap: theme.spacing.xs,
  },
  addNewButtonText: {
    fontSize: theme.typography.sm,
    color: theme.colors.primary,
    fontWeight: theme.typography.medium,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    paddingVertical: theme.spacing.md,
    alignItems: 'center',
  },
  cancelText: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
  },
  confirmButton: {
    flex: 1,
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.md,
    paddingVertical: theme.spacing.md,
    alignItems: 'center',
  },
  confirmButtonDisabled: {
    backgroundColor: theme.colors.border,
  },
  confirmButtonText: {
    fontSize: theme.typography.base,
    color: '#FFFFFF',
    fontWeight: theme.typography.medium,
  },
  confirmButtonTextDisabled: {
    color: theme.colors.textSecondary,
  },
});

export default EquipmentSelectorModal; 