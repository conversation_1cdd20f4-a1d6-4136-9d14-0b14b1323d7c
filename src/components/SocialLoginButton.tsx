import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { theme } from '../theme';
import Icon from 'react-native-vector-icons/AntDesign';

interface SocialLoginButtonProps {
  provider: 'google' | 'facebook';
  onPress: () => void;
  loading?: boolean;
  title?: string;
}

const SocialLoginButton: React.FC<SocialLoginButtonProps> = ({
  provider,
  onPress,
  loading = false,
  title,
}) => {
  const isGoogle = provider === 'google';
  const isFacebook = provider === 'facebook';

  const getButtonConfig = () => {
    if (isGoogle) {
      return {
        icon: 'google' as const,
        text: title || 'Google ile Giriş Yap',
        colors: ['#DB4437', '#DC4C3E'],
        iconColor: '#FFFFFF',
      };
    } else {
      return {
        icon: 'facebook-square' as const,
        text: title || 'Facebook ile Giriş <PERSON>',
        colors: ['#4267B2', '#365899'],
        iconColor: '#FFFFFF',
      };
    }
  };

  const config = getButtonConfig();

  return (
    <TouchableOpacity
      style={[styles.container, { opacity: loading ? 0.7 : 1 }]}
      onPress={onPress}
      disabled={loading}
      activeOpacity={0.8}
    >
      <LinearGradient
        colors={config.colors as [string, string]}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
      >
        <View style={styles.content}>
          <Icon
            name={config.icon}
            size={24}
            color={config.iconColor}
            style={styles.icon}
          />
          <Text style={styles.text}>{config.text}</Text>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 56,
    borderRadius: theme.borderRadius.md,
    overflow: 'hidden',
    ...theme.shadows.md,
  },
  gradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  icon: {
    marginRight: theme.spacing.sm,
  },
  text: {
    color: '#FFFFFF',
    fontSize: theme.typography.base,
    fontWeight: theme.typography.semibold,
  },
});

export default SocialLoginButton; 