import React from 'react';
import { View } from 'react-native';
import Svg, { Path } from 'react-native-svg';

interface CrownIconProps {
  size?: number;
}

const CrownIcon: React.FC<CrownIconProps> = ({ size = 16 }) => {
  return (
    <View style={{
      width: size,
      height: size,
      alignItems: 'center',
      justifyContent: 'center',
    }}>
      <Svg width={size} height={size} viewBox="0 0 708.18 801.6">
        {/* Hexagon Background */}
        <Path
          d="M682.89,619.83l-305.9,175.38c-14.94,8.56-33.31,8.51-48.19-.15L23.97,617.84C9.08,609.19-.05,593.25,0,576.03L1.07,223.42c.05-17.22,9.28-33.1,24.22-41.66L331.19,6.38c14.94-8.56,33.31-8.51,48.19.15l304.83,177.23c14.88,8.65,24.02,24.59,23.97,41.81l-1.07,352.61c-.05,17.22-9.28,33.1-24.22,41.66Z"
          fill="#f4b400"
        />
        
        {/* Crown */}
        <Path
          d="M576.7,279.78c-4.19-2.84-8.53-1.92-12.67.69-8.83,5.55-17.7,11.02-26.57,16.49-22.89,14.14-45.77,28.3-68.69,42.39-6.22,3.82-10.41,2.99-15.05-2.72-8.26-10.16-16.53-20.32-24.74-30.52-21.71-27-43.36-54.05-65.08-81.03-5.75-7.15-14.07-7.05-19.79.06-29.89,37.23-59.78,74.46-89.66,111.69-4.93,6.14-8.55,6.86-15.22,2.68-31.76-19.88-63.52-39.77-95.23-59.73-4.23-2.66-8.36-3.41-12.61-.52-4.32,2.94-4.83,7.32-3.99,12.05,1.17,6.66,2.34,13.31,3.55,19.96,7.18,39.63,14.39,79.25,21.56,118.87,3.98,22.03,7.86,44.08,11.87,66.11,1.67,9.15,5.46,12.16,14.74,12.24,110.07,1.04,220.14,0,330.22,0,4.65,0,11.69.02,18.73,0,9.69-.03,13.36-3.13,15.13-12.75,4.7-25.62,9.36-51.25,14.09-76.87,7.79-42.24,15.58-84.47,23.46-126.69.94-5.02.32-9.45-4.04-12.4Z"
          fill="#fff"
        />
        <Path
          d="M535.1,531.84c-2.79-2.41-6.13-2.53-9.56-2.53-114.12.03-228.24.01-342.36.01-1.69,0-3.39-.06-5.07.07-5.85.48-9.61,4.34-9.67,10.22-.1,10.42-.08,20.85,0,31.28.05,5.53,2.91,8.92,8.23,10.25,2.49.62,5.01.69,7.55.69,112.85,0,225.7,0,338.56-.03,2.1,0,4.24-.1,6.3-.5,6.48-1.26,9.77-5.17,9.84-11.78.1-9.72,0-19.44.06-29.16.02-3.5-1.28-6.26-3.89-8.52Z"
          fill="#fff"
        />
      </Svg>
    </View>
  );
};

export default CrownIcon; 