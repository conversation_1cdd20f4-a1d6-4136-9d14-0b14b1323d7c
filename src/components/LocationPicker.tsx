import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
} from 'react-native';
import Mapbox from '@rnmapbox/maps';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from './Icon';
import Button from './Button';
import { theme } from '../theme/index';

// TypeScript tipleri - Mapbox React Native için
interface MapCenter {
  lat: number;
  lng: number;
}

interface LocationData {
  lat: number;
  lng: number;
  locationString: string;
}

interface LocationPickerProps {
  visible: boolean;
  onClose: () => void;
  onLocationSelect: (location: LocationData) => void;
  initialLocation?: MapCenter;
}

const LocationPicker: React.FC<LocationPickerProps> = ({
  visible,
  onClose,
  onLocationSelect,
  initialLocation = { lat: 41.0082, lng: 28.9784 }
}) => {
  const [mapCenter, setMapCenter] = useState<MapCenter>(initialLocation);
  const [currentZoom, setCurrentZoom] = useState<number>(10);

  // Mapbox dokümantasyonuna göre real-time coordinate update
  // Mouse position örneğindeki gibi sürekli koordinat takibi
  const handleMapRegionChange = (event: any) => {
    // Mapbox React Native'de coordinates geometry.coordinates içinde gelir
    // Bu, Mapbox GL JS'deki map.getCenter() ile aynı mantığı kullanır
    if (event?.geometry?.coordinates) {
      const [lng, lat] = event.geometry.coordinates;
      setMapCenter({ lat, lng });
    }
  };

  const handleLocationConfirm = (): void => {
    const locationString: string = `${mapCenter.lat.toFixed(6)}°K, ${mapCenter.lng.toFixed(6)}°D`;
    onLocationSelect({
      lat: mapCenter.lat,
      lng: mapCenter.lng,
      locationString
    });
    onClose();
  };

  const handleZoomIn = (): void => {
    if (currentZoom < 18) {
      setCurrentZoom(prev => prev + 1);
    }
  };

  const handleZoomOut = (): void => {
    if (currentZoom > 1) {
      setCurrentZoom(prev => prev - 1);
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={false}
      animationType="slide"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <Button
            variant="ghost"
            size="sm"
            icon="arrow-left"
            onPress={onClose}
            style={{ width: 40, height: 40, paddingHorizontal: 0, backgroundColor: theme.colors.surfaceVariant }}
          />
          <Text style={styles.headerTitle}>Konum Seç</Text>
          <View style={{ width: 40 }} />
        </View>

        {/* Map Container */}
        <View style={styles.mapView}>
          <Mapbox.MapView
            style={styles.mapView}
            styleURL="mapbox://styles/mapbox/streets-v12"
            onRegionIsChanging={handleMapRegionChange}
            zoomEnabled={true}
            scrollEnabled={true}
            pitchEnabled={true}
            rotateEnabled={true}
            logoEnabled={false}
            attributionEnabled={false}
            scaleBarEnabled={false}
            compassEnabled={false}
          >
            <Mapbox.Camera
              zoomLevel={currentZoom}
              centerCoordinate={[mapCenter.lng, mapCenter.lat]}
              animationDuration={800}
            />
          </Mapbox.MapView>
          
          {/* Sniper Crosshair */}
          <View style={styles.crosshair}>
            <View style={styles.crosshairHorizontal} />
            <View style={styles.crosshairVertical} />
            <View style={styles.crosshairCenter} />
          </View>
          
          {/* Current Location Info */}
          <View style={styles.locationInfo}>
            <Text style={styles.locationText}>
              {mapCenter.lat.toFixed(6)}°K, {mapCenter.lng.toFixed(6)}°D
            </Text>
          </View>

          {/* FAB Style Controls */}
          
          {/* Compass */}
          <View style={styles.fabCompass}>
            <Icon name="compass" size={18} color={theme.colors.text} />
          </View>

          {/* Zoom Controls */}
          <View style={styles.fabZoomContainer}>
            <TouchableOpacity 
              style={styles.fabZoomButton}
              onPress={handleZoomIn}
            >
              <Icon name="plus" size={18} color={theme.colors.text} />
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.fabZoomButton}
              onPress={handleZoomOut}
            >
              <Icon name="minus" size={18} color={theme.colors.text} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Bottom Controls */}
        <View style={styles.bottomControls}>
          <TouchableOpacity 
            style={styles.cancelButton} 
            onPress={onClose}
          >
            <Text style={styles.cancelButtonText}>İptal</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.confirmButton} 
            onPress={handleLocationConfirm}
          >
            <Text style={styles.confirmButtonText}>Konumu Seç</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    backgroundColor: theme.colors.surface,
  },
  headerTitle: {
    fontSize: theme.typography.xl,
    color: theme.colors.text,
    fontWeight: theme.typography.bold,
  },
  mapView: {
    flex: 1,
  },
  // Sniper Crosshair
  crosshair: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    width: 60,
    height: 60,
    marginTop: -30,
    marginLeft: -30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  crosshairHorizontal: {
    position: 'absolute',
    width: 60,
    height: 2,
    backgroundColor: '#FF6B6B',
    borderRadius: 1,
  },
  crosshairVertical: {
    position: 'absolute',
    width: 2,
    height: 60,
    backgroundColor: '#FF6B6B',
    borderRadius: 1,
  },
  crosshairCenter: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#FF6B6B',
    backgroundColor: 'transparent',
  },
  // Location Info
  locationInfo: {
    position: 'absolute',
    top: theme.spacing.lg,
    left: theme.spacing.md,
    right: theme.spacing.md,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    alignItems: 'center',
  },
  locationText: {
    fontSize: theme.typography.sm,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
  },
  // FAB Style Controls (MapScreen benzeri)
  fabCompass: {
    position: 'absolute',
    top: 100, // Location info'nun altında
    right: 16,
    width: 40,
    height: 40,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  fabZoomContainer: {
    position: 'absolute',
    bottom: 120, // Bottom controls'un üstünde
    right: 16,
    flexDirection: 'column',
    gap: 8,
  },
  fabZoomButton: {
    width: 40,
    height: 40,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  // Bottom Controls
  bottomControls: {
    position: 'absolute',
    bottom: theme.spacing.lg,
    left: theme.spacing.md,
    right: theme.spacing.md,
    flexDirection: 'row',
    gap: theme.spacing.sm,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    paddingVertical: theme.spacing.md,
    alignItems: 'center',
  },
  confirmButton: {
    flex: 1,
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.md,
    paddingVertical: theme.spacing.md,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.semibold,
  },
  confirmButtonText: {
    fontSize: theme.typography.base,
    color: theme.colors.background,
    fontWeight: theme.typography.semibold,
  },
});

export default LocationPicker; 