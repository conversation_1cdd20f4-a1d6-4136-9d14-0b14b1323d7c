import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import MapComponent from '../components/MapComponent';
import { ScreenContainer } from '../components';
import { theme } from '../theme';

const MapScreen: React.FC = () => {
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <ScreenContainer paddingHorizontal="none" paddingVertical="none">
        <MapComponent
          showCrosshair={true}
          showCoordinates={true}
          showLocationButton={true}
          showLayerSelector={true}
          show3DToggle={true}
        />
      </ScreenContainer>
    </SafeAreaView>
  );
};

export default MapScreen;