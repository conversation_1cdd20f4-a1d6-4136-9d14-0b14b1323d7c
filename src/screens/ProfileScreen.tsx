import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Alert,
  Image,
} from 'react-native';
import { apiService } from '../services/api';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from "../components/Icon";
import EquipmentCard from '../components/EquipmentCard';
import EquipmentSection from '../components/EquipmentSection';
import AddButton from '../components/AddButton';
import Button from '../components/Button';
import TabSelector from '../components/TabSelector';
import PostsGrid from '../components/PostsGrid';
import LocationCard from '../components/LocationCard';
import ProBadge from '../components/ProBadge';
import DefaultAvatar from '../components/DefaultAvatar';
import UserDisplayName from '../components/UserDisplayName';
import ProfileHeader from '../components/ProfileHeader';
import UserProfileLayout from '../components/UserProfileLayout';
import AppHeader from '../components/AppHeader';
import { theme } from '../theme';
import { ScreenContainer } from '../components';
import { useAuth } from '../contexts/AuthContext';
import ConfirmModal from '../components/ConfirmModal';
import { useUnits } from '../hooks/useUnits';

// Koordinat dönüştürme fonksiyonu
const getCoordinatesForLocation = (location: string): [number, number] => {
  const locationCoordinates: { [key: string]: [number, number] } = {
    'Boğaziçi': [28.9784, 41.0082],
    'Galata Köprüsü': [28.9744, 41.0199],
    'Kadıköy İskelesi': [29.0158, 40.9833],
    'Büyükada': [29.1189, 40.8606],
    'İstanbul': [28.9784, 41.0082],
  };
  return locationCoordinates[location] || [29.0100, 41.0082];
};

interface UserStats {
  totalCatches: number;
  totalSpots: number;
  followers: number;
  following: number;
}

interface FishingGear {
  id: string;
  name: string;
  category: string;
  brand?: string;
  icon: string;
  imageUrl?: string;
  condition: 'excellent' | 'good' | 'fair';
}

interface RecentCatch {
  id: string;
  species: string;
  weight: {
    value: number;
    unit: string;
    originalUnit: string;
    displayValue: string;
  };
  length?: {
    value: number;
    unit: string;
    originalUnit: string;
    displayValue: string;
  };
  date: string;
  location: string;
  photo?: string;
  imageUrl?: string;
  equipmentDetails?: Array<{
    id: string;
    name: string;
    category: string;
    brand?: string;
    icon: string;
    condition: 'excellent' | 'good' | 'fair';
  }>;
}

// Kullanıcı ID'si
const currentUserId = '550e8400-e29b-41d4-a716-446655440000'; // Test user ID

interface ProfileScreenProps {
  navigation: any;
}

const ProfileScreen: React.FC<ProfileScreenProps> = ({ navigation }) => {
  const { user } = useAuth();
  const { convertAndFormat } = useUnits();
  const [activeTab, setActiveTab] = useState<'catches' | 'gear' | 'spots'>('catches');
  const [showDeleteGearConfirm, setShowDeleteGearConfirm] = useState(false);
  const [gearToDelete, setGearToDelete] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [recentCatches, setRecentCatches] = useState<RecentCatch[]>([]);
  const [gearItems, setGearItems] = useState<FishingGear[]>([]);

  // Kullanıcı profil bilgileri
  const [userProfile, setUserProfile] = useState({
    name: 'Ahmet Yılmaz',
    location: 'İstanbul, Türkiye',
    bio: '"Boğaziçi\'nin en güzel balıkçılık noktalarını keşfediyorum"',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    isPro: true,
    proSince: '2024'
  });

  // API'den kullanıcı verilerini çek
  const loadUserData = async () => {
    try {
      setLoading(true);
      // Kullanıcının postlarını çek
      const userPosts = await apiService.getPosts(1, 10, { userId: currentUserId });
      
      // API verisini mevcut format'a çevir
      const formattedCatches = userPosts.items.map((post: any) => ({
        id: post.id.toString(),
        species: post.catch_details?.species_name || 'Bilinmeyen Balık',
        weight: {
          value: post.catch_details?.weight || 0,
          unit: 'kg',
          originalUnit: 'kg',
          displayValue: `${post.catch_details?.weight || 0} kg`
        },
        length: {
          value: post.catch_details?.length || 0,
          unit: 'cm', 
          originalUnit: 'cm',
          displayValue: `${post.catch_details?.length || 0} cm`
        },
        date: new Date(post.created_at).toLocaleDateString('tr-TR'),
        location: 'İstanbul',
        photo: post.image_url || 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400&h=300&fit=crop'
      }));
      
      setRecentCatches(formattedCatches.slice(0, 3));
    } catch (error) {
      console.error('Kullanıcı verileri yüklenirken hata:', error);
      // Hata durumunda boş array
      setRecentCatches([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUserData();
  }, []);
  
  const tabs = [
    { id: 'catches', label: 'Avlar', icon: 'fish' },
    { id: 'gear', label: 'Ekipmanlar', icon: 'backpack' },
    { id: 'spots', label: 'Spotlar', icon: 'anchor' },
  ];

  // Gerçek data API'den gelecek
  const userSpots: any[] = [];
  const favoriteSpots: any[] = [];
  const totalSpots = 0;
  const privateSpots = 0;

  const handleSelectSpot = (location: any) => {
    navigation.navigate('MainTabs', { 
      screen: 'Map',
      params: {
        selectedLocation: {
          id: location.id,
          name: location.name,
          coordinates: location.coordinates,
          address: location.address,
          type: location.type
        }
      }
    });
  };

  const handleDeleteGear = (gearId: string) => {
    setGearToDelete(gearId);
    setShowDeleteGearConfirm(true);
  };

  const confirmDeleteGear = () => {
    if (gearToDelete) {
      setGearItems(prev => prev.filter(item => item.id !== gearToDelete));
      setGearToDelete(null);
    }
    setShowDeleteGearConfirm(false);
  };



  const renderTabContent = () => {
    switch (activeTab) {
      case 'gear':
        return (
          <EquipmentSection
            equipment={gearItems}
            title="Ekipmanlar"
            showAddButton={true}
            onAddPress={() => navigation.navigate('AddGear')}
            onDeleteGear={handleDeleteGear}
          />
        );

      case 'spots':
        return (
          <View style={styles.tabContent}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Spotlar</Text>
              <AddButton onPress={() => navigation.navigate('AddSpot')} />
            </View>
            
            <View style={styles.spotStats}>
              <View style={styles.statItem}>
                <Icon name="anchor" size={16} color={theme.colors.primary} />
                <Text style={styles.spotStatNumber}>{totalSpots}</Text>
                <Text style={styles.spotStatLabel}>Toplam Spot</Text>
              </View>
              <View style={styles.statItem}>
                <Icon name="shield" size={16} color={theme.colors.accent} />
                <Text style={styles.spotStatNumber}>{privateSpots}</Text>
                <Text style={styles.spotStatLabel}>Özel Spot</Text>
              </View>
              <View style={styles.statItem}>
                <Icon name="heart" size={16} color={theme.colors.error} />
                <Text style={styles.spotStatNumber}>{favoriteSpots.length}</Text>
                <Text style={styles.spotStatLabel}>Favori</Text>
              </View>
            </View>

            <TouchableOpacity 
              style={styles.viewAllButton}
              onPress={() => navigation.navigate('LocationManagement')}
            >
              <Text style={styles.viewAllText}>Tüm Spotları Görüntüle</Text>
              <Icon name="chevron-right" size={16} color={theme.colors.primary} />
            </TouchableOpacity>
          </View>
        );

      default:
        return (
          <View style={styles.tabContent}>
            {recentCatches.length > 0 ? (
              <PostsGrid
                data={recentCatches}
                onPostPress={(post) => {
                  const postData = {
                    id: post.id,
                    user: {
                      name: userProfile.name,
                      avatar: userProfile.avatar,
                      location: userProfile.location,
                    },
                    fish: {
                      species: post.species,
                      weight: convertAndFormat(post.weight.value, 'weight'),
                      length: post.length ? convertAndFormat(post.length.value, 'length') : '45cm',
                    },
                    photo: post.photo,
                    likes: 25,
                    comments: 8,
                    timeAgo: post.date,
                    description: `${post.location} spot'unda ${post.species} yakaladım!`,
                    equipmentDetails: post.equipmentDetails,
                    catchLocation: post.location,
                    coordinates: getCoordinatesForLocation(post.location),
                  };
                  navigation.navigate('PostDetail', { postData });
                }}
                noPadding={true}
              />
            ) : (
              <View style={styles.emptyState}>
                <Icon name="fish" size={48} color={theme.colors.textSecondary} />
                <Text style={styles.emptyStateText}>Henüz av kaydınız yok</Text>
                <Text style={styles.emptyStateSubtext}>İlk avınızı kaydetmek için + butonuna dokunun</Text>
              </View>
            )}
          </View>
        );
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        title="Profil"
        rightButtons={[
          {
            icon: "bell",
            onPress: () => navigation.navigate('Notifications')
          },
          {
            icon: "settings",
            onPress: () => navigation.navigate('Settings')
          }
        ]}
      />

      <ScreenContainer>
        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Profile Section */}
          <UserProfileLayout
            userData={{
              name: userProfile.name,
              location: userProfile.location,
              bio: userProfile.bio,
              avatar: userProfile.avatar,
              catchCount: recentCatches.length,
              followers: 156,
              following: 89,
              isPro: userProfile.isPro,
            }}
            isOwnProfile={true}
            onPrimaryAction={() => navigation.navigate('EditProfile')}
            onShareAction={() => {
          
            }}
            onProPress={() => navigation.navigate('ProSettings')}
            noPadding={true}
          />

          {/* Haritam Section */}
          <View style={styles.quickActionsSection}>
            <TouchableOpacity 
              style={styles.yourMapCard}
              onPress={() => navigation.navigate('YourMap')}
            >
              <View style={styles.yourMapIcon}>
                <Icon name="navigation" size={24} color={theme.colors.primary} />
              </View>
              <View style={styles.yourMapContent}>
                <Text style={styles.yourMapTitle}>Haritam</Text>
                <Text style={styles.yourMapSubtitle}>
                  Favori spotlar, gizli noktalar ve av geçmişiniz
                </Text>
              </View>
              <Icon name="chevron-right" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>

          {/* Tab Navigation */}
          <TabSelector
            tabs={tabs}
            activeTab={activeTab}
            onTabPress={(tabId) => setActiveTab(tabId as 'catches' | 'gear' | 'spots')}
          />

          {/* Tab Content */}
          {renderTabContent()}

          {/* Bottom Padding */}
          <View style={{ height: theme.spacing.xl }} />
        </ScrollView>
      </ScreenContainer>

      <ConfirmModal
        visible={showDeleteGearConfirm}
        title="Ekipmanı Sil"
        message="Bu ekipmanı silmek istediğinizden emin misiniz?"
        onConfirm={confirmDeleteGear}
        onCancel={() => {
          setShowDeleteGearConfirm(false);
          setGearToDelete(null);
        }}
        confirmText="Sil"
        cancelText="İptal"
        type="destructive"
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },

  userCard: {
    backgroundColor: theme.colors.surface,
    marginBottom: theme.spacing.md,
    borderRadius: theme.borderRadius.xl,
    padding: theme.spacing.xl,
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: theme.spacing.md,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: theme.colors.surfaceVariant,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 4,
    borderColor: theme.colors.primary,
  },
  avatarText: {
    fontSize: 40,
  },
  onlineBadge: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
  },
  onlineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: theme.colors.success,
  },
  userName: {
    fontSize: theme.typography.xl,
    fontWeight: theme.typography.bold,
    color: theme.colors.text,
    textAlign: 'center',
  },

  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  userLocation: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
  },
  userBio: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: theme.spacing.lg,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  proStatusBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: theme.spacing.xs,
    marginTop: theme.spacing.md,
    padding: theme.spacing.sm,
    backgroundColor: `${theme.colors.accent}10`,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: `${theme.colors.accent}20`,
  },
  proStatusText: {
    fontSize: theme.typography.sm,
    color: theme.colors.accent,
    fontWeight: theme.typography.medium,
  },

  statsContainer: {
    marginBottom: theme.spacing.md,
    gap: theme.spacing.md,
  },
  statsRow: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  statCard: {
    flex: 1,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: theme.typography.xl,
    color: theme.colors.text,
    fontWeight: theme.typography.bold,
    marginBottom: theme.spacing.xs,
  },
  statLabel: {
    fontSize: theme.typography.xs,
    color: theme.colors.textSecondary,
    fontWeight: theme.typography.medium,
    textAlign: 'center',
  },

  tabContent: {
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
    paddingHorizontal: theme.spacing.xs,
  },
  sectionTitle: {
    fontSize: theme.typography.lg,
    color: theme.colors.text,
    fontWeight: theme.typography.semibold,
  },
  seeAll: {
    fontSize: theme.typography.sm,
    color: theme.colors.primary,
    fontWeight: theme.typography.medium,
  },

  catchCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    gap: theme.spacing.md,
  },
  catchPhoto: {
    width: 50,
    height: 50,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.surfaceVariant,
    alignItems: 'center',
    justifyContent: 'center',
  },
  catchPhotoText: {
    fontSize: 20,
  },
  catchInfo: {
    flex: 1,
  },
  catchSpecies: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
    marginBottom: 2,
  },
  catchWeight: {
    fontSize: theme.typography.sm,
    color: theme.colors.primary,
    fontWeight: theme.typography.medium,
    marginBottom: 2,
  },
  catchLocation: {
    fontSize: theme.typography.xs,
    color: theme.colors.textSecondary,
  },
  catchDate: {
    fontSize: theme.typography.xs,
    color: theme.colors.textTertiary,
  },

  emptyContainer: {
    padding: theme.spacing.xl,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 200,
  },
  emptyText: {
    fontSize: theme.typography.base,
    color: theme.colors.textSecondary,
    fontWeight: theme.typography.medium,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: theme.spacing.xs,
  },
  emptyState: {
    padding: theme.spacing.xl,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 200,
  },
  emptyStateText: {
    fontSize: theme.typography.base,
    color: theme.colors.textSecondary,
    fontWeight: theme.typography.medium,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
    marginTop: theme.spacing.md,
  },
  emptyStateSubtext: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: theme.spacing.xs,
  },

  // Spot Stats Styles
  spotStats: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    gap: theme.spacing.sm,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
    padding: theme.spacing.sm,
  },
  spotStatNumber: {
    fontSize: theme.typography.lg,
    fontWeight: theme.typography.bold,
    color: theme.colors.text,
    marginTop: theme.spacing.xs,
  },
  spotStatLabel: {
    fontSize: theme.typography.xs,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  // Recent Spots Styles
  recentSpots: {
    gap: theme.spacing.sm,
    marginBottom: theme.spacing.md,
  },
  recentSpotItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    gap: theme.spacing.md,
  },
  spotIcon: {
    width: 40,
    height: 40,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.surfaceVariant,
    alignItems: 'center',
    justifyContent: 'center',
  },
  spotInfo: {
    flex: 1,
  },
  spotName: {
    fontSize: theme.typography.base,
    fontWeight: theme.typography.medium,
    color: theme.colors.text,
    marginBottom: 2,
  },
  spotLocation: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
  },
  spotAction: {
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    borderWidth: 1,
    borderColor: theme.colors.primary,
    gap: theme.spacing.xs,
  },
  viewAllText: {
    fontSize: theme.typography.base,
    fontWeight: theme.typography.medium,
    color: theme.colors.primary,
  },
  favoriteSpotsSection: {
    marginBottom: theme.spacing.md,
  },
  sectionSubtitle: {
    fontSize: theme.typography.base,
    fontWeight: theme.typography.medium,
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  spotsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.sm,
  },

  // YourMap Section Styles
  quickActionsSection: {
    marginBottom: theme.spacing.lg,
  },
  yourMapCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
    gap: theme.spacing.md,
  },
  yourMapIcon: {
    width: 48,
    height: 48,
    borderRadius: theme.borderRadius.md,
    backgroundColor: `${theme.colors.primary}10`,
    alignItems: 'center',
    justifyContent: 'center',
  },
  yourMapContent: {
    flex: 1,
  },
  yourMapTitle: {
    fontSize: theme.typography.base,
    fontWeight: theme.typography.semibold,
    color: theme.colors.text,
    marginBottom: 2,
  },
  yourMapSubtitle: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    lineHeight: 18,
  },

});

export default ProfileScreen; 