import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  Dimensions,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import { theme } from '../theme';
import { ScreenContainer } from '../components';
import SuccessModal from '../components/SuccessModal';
import SocialLoginButton from '../components/SocialLoginButton';
import { AuthService, UserProfile } from '../services/authService';
import { useAuth } from '../contexts/AuthContext';
import Icon from '../components/Icon';

const { width, height } = Dimensions.get('window');

const AuthScreen: React.FC = () => {
  const { login } = useAuth();
  const [loading, setLoading] = useState<{
    google: boolean;
    facebook: boolean;
  }>({ google: false, facebook: false });
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [modalMessage, setModalMessage] = useState('');

  const handleGoogleLogin = async () => {
    try {
      setLoading(prev => ({ ...prev, google: true }));
      
      const userProfile = await AuthService.signInWithGoogle();
      
      if (userProfile) {
        await login(userProfile);
        setModalMessage(`Hoş geldin ${userProfile.name}!`);
        setShowSuccessModal(true);
      } else {
        setModalMessage('Google ile giriş yapılamadı. Lütfen tekrar deneyin.');
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error('Google login error:', error);
      setModalMessage('Bir hata oluştu. Lütfen tekrar deneyin.');
      setShowErrorModal(true);
    } finally {
      setLoading(prev => ({ ...prev, google: false }));
    }
  };

  const handleFacebookLogin = async () => {
    try {
      setLoading(prev => ({ ...prev, facebook: true }));
      
      const userProfile = await AuthService.signInWithFacebook();
      
      if (userProfile) {
        await login(userProfile);
        setModalMessage(`Hoş geldin ${userProfile.name}!`);
        setShowSuccessModal(true);
      } else {
        setModalMessage('Facebook ile giriş yapılamadı. Lütfen tekrar deneyin.');
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error('Facebook login error:', error);
      setModalMessage('Bir hata oluştu. Lütfen tekrar deneyin.');
      setShowErrorModal(true);
    } finally {
      setLoading(prev => ({ ...prev, facebook: false }));
    }
  };

  return (
    <View style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <ScreenContainer>
          {/* Logo ve Branding */}
          <View style={styles.logoSection}>
            <View style={styles.logoContainer}>
              <Image 
                source={require('../../android/app/src/main/res/drawable/splash_logo.png')}
                style={styles.logoImage}
                resizeMode="contain"
              />
            </View>
            <Text style={styles.appName}>Fishivo</Text>
            <Text style={styles.tagline}>Balıkçılık Deneyimini Paylaş</Text>
          </View>

          {/* Ana İçerik */}
          <View style={styles.contentSection}>
            <View style={styles.welcomeContainer}>
              <Text style={styles.welcomeTitle}>Hoş Geldin!</Text>
              <Text style={styles.welcomeSubtitle}>
                Fishivo topluluğuna katılmak için giriş yap
              </Text>
            </View>

            {/* Giriş Butonları */}
            <View style={styles.loginButtonsContainer}>
              <SocialLoginButton
                provider="google"
                onPress={handleGoogleLogin}
                loading={loading.google}
              />
              
              <SocialLoginButton
                provider="facebook"
                onPress={handleFacebookLogin}
                loading={loading.facebook}
              />
            </View>

            {/* Özellikler */}
            <View style={styles.featuresContainer}>
              <View style={styles.featureRow}>
                <View style={styles.featureItem}>
                  <View style={styles.featureIconContainer}>
                    <Icon name="map-pin" size={20} color={theme.colors.primary} />
                  </View>
                  <Text style={styles.featureText}>Balık noktalarını keşfet</Text>
                </View>
                <View style={styles.featureItem}>
                  <View style={styles.featureIconContainer}>
                    <Icon name="bar-chart-2" size={20} color={theme.colors.primary} />
                  </View>
                  <Text style={styles.featureText}>Avlarını kaydet</Text>
                </View>
              </View>
              <View style={styles.featureRow}>
                <View style={styles.featureItem}>
                  <View style={styles.featureIconContainer}>
                    <Icon name="users" size={20} color={theme.colors.primary} />
                  </View>
                  <Text style={styles.featureText}>Topluluğa katıl</Text>
                </View>
                <View style={styles.featureItem}>
                  <View style={styles.featureIconContainer}>
                    <Icon name="cloud-rain" size={20} color={theme.colors.primary} />
                  </View>
                  <Text style={styles.featureText}>Hava durumu</Text>
                </View>
              </View>
            </View>
          </View>

          {/* Footer */}
          <View style={styles.footer}>
            <Text style={styles.footerText}>
              Giriş yaparak{' '}
              <Text style={styles.linkText}>Kullanım Koşulları</Text>
              {' '}ve{' '}
              <Text style={styles.linkText}>Gizlilik Politikası</Text>
              'nı kabul etmiş olursunuz
            </Text>
          </View>
        </ScreenContainer>

        <SuccessModal
          visible={showSuccessModal}
          title="Giriş Başarılı!"
          message={modalMessage}
          onClose={() => setShowSuccessModal(false)}
        />

        <SuccessModal
          visible={showErrorModal}
          title="Hata"
          message={modalMessage}
          onClose={() => setShowErrorModal(false)}
        />
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  safeArea: {
    flex: 1,
  },
  logoSection: {
    alignItems: 'center',
    paddingTop: theme.spacing.xxl,
    paddingBottom: theme.spacing.xl,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: theme.borderRadius.full,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: theme.spacing.lg,
    ...theme.shadows.lg,
  },
  logoImage: {
    width: '100%',
    height: '100%',
    borderRadius: theme.borderRadius.full,
  },
  appName: {
    fontSize: theme.typography['4xl'],
    color: theme.colors.text,
    fontWeight: theme.typography.bold,
    marginBottom: theme.spacing.xs,
  },
  tagline: {
    fontSize: theme.typography.base,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  contentSection: {
    flex: 1,
    justifyContent: 'center',
  },
  welcomeContainer: {
    alignItems: 'center',
    marginBottom: theme.spacing.xxl,
  },
  welcomeTitle: {
    fontSize: theme.typography['3xl'],
    color: theme.colors.text,
    fontWeight: theme.typography.bold,
    marginBottom: theme.spacing.sm,
  },
  welcomeSubtitle: {
    fontSize: theme.typography.base,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  loginButtonsContainer: {
    gap: theme.spacing.md,
    marginBottom: theme.spacing.xxl,
  },
  featuresContainer: {
    gap: theme.spacing.lg,
  },
  featureRow: {
    flexDirection: 'row',
    gap: theme.spacing.lg,
  },
  featureItem: {
    flex: 1,
    alignItems: 'center',
  },
  featureIconContainer: {
    width: 40,
    height: 40,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: theme.spacing.sm,
  },
  featureText: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 18,
  },
  footer: {
    paddingTop: theme.spacing.xl,
    paddingBottom: theme.spacing.lg,
  },
  footerText: {
    fontSize: theme.typography.xs,
    color: theme.colors.textTertiary,
    textAlign: 'center',
    lineHeight: 16,
  },
  linkText: {
    color: theme.colors.primary,
    fontWeight: theme.typography.medium,
  },
});

export default AuthScreen; 