import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  Alert,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from "../components/Icon";
import SuccessModal from '../components/SuccessModal';
import AppHeader from '../components/AppHeader';
import { theme } from '../theme';
import { ScreenContainer } from '../components';
import ConfirmModal from '../components/ConfirmModal';
import { apiService } from '../services/api';
import { AuthService } from '../services/authService';

interface EditProfileScreenProps {
  navigation: any;
}

const EditProfileScreen: React.FC<EditProfileScreenProps> = ({ navigation }) => {
  const [profile, setProfile] = useState({
    name: '',
    title: '',
    username: '',
    bio: '',
    avatar: null,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showAvatarModal, setShowAvatarModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);

  useEffect(() => {
    const loadProfile = async () => {
      try {
        const currentUser = await AuthService.getCurrentUser();
        if (currentUser) {
          setProfile({
            name: currentUser.full_name || currentUser.name || '',
            title: '', // Bu field backend'de yoksa boş bırak
            username: currentUser.username || '',
            bio: '', // Bio field'i backend'den gelecek
            avatar: currentUser.avatar_url || null,
          });
        }
      } catch (error) {
        console.error('Profil yüklenirken hata:', error);
      }
    };

    loadProfile();
  }, []);

  const handleSave = async () => {
    setIsLoading(true);
    try {
      const updateData = {
        full_name: profile.name,
        username: profile.username,
        // bio: profile.bio, // Backend'de bio field'i varsa ekle
        // avatar_url: profile.avatar // Avatar update başka endpoint'te olabilir
      };

      await apiService.updateProfile(updateData);
      setShowSuccessModal(true);
    } catch (error) {
      console.error('Profil güncelleme hatası:', error);
      setShowErrorModal(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuccessClose = () => {
    setShowSuccessModal(false);
    navigation.goBack();
  };

  const handleChangeAvatar = () => {
    setShowAvatarModal(true);
  };

  const handleCameraSelect = () => {
    setShowAvatarModal(false);
    // Kamera işlemi
  };

  const handleGallerySelect = () => {
    setShowAvatarModal(false);
    // Galeri işlemi
  };

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        title="Profili Düzenle"
        leftButtons={[
          {
            icon: "arrow-left",
            onPress: () => navigation.goBack()
          }
        ]}
      />
      
      {/* Save Button */}
      <View style={styles.saveButtonContainer}>
        <TouchableOpacity
          style={[styles.saveButton, isLoading && styles.saveButtonDisabled]}
          onPress={handleSave}
          disabled={isLoading}
        >
          <Text style={styles.saveButtonText}>
            {isLoading ? 'Kaydediliyor...' : 'Kaydet'}
          </Text>
        </TouchableOpacity>
      </View>

      <ScreenContainer>
        <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.avatarSection}>
          <View style={styles.avatarContainer}>
            <View style={styles.avatar}>
              {profile.avatar ? (
                <Image source={{ uri: profile.avatar }} style={styles.avatarImage} />
              ) : (
                <Text style={styles.avatarText}>👤</Text>
              )}
            </View>
            <TouchableOpacity 
              style={styles.cameraButton}
              onPress={handleChangeAvatar}
            >
              <Icon name="camera" size={16} color={theme.colors.background} />
            </TouchableOpacity>
          </View>
          <Text style={styles.avatarHint}>Profil fotoğrafınızı değiştirmek için dokunun</Text>
        </View>

        <View style={styles.formSection}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Ad Soyad</Text>
            <TextInput
              style={styles.input}
              value={profile.name}
              onChangeText={(text) => setProfile(prev => ({ ...prev, name: text }))}
              placeholder="Adınızı ve soyadınızı girin"
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Unvan</Text>
            <TextInput
              style={styles.input}
              value={profile.title}
              onChangeText={(text) => setProfile(prev => ({ ...prev, title: text }))}
              placeholder="Örn: Profesyonel Balıkçı, Amatör Balıkçı"
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Kullanıcı Adı</Text>
            <TextInput
              style={styles.input}
              value={profile.username}
              onChangeText={(text) => setProfile(prev => ({ ...prev, username: text.toLowerCase() }))}
              placeholder="Kullanıcı adınız"
              placeholderTextColor={theme.colors.textSecondary}
              autoCapitalize="none"
            />
            <Text style={styles.usernameHint}>
              @{profile.username}
            </Text>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Hakkında</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={profile.bio}
              onChangeText={(text) => setProfile(prev => ({ ...prev, bio: text }))}
              placeholder="Kendiniz hakkında birkaç kelime yazın"
              placeholderTextColor={theme.colors.textSecondary}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
            <Text style={styles.characterCount}>
              {profile.bio.length}/250
            </Text>
          </View>
        </View>

        <View style={{ height: theme.spacing.xl }} />
        </ScrollView>
      </ScreenContainer>

      <SuccessModal
        visible={showSuccessModal}
        message="Profil bilgileriniz güncellendi"
        onClose={handleSuccessClose}
      />

      <SuccessModal
        visible={showErrorModal}
        title="Hata"
        message="Profil güncellenirken bir hata oluştu"
        onClose={() => setShowErrorModal(false)}
      />

      <SuccessModal
        visible={showAvatarModal}
        title="Profil Fotoğrafı"
        message="Profil fotoğrafı değiştirme özelliği yakında gelecek!"
        onClose={() => setShowAvatarModal(false)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  saveButtonContainer: {
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    alignItems: 'flex-end',
  },
  saveButton: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.sm,
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.md,
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  saveButtonText: {
    fontSize: theme.typography.sm,
    color: theme.colors.background,
    fontWeight: theme.typography.medium,
  },
  content: {
    flex: 1,
  },
  avatarSection: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
    paddingHorizontal: theme.spacing.sm,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: theme.spacing.sm,
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: theme.colors.surfaceVariant,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 4,
    borderColor: theme.colors.primary,
    overflow: 'hidden',
  },
  avatarImage: {
    width: '100%',
    height: '100%',
  },
  avatarText: {
    fontSize: 50,
  },
  cameraButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: theme.colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 3,
    borderColor: theme.colors.background,
  },
  avatarHint: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  formSection: {
    paddingHorizontal: theme.spacing.sm,
  },
  inputGroup: {
    marginBottom: theme.spacing.lg,
  },
  label: {
    fontSize: theme.typography.sm,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
    marginBottom: theme.spacing.xs,
  },
  input: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.md,
    fontSize: theme.typography.base,
    color: theme.colors.text,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  textArea: {
    height: 100,
    paddingTop: theme.spacing.md,
  },
  usernameHint: {
    fontSize: theme.typography.sm,
    color: theme.colors.primary,
    marginTop: theme.spacing.xs,
  },
  characterCount: {
    fontSize: theme.typography.xs,
    color: theme.colors.textSecondary,
    textAlign: 'right',
    marginTop: theme.spacing.xs,
  },
});

export default EditProfileScreen; 