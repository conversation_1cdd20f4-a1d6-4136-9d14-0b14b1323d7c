import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Alert,
  Modal,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { apiService, Post as ApiPost } from '../services/api';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from "../components/Icon";
import CatchCard from "../components/CatchCard";
import AppHeader from "../components/AppHeader";
import { theme } from '../theme';
import { ScreenContainer } from '../components';
import SuccessModal from '../components/SuccessModal';
import { useUnits } from '../hooks/useUnits';

// Spot koordinatları helper fonksiyonu
const getCoordinatesForLocation = (location: string): [number, number] => {
  const locationCoordinates: { [key: string]: [number, number] } = {
    'Boğaziçi': [28.9784, 41.0082],
    '<PERSON><PERSON>': [28.9744, 41.0199],
    'Ka<PERSON>ıköy İskelesi': [29.0158, 40.9833],
    'Büyükada': [29.1189, 40.8606],
  };
  return locationCoordinates[location] || [28.9784, 41.0082]; // Default: İstanbul Boğazı
};

interface HomeScreenProps {
  navigation: any;
}

interface CatchItem {
  id: string;
  user: {
    id: string;
    name: string;
    avatar?: string | null;
    location: string;
    country?: string;
    isPro?: boolean;
  };
  fish: {
    species: string;
    weight: string;
    length: string;
  };
  image: string;
  images?: string[];
  likes: number;
  comments: number;
  timeAgo: string;
  equipmentDetails?: Array<{
    id: string;
    name: string;
    category: string;
    brand?: string;
    icon: string;
    condition: 'excellent' | 'good' | 'fair';
  }>;
}

const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const [showOptionsModal, setShowOptionsModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState('');
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [posts, setPosts] = useState<CatchItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const { convertAndFormat } = useUnits();
  
  // 🎣 API'den catches çek
  const loadCatches = async () => {
    try {
      setLoading(true);
      const apiCatches = await apiService.getCatches();
      
      console.log('📥 API Catches:', apiCatches);
      
      // API verisini mevcut format'a çevir (UI değişmeden)
      const formattedCatches = apiCatches.items.map((catchItem: any) => ({
        id: catchItem.id.toString(),
        user: {
          id: catchItem.user_id,
          name: catchItem.user?.full_name || catchItem.user?.username || 'Balık Avcısı',
          avatar: catchItem.user?.avatar_url || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
          location: catchItem.location?.city || 'İstanbul, Türkiye',
          country: catchItem.location?.country || 'Türkiye',
          isPro: catchItem.user?.is_pro || false
        },
        fish: {
          species: catchItem.catch_details?.species_name || 'Bilinmeyen Balık',
          weight: catchItem.catch_details?.weight ? `${catchItem.catch_details.weight} kg` : '0 kg',
          length: catchItem.catch_details?.length ? `${catchItem.catch_details.length} cm` : '0 cm'
        },
        image: catchItem.image_url || 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400&h=300&fit=crop',
        images: catchItem.images || [],
        likes: catchItem.likes_count || 0,
        comments: catchItem.comments_count || 0,
        timeAgo: formatTimeAgo(catchItem.created_at),
        equipmentDetails: []
      }));
      
      setPosts(formattedCatches);
    } catch (error) {
      console.error('❌ Posts yüklenirken hata:', error);
      // Hata durumunda boş array
      setPosts([]);
      Alert.alert('Hata', 'Gönderiler yüklenirken bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Zaman formatı helper
  const formatTimeAgo = (dateString: string): string => {
    const now = new Date();
    const created = new Date(dateString);
    const diffMs = now.getTime() - created.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 60) {
      return `${diffMins} dakika önce`;
    } else if (diffHours < 24) {
      return `${diffHours} saat önce`;
    } else {
      return `${diffDays} gün önce`;
    }
  };

  useEffect(() => {
    loadCatches();
  }, []);

  const onRefresh = () => {
    setRefreshing(true);
    loadCatches();
  };

  const showMoreOptions = (userName: string) => {
    setSelectedUser(userName);
    setShowOptionsModal(true);
  };

  const handleReport = () => {
    setShowOptionsModal(false);
    setSuccessMessage('Bu gönderiyi inceleme için bildirdik. Teşekkürler.');
    setShowSuccessModal(true);
  };

  const handleBlock = () => {
    setShowOptionsModal(false);
    setSuccessMessage(`${selectedUser} artık gönderilerini görmeyeceksiniz.`);
    setShowSuccessModal(true);
  };

  const renderCatchItem = ({ item }: { item: CatchItem }) => {
    const handleUserPress = () => {
      // Profil sayfasına git - API'den kullanıcı verisi gelecek
      const userProfileData = {
        id: item.user.id,
        name: item.user.name,
        avatar: item.user.avatar,
        location: item.user.location,
        catchCount: 25,
        followers: 500,
        following: 200,
        bio: 'Balık avına tutku ile bağlı bir avcı',
        isPro: item.user.isPro,
      };

      navigation.navigate('UserProfile', { userData: userProfileData });
    };

    const handlePostPress = () => {
      // PostDetailScreen için veri hazırla
      const postData = {
        id: item.id,
        user: {
          name: item.user.name,
          avatar: item.user.avatar || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
          location: 'İstanbul, Türkiye',
          isPro: item.user.isPro,
        },
        fish: {
          species: item.fish.species,
          weight: item.fish.weight,
          length: item.fish.length,
        },
        photo: item.image,
        images: item.images || [item.image],
        likes: item.likes,
        comments: item.comments,
        timeAgo: item.timeAgo,
        description: `Bugün ${item.user.location} spot'unda güzel bir ${item.fish.species} yakaladım! ${item.fish.weight} ağırlığında harika bir deneyimdi.`,
        equipmentDetails: item.equipmentDetails,
        catchLocation: item.user.location,
        coordinates: getCoordinatesForLocation(item.user.location),
      };
      navigation.navigate('PostDetail', { postData });
    };

    const handleMorePress = () => {
      showMoreOptions(item.user.name);
    };

    return (
      <CatchCard
        item={item}
        onUserPress={handleUserPress}
        onPostPress={handlePostPress}
        onMorePress={handleMorePress}
      />
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        title="Keşfet"
        rightButtons={[
          {
            icon: "search",
            onPress: () => navigation.navigate('ExploreSearch')
          },
          {
            icon: "bell",
            onPress: () => navigation.navigate('Notifications')
          }
        ]}
      />

      <ScreenContainer>
        <FlatList
          data={posts}
          renderItem={renderCatchItem}
          keyExtractor={item => item.id}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
          ListEmptyComponent={() => (
            loading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={theme.colors.primary} />
                <Text style={styles.loadingText}>Postlar yükleniyor...</Text>
              </View>
            ) : (
              <View style={styles.emptyContainer}>
                <Icon name="fish" size={48} color={theme.colors.textSecondary} />
                <Text style={styles.emptyText}>Henüz post yok</Text>
              </View>
            )
          )}
        />
      </ScreenContainer>

      {/* More Options Modal */}
      <Modal
        visible={showOptionsModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowOptionsModal(false)}
      >
        <TouchableOpacity 
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowOptionsModal(false)}
        >
          <View style={styles.modalContent}>
            <TouchableOpacity style={styles.optionItem} onPress={handleReport}>
              <Icon name="flag" size={20} color={theme.colors.error} />
              <Text style={styles.optionText}>Bildir</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.optionItem} onPress={handleBlock}>
              <Icon name="user-x" size={20} color={theme.colors.error} />
              <Text style={styles.optionText}>Engelle</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.optionItem, styles.cancelOption]} 
              onPress={() => setShowOptionsModal(false)}
            >
              <Icon name="x" size={20} color={theme.colors.textSecondary} />
              <Text style={styles.optionText}>İptal</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>

      <SuccessModal
        visible={showSuccessModal}
        message={successMessage}
        onClose={() => setShowSuccessModal(false)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.xl,
    padding: theme.spacing.lg,
    margin: theme.spacing.lg,
    minWidth: 200,
    gap: theme.spacing.sm,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.sm,
    borderRadius: theme.borderRadius.lg,
    backgroundColor: theme.colors.background,
    gap: theme.spacing.md,
  },
  cancelOption: {
    marginTop: theme.spacing.sm,
    backgroundColor: theme.colors.surfaceVariant,
  },
  optionText: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
  },
  loadingText: {
    fontSize: theme.typography.base,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.md,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
  },
  emptyText: {
    fontSize: theme.typography.base,
    color: theme.colors.textSecondary,
  },
});

export default HomeScreen; 