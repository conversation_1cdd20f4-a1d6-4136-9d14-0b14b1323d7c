import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from '../components/Icon';
import AddButton from '../components/AddButton';
import AppHeader from '../components/AppHeader';
import { theme } from '../theme';
import { ScreenContainer, HourlyForecast, LocationTabs } from '../components';
import { useUnits } from '../hooks/useUnits';
import { apiService } from '../services/api';

interface WeatherScreenProps {
  navigation: any;
  route?: {
    params?: {
      selectedLocation?: any;
    };
  };
}

interface SavedLocation {
  id: string;
  name: string;
  type: 'manual' | 'spot' | 'current';
  coordinates: {
    latitude: number;
    longitude: number;
  };
  address: string;
  isFavorite: boolean;
}

const { width } = Dimensions.get('window');

const WeatherScreen: React.FC<WeatherScreenProps> = ({ navigation, route }) => {
  const [currentLocationIndex, setCurrentLocationIndex] = useState(0);
  const { units, convertAndFormat } = useUnits();
  const [savedLocations, setSavedLocations] = useState<SavedLocation[]>([]);
  const [weatherData, setWeatherData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [formattedTemperature, setFormattedTemperature] = useState('');
  const [formattedWindSpeed, setFormattedWindSpeed] = useState('');
  const [formattedPressure, setFormattedPressure] = useState('');
  const [formattedDailyTemps, setFormattedDailyTemps] = useState<{[key: number]: {high: string, low: string}}>({});

  // Load saved locations from API
  useEffect(() => {
    const loadSavedLocations = async () => {
      try {
        // API'den kullanıcının kayıtlı lokasyonlarını çek
        const locations = await apiService.getUserLocations();
        setSavedLocations(locations);
        if (locations.length > 0) {
          setCurrentLocationIndex(0);
        }
      } catch (error) {
        console.error('Lokasyonlar yüklenirken hata:', error);
        // Hata durumunda sadece mevcut konum
        setSavedLocations([{
          id: 'current',
          name: 'Mevcut Konum',
          type: 'current',
          coordinates: { latitude: 41.0082, longitude: 28.9784 },
          address: 'İstanbul, Türkiye',
          isFavorite: false,
        }]);
      } finally {
        setLoading(false);
      }
    };

    loadSavedLocations();
  }, []);

  const currentLocation = savedLocations[currentLocationIndex];

  // API'den weather data çek
  const loadWeatherData = async (location: SavedLocation) => {
    try {
      const weather = await apiService.getCurrentWeather(
        location.coordinates.latitude,
        location.coordinates.longitude
      );
      setWeatherData(weather);
    } catch (error) {
      console.error('Hava durumu yüklenirken hata:', error);
      // Hata durumunda default data kullan
    }
  };

  // Location değiştiğinde weather data'yı güncelle
  useEffect(() => {
    if (currentLocation) {
      loadWeatherData(currentLocation);
    }
  }, [currentLocation]);

  useEffect(() => {
    const formatWeatherData = async () => {
      if (!weatherData) return;
      
      // Format current weather
      const temp = await convertAndFormat(weatherData.current.temperature, 'temperature');
      const wind = await convertAndFormat(weatherData.current.windSpeed, 'speed');
      const pressure = await convertAndFormat(weatherData.current.pressure, 'pressure');
      
      setFormattedTemperature(temp);
      setFormattedWindSpeed(wind);
      setFormattedPressure(pressure);
      
      // Format daily temperatures
      const dailyTemps: {[key: number]: {high: string, low: string}} = {};
      for (let i = 0; i < weatherData.daily.length; i++) {
        const day = weatherData.daily[i];
        const high = await convertAndFormat(day.high, 'temperature');
        const low = await convertAndFormat(day.low, 'temperature');
        dailyTemps[i] = { high, low };
      }
      setFormattedDailyTemps(dailyTemps);
    };

    formatWeatherData();
  }, [weatherData, convertAndFormat]);

  const getConditionIcon = (condition: string) => {
    switch (condition) {
      case 'sunny': return 'sun';
      case 'cloudy': return 'cloud';
      case 'rainy': return 'cloud-rain';
      default: return 'sun';
    }
  };

  const getRatingColor = (rating: string) => {
    switch (rating) {
      case 'Mükemmel': return theme.colors.success;
      case 'İyi': return theme.colors.primary;
      case 'Orta': return theme.colors.warning;
      case 'Kötü': return theme.colors.error;
      default: return theme.colors.textSecondary;
    }
  };

  const getLocationIcon = (type: string) => {
    switch (type) {
      case 'current': return 'crosshair';
      case 'spot': return 'compass';
      case 'manual': return 'map-pin';
      default: return 'map-pin';
    }
  };

  const openLocationManager = () => {
    navigation.navigate('LocationManagement');
  };

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        title="Hava Durumu"
        rightButtons={[
          {
            icon: "settings",
            onPress: openLocationManager
          },
          {
            icon: "refresh-cw",
            onPress: () => {}
          }
        ]}
      />

      <ScreenContainer>
        {/* Location Tabs */}
        <LocationTabs
          data={savedLocations}
          activeLocationId={currentLocation?.id || ''}
          onLocationPress={(location) => {
            const index = savedLocations.findIndex(loc => loc.id === location.id);
            if (index !== -1) {
              setCurrentLocationIndex(index);
            }
          }}
          onAddPress={openLocationManager}
          getLocationIcon={getLocationIcon}
        />
        <ScrollView showsVerticalScrollIndicator={false}>
        {/* Current Location Info */}
        <View style={styles.currentLocationCard}>
          <View style={styles.locationHeader}>
            <View style={styles.locationInfo}>
              <View style={styles.locationTitleRow}>
                <Icon 
                  name={getLocationIcon(currentLocation?.type)} 
                  size={16} 
                  color={theme.colors.primary} 
                />
                <Text style={styles.locationName}>{currentLocation?.name}</Text>
                {currentLocation?.type === 'spot' && (
                  <View style={styles.spotBadge}>
                    <Text style={styles.spotBadgeText}>SPOT</Text>
                  </View>
                )}
              </View>
              <Text style={styles.locationAddress}>{currentLocation?.address}</Text>
            </View>
          </View>

          {/* Current Weather */}
          <View style={styles.currentWeatherSection}>
            <View style={styles.temperatureRow}>
              <Text style={styles.temperature}>{formattedTemperature}</Text>
              <View style={styles.conditionColumn}>
                <Text style={styles.condition}>{weatherData?.current.condition}</Text>
                <Text style={styles.feelsLike}>Hissedilen 24°</Text>
              </View>
            </View>
            
            <View style={styles.quickStats}>
              <View style={styles.quickStatItem}>
                <Icon name="wind" size={14} color={theme.colors.textSecondary} />
                <Text style={styles.quickStatText}>{formattedWindSpeed}</Text>
              </View>
              <View style={styles.quickStatItem}>
                <Icon name="droplets" size={14} color={theme.colors.textSecondary} />
                <Text style={styles.quickStatText}>{weatherData?.current.humidity}%</Text>
              </View>
              <View style={styles.quickStatItem}>
                <Icon name="activity" size={14} color={theme.colors.textSecondary} />
                <Text style={styles.quickStatText}>{formattedPressure}</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Fishing Conditions */}
        <View style={styles.fishingCard}>
          <View style={styles.fishingHeader}>
            <Icon name="fish" size={20} color={theme.colors.primary} />
            <Text style={styles.fishingTitle}>Balıkçılık Koşulları</Text>
            <View style={styles.ratingContainer}>
              <Text style={[styles.ratingText, { color: getRatingColor(weatherData?.fishing.rating) }]}>
                {weatherData?.fishing.rating}
              </Text>
              <Text style={styles.scoreText}>{weatherData?.fishing.score}/100</Text>
            </View>
          </View>
          
          <View style={styles.fishingFactorsGrid}>
            {weatherData?.fishing.factors.map((factor, index) => (
              <View key={index} style={styles.factorGridItem}>
                <View style={[styles.factorIcon, { backgroundColor: `${factor.color}20` }]}>
                  <Icon name={factor.icon} size={14} color={factor.color} />
                </View>
                <Text style={styles.factorLabel}>{factor.label}</Text>
                <Text style={[styles.factorValue, { color: factor.color }]}>{factor.value}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Hourly Forecast */}
        <HourlyForecast 
          data={weatherData?.hourly} 
          getConditionIcon={getConditionIcon}
        />

        {/* Daily Forecast */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>5 Günlük Tahmin</Text>
          <View style={styles.dailyForecast}>
            {weatherData?.daily.map((day, index) => (
              <View key={index} style={[styles.dailyItem, index === weatherData.daily.length - 1 && styles.lastDailyItem]}>
                <Text style={styles.dayName}>{day.day}</Text>
                <View style={styles.dailyCondition}>
                  <Icon name={getConditionIcon(day.condition)} size={18} color={theme.colors.primary} />
                  <View style={styles.rainInfo}>
                    <Icon name="droplets" size={10} color={theme.colors.secondary} />
                    <Text style={styles.rainText}>{day.rain}%</Text>
                  </View>
                </View>
                <View style={styles.tempRange}>
                  <Text style={styles.highTemp}>{formattedDailyTemps[index]?.high}</Text>
                  <Text style={styles.lowTemp}>{formattedDailyTemps[index]?.low}</Text>
                </View>
              </View>
            ))}
          </View>
        </View>

        <View style={{ height: theme.spacing.xl }} />
        </ScrollView>
      </ScreenContainer>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },

  content: {
    flex: 1,
  },
  currentLocationCard: {
    backgroundColor: theme.colors.surface,
    marginTop: theme.spacing.md,
    borderRadius: theme.borderRadius.xl,
    padding: theme.spacing.lg,
  },
  locationHeader: {
    marginBottom: theme.spacing.md,
  },
  locationInfo: {
    gap: theme.spacing.xs,
  },
  locationTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
  },
  locationName: {
    fontSize: theme.typography.lg,
    color: theme.colors.text,
    fontWeight: theme.typography.semibold,
    flex: 1,
  },
  spotBadge: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.sm,
  },
  spotBadgeText: {
    fontSize: theme.typography.xs,
    color: '#FFFFFF',
    fontWeight: theme.typography.bold,
  },
  locationAddress: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
  },
  currentWeatherSection: {
    gap: theme.spacing.md,
  },
  temperatureRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.lg,
  },
  temperature: {
    fontSize: 48,
    color: theme.colors.text,
    fontWeight: theme.typography.bold,
  },
  conditionColumn: {
    flex: 1,
  },
  condition: {
    fontSize: theme.typography.lg,
    color: theme.colors.text,
    fontWeight: theme.typography.semibold,
    marginBottom: 2,
  },
  feelsLike: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
  },
  quickStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickStatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
  },
  quickStatText: {
    fontSize: theme.typography.sm,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
  },
  fishingCard: {
    backgroundColor: theme.colors.surface,
    marginTop: theme.spacing.md,
    borderRadius: theme.borderRadius.xl,
    padding: theme.spacing.lg,
  },
  fishingHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
    gap: theme.spacing.sm,
  },
  fishingTitle: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.semibold,
    flex: 1,
  },
  ratingContainer: {
    alignItems: 'flex-end',
  },
  ratingText: {
    fontSize: theme.typography.base,
    fontWeight: theme.typography.bold,
  },
  scoreText: {
    fontSize: theme.typography.xs,
    color: theme.colors.textSecondary,
  },
  fishingFactorsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: theme.spacing.md,
  },
  factorGridItem: {
    width: '47%',
    alignItems: 'center',
    gap: theme.spacing.xs,
  },
  factorIcon: {
    width: 28,
    height: 28,
    borderRadius: theme.borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  factorLabel: {
    fontSize: theme.typography.xs,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  factorValue: {
    fontSize: theme.typography.sm,
    fontWeight: theme.typography.bold,
    textAlign: 'center',
  },
  section: {
    marginTop: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.semibold,
    marginBottom: theme.spacing.md,
  },
  rainInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  rainText: {
    fontSize: theme.typography.xs,
    color: theme.colors.secondary,
  },
  dailyForecast: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    overflow: 'hidden',
  },
  dailyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  lastDailyItem: {
    borderBottomWidth: 0,
  },
  dayName: {
    fontSize: theme.typography.sm,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
    width: 70,
  },
  dailyCondition: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: theme.spacing.sm,
  },
  tempRange: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
    width: 70,
    justifyContent: 'flex-end',
  },
  highTemp: {
    fontSize: theme.typography.sm,
    color: theme.colors.text,
    fontWeight: theme.typography.semibold,
  },
  lowTemp: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
  },
});

export default WeatherScreen;