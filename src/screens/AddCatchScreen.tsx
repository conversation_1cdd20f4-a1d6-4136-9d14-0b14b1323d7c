import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  Alert,
  Image,
  FlatList,
  Dimensions,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from "../components/Icon";
import Button from "../components/Button";
import PhotoPickerModal from "../components/PhotoPickerModal";
import FishSpeciesSelectorModal from "../components/FishSpeciesSelectorModal";
import FishingTechniqueSelectorModal from "../components/FishingTechniqueSelectorModal";
import EquipmentSelectorModal from "../components/EquipmentSelectorModal";
import WeatherSelectorModal from "../components/WeatherSelectorModal";
import LocationCard from "../components/LocationCard";
import MapComponent from "../components/MapComponent";
import { useLocation, formatLocationString, getMapboxCoordinates } from '../services/LocationService';
import { theme } from '../theme';
import { ScreenContainer } from '../components';
import SuccessModal from '../components/SuccessModal';
// JSON imports removed - using API data
import { apiService } from '../services/api';
import { useUnits } from '../hooks/useUnits';
import AppHeader from '../components/AppHeader';
import imageUploadService from '../services/imageUploadService';

interface AddCatchScreenProps {
  navigation: any;
  route?: any;
}

// Map coordinate types - Mapbox dokümantasyonuna göre
interface MapCenter {
  lat: number;
  lng: number;
}

interface CatchData {
  species: string;
  waterType: 'freshwater' | 'saltwater' | '';
  weight: string;
  length: string;
  location: string;
  date: string;
  time: string;
  technique: string;
  weather: string;
  notes: string;
  images: string[];
  equipment: string[];
  useLiveBait: boolean;
  liveBait: string;
}

const AddCatchScreen: React.FC<AddCatchScreenProps> = ({ navigation, route }) => {
  const { getCurrentLocation, requestPermission } = useLocation();
  const { 
    units, 
    getWeightUnit, 
    getLengthUnit, 
    getDepthUnit, 
    getTemperatureUnit,
    convertFromUserUnit,
    isLoading: unitsLoading 
  } = useUnits();
  const [catchData, setCatchData] = useState<CatchData>({
    species: '',
    waterType: '',
    weight: '',
    length: '',
    location: '',
    date: new Date().toLocaleDateString('tr-TR'),
    time: new Date().toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' }),
    technique: '',
    weather: '',
    notes: '',
    images: [],
    equipment: [],
    useLiveBait: false,
    liveBait: '',
  });

  const [activeImageIndex, setActiveImageIndex] = useState(0);
  const [showPhotoPickerModal, setShowPhotoPickerModal] = useState(false);
  const [showFishSpeciesModal, setShowFishSpeciesModal] = useState(false);
  const [showTechniqueModal, setShowTechniqueModal] = useState(false);
  const [showEquipmentModal, setShowEquipmentModal] = useState(false);
  const [showWeatherModal, setShowWeatherModal] = useState(false);
  const [showSpotModal, setShowSpotModal] = useState(false);

  const [showMapModal, setShowMapModal] = useState<boolean>(false);
  const [mapCenter, setMapCenter] = useState<MapCenter>({
    lat: 41.0082,
    lng: 28.9784
  });
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // API'den spot verilerini çek
  const [savedSpots, setSavedSpots] = useState<any[]>([]);

  const [selectedSpot, setSelectedSpot] = useState<any>(null);

  // AddSpot'tan gelen yeni spot'u dinle
  useEffect(() => {
    if (route?.params?.newSpot) {
      const newSpot = route.params.newSpot;
      setCatchData(prev => ({ ...prev, location: newSpot.name }));
      // Params'ı temizle
      navigation.setParams({ newSpot: undefined });
    }
  }, [route?.params?.newSpot]);

  // MapScreen'deki gibi konum izni ve ilk konum al
  useEffect(() => {
    const initializeLocation = async () => {
      console.log('🎯 AddCatch: Konum izni kontrol ediliyor...');
      await requestPermission();
      
      console.log('🎯 AddCatch: İlk konum alınıyor...');
      const location = await getCurrentLocation();
      if (location) {
        setMapCenter({ lat: location.latitude, lng: location.longitude });
        console.log('✅ AddCatch: İlk konum alındı:', location.latitude.toFixed(6), location.longitude.toFixed(6));
      }
    };
    
    initializeLocation();
  }, []);

  // API'den verileri yükle
  useEffect(() => {
    const loadData = async () => {
      try {
        // Fish species yükle
        const speciesData = await apiService.getSpecies();
        const speciesNames = speciesData.map((species: any) => species.name);
        setFishSpecies(speciesNames);

        // Spots yükle
        const spotsData = await apiService.getSpots();
        setSavedSpots(spotsData.items || []);

        // Sabit veriler (JSON'dan API'ye taşınana kadar)
        setTechniques(['Olta', 'Spin', 'Jigging', 'Trolling', 'Fly Fishing']);
        setWeatherConditions(['Güneşli', 'Bulutlu', 'Yağmurlu', 'Rüzgarlı', 'Sisli']);
        
      } catch (error) {
        console.error('Balık türleri getirilemedi:', error);
        // Fallback veriler
        setFishSpecies(['Levrek', 'Çupra', 'Kalkan', 'Lüfer', 'Hamsi']);
        setTechniques(['Olta', 'Spin', 'Jigging', 'Trolling', 'Fly Fishing']);
        setWeatherConditions(['Güneşli', 'Bulutlu', 'Yağmurlu', 'Rüzgarlı', 'Sisli']);
        setSavedSpots([]);
      }
    };

    loadData();
  }, []);

  // API'den veri state'leri
  const [fishSpecies, setFishSpecies] = useState<string[]>([]);
  const [techniques, setTechniques] = useState<string[]>([]);
  const [weatherConditions, setWeatherConditions] = useState<string[]>([]);

  const handleSaveCatch = async () => {
    // Validation
    if (!catchData.species || !catchData.weight || !catchData.length) {
      Alert.alert('Eksik Bilgi', 'Lütfen balık türü, ağırlık ve boy bilgilerini girin.');
      return;
    }

    try {
      // **BİRİM DÖNÜŞÜMLERİ - Kullanıcı biriminden backend base unit'e çevir**
      const weightInKg = await convertFromUserUnit(parseFloat(catchData.weight), 'weight');
      const lengthInCm = await convertFromUserUnit(parseFloat(catchData.length), 'length');
      
      // Backend API'ye gönderilecek veri
      const postData = {
        content: catchData.notes || `${catchData.species} avı paylaştı`,
        images: catchData.images,
        location: {
          latitude: mapCenter.lat,
          longitude: mapCenter.lng,
          address: catchData.location
        },
        spot_id: selectedSpot?.id || null, // Seçilen spot'un ID'si
        catch_details: {
          species_name: catchData.species,
          weight: weightInKg, // Base unit (kg)
          length: lengthInCm, // Base unit (cm)
          bait_used: catchData.liveBait || 'Belirtilmemiş',
          technique: catchData.technique,
          weather_conditions: catchData.weather,
          time_of_day: getTimeOfDay() as 'morning' | 'afternoon' | 'evening' | 'night', // Mevcut saate göre
          equipment_used: catchData.equipment
        }
      };

      // Backend API çağrısı (gerçek implementasyon)
      console.log('Backend API\'ye gönderilen veri:', postData);
      
      // Gerçek API çağrısı
      const response = await apiService.createPost(postData);
      console.log('API Response:', response);
      //   },
      //   body: JSON.stringify(postData)
      // });
      
      setSuccessMessage('Avınız başarıyla kaydedildi! 🎣');
      setShowSuccessModal(true);
      
      // 2 saniye sonra önceki sayfaya dön
      setTimeout(() => {
        setShowSuccessModal(false);
        navigation.goBack();
      }, 2000);
      
    } catch (error) {
      console.error('Catch kaydedilirken hata:', error);
      Alert.alert('Hata', 'Av kaydedilirken bir hata oluştu.');
    }
  };

  // Zamanı belirle (time_of_day için)
  const getTimeOfDay = () => {
    const hour = new Date().getHours();
    if (hour >= 6 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 18) return 'afternoon';
    if (hour >= 18 && hour < 22) return 'evening';
    return 'night';
  };

  const handleImagePicker = () => {
    if (catchData.images.length >= 5) {
      setSuccessMessage('Maksimum 5 fotoğraf ekleyebilirsiniz.');
      setShowSuccessModal(true);
      return;
    }

    setShowPhotoPickerModal(true);
  };

  const handleCamera = async () => {
    try {
      const result = await imageUploadService.pickImage('camera');
      
      if (result && !result.didCancel && result.assets && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        setCatchData(prev => ({ 
          ...prev, 
          images: [...prev.images, imageUri] 
        }));
      }
    } catch (error) {
      console.error('Kamera hatası:', error);
    }
  };

  const handleGallery = async () => {
    try {
      const result = await imageUploadService.pickImage('gallery');
      
      if (result && !result.didCancel && result.assets && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        setCatchData(prev => ({ 
          ...prev, 
          images: [...prev.images, imageUri] 
        }));
      }
    } catch (error) {
      console.error('Galeri hatası:', error);
    }
  };

  const handleRemoveImage = (index: number) => {
    setCatchData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }));
    if (activeImageIndex >= catchData.images.length - 1) {
      setActiveImageIndex(Math.max(0, catchData.images.length - 2));
    }
  };

  const renderImageItem = ({ item, index }: { item: string, index: number }) => (
    <View style={styles.imageSlide}>
      <Image source={{ uri: item }} style={styles.slideImage} />
      <TouchableOpacity 
        style={styles.removeImageButton}
        onPress={() => handleRemoveImage(index)}
      >
        <Icon name="x" size={16} color="#FFFFFF" />
      </TouchableOpacity>
    </View>
  );

  const renderPaginationDots = () => {
    if (catchData.images.length <= 1) return null;
    
    return (
      <View style={styles.paginationContainer}>
        {catchData.images.map((_, index) => (
          <View
            key={index}
            style={[
              styles.paginationDot,
              index === activeImageIndex && styles.paginationDotActive
            ]}
          />
        ))}
      </View>
    );
  };

  // Spot selection handlers
  const handleSpotSelect = (spot: any) => {
    setSelectedSpot(spot);
    setCatchData(prev => ({ ...prev, location: spot.name }));
    setShowSpotModal(false);
  };

  const handleMapLocationConfirm = (): void => {
    const locationString = `${mapCenter.lat.toFixed(6)}°K, ${mapCenter.lng.toFixed(6)}°D`;
    setCatchData(prev => ({ ...prev, location: locationString }));
    setShowMapModal(false);
  };

  const handleMapRegionDidChange = (event: any): void => {
    // Real-time coordinate updates - Mapbox dokümantasyonuna göre
    if (event?.geometry?.coordinates) {
      const [lng, lat] = event.geometry.coordinates;
      setMapCenter({ lat, lng });
    }
  };





  const renderDropdown = (
    title: string, 
    value: string, 
    options: string[], 
    onSelect: (value: string) => void
  ) => (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>{title}</Text>
        <TouchableOpacity 
          style={styles.dropdown}
          onPress={() => {
            setSuccessMessage(`${title} seçimi yakında gelecek!`);
            setShowSuccessModal(true);
          }}
        >
          <Text style={[styles.dropdownText, !value && styles.placeholder]}>
            {value || `${title} seçin...`}
          </Text>
          <Icon name="chevron-right" size={16} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        title="Av Ekle"
        leftButtons={[
          {
            icon: 'arrow-left',
            onPress: () => navigation.goBack()
          }
        ]}
        rightComponent={
          <Button
            variant="primary"
            size="sm"
            onPress={handleSaveCatch}
          >
            Kaydet
          </Button>
        }
      />

      <ScreenContainer>
        <ScrollView showsVerticalScrollIndicator={false}>
        {/* Fotoğraflar */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { marginBottom: 0 }]}>Fotoğraflar</Text>
            <Text style={styles.photoCount}>{catchData.images.length}/5</Text>
          </View>
          
          {catchData.images.length > 0 ? (
            <View style={styles.imageContainer}>
              <FlatList
                data={catchData.images}
                renderItem={renderImageItem}
                keyExtractor={(item, index) => index.toString()}
                horizontal
                pagingEnabled
                showsHorizontalScrollIndicator={false}
                onMomentumScrollEnd={(event) => {
                  const slideSize = Dimensions.get('window').width - (theme.spacing.md * 2);
                  const index = Math.round(event.nativeEvent.contentOffset.x / slideSize);
                  setActiveImageIndex(index);
                }}
              />
              {renderPaginationDots()}
              <TouchableOpacity 
                style={styles.addMoreButton}
                onPress={handleImagePicker}
                disabled={catchData.images.length >= 5}
              >
                <Icon name="plus" size={20} color={catchData.images.length >= 5 ? theme.colors.textSecondary : theme.colors.primary} />
                <Text style={[styles.addMoreText, catchData.images.length >= 5 && styles.disabledText]}>
                  Daha Fazla Ekle
                </Text>
              </TouchableOpacity>
            </View>
          ) : (
            <TouchableOpacity style={styles.imageContainer} onPress={handleImagePicker}>
              <View style={styles.imagePlaceholder}>
                <Icon name="camera" size={32} color={theme.colors.textSecondary} />
                <Text style={styles.imagePlaceholderText}>Fotoğraf Ekle</Text>
                <Text style={styles.imagePlaceholderSubtext}>Maksimum 5 fotoğraf</Text>
              </View>
            </TouchableOpacity>
          )}
        </View>

        {/* Açıklama */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Açıklama</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={catchData.notes}
            onChangeText={(text) => setCatchData(prev => ({ ...prev, notes: text }))}
            placeholder="Av hakkında açıklama, hikaye..."
            placeholderTextColor={theme.colors.textSecondary}
            multiline
            textAlignVertical="top"
          />
        </View>

        {/* Balık Türü */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Balık Türü</Text>
          <TouchableOpacity 
            style={styles.dropdown}
            onPress={() => setShowFishSpeciesModal(true)}
          >
            <Text style={[styles.dropdownText, !catchData.species && styles.placeholder]}>
              {catchData.species || 'Balık türü seçin...'}
            </Text>
            <Icon name="chevron-right" size={16} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        </View>

        {/* Ağırlık ve Boy */}
        <View style={styles.row}>
          <View style={[styles.section, { flex: 1, marginRight: theme.spacing.sm, marginBottom: 0 }]}>
            <Text style={styles.sectionTitle}>Ağırlık ({getWeightUnit()})</Text>
            <TextInput
              style={styles.input}
              value={catchData.weight}
              onChangeText={(text) => setCatchData(prev => ({ ...prev, weight: text }))}
              placeholder={units.weight === 'kg' ? "Örn: 2.5" : "Örn: 5.5"}
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="decimal-pad"
            />
          </View>
          <View style={[styles.section, { flex: 1, marginLeft: theme.spacing.sm, marginBottom: 0 }]}>
            <Text style={styles.sectionTitle}>Boy ({getLengthUnit()})</Text>
            <TextInput
              style={styles.input}
              value={catchData.length}
              onChangeText={(text) => setCatchData(prev => ({ ...prev, length: text }))}
              placeholder={units.length === 'cm' ? "Örn: 35" : "Örn: 14"}
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="numeric"
            />
          </View>
        </View>

        {/* Spot */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { marginBottom: 0 }]}>Spot</Text>
            <View style={styles.locationActions}>
              <TouchableOpacity 
                style={styles.locationActionButton}
                onPress={async () => {
                  const location = await getCurrentLocation();
                  if (location) {
                    const gpsLocation = formatLocationString(location);
                    setCatchData(prev => ({ ...prev, location: `GPS: ${gpsLocation}` }));
                    setMapCenter({ lat: location.latitude, lng: location.longitude });
                    console.log('✅ GERÇEK GPS ADDCATCH:', gpsLocation);
                  }
                }}
              >
                <Icon name="my-location" size={16} color={theme.colors.primary} />
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.locationActionButton}
                onPress={() => {
                  // Eğer daha önce GPS'ten konum alınmışsa haritayı o koordinatlarda aç
                  if (catchData.location && catchData.location.startsWith('GPS:')) {
                    // GPS verilerinden koordinatları çıkar
                    const coords = catchData.location.match(/(\d+\.\d+)°K, (\d+\.\d+)°D/);
                    if (coords) {
                      setMapCenter({ 
                        lat: parseFloat(coords[1]), 
                        lng: parseFloat(coords[2]) 
                      });
                    }
                  }
                  setShowMapModal(true);
                }}
              >
                <Icon name="map" size={16} color={theme.colors.primary} />
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.locationActionButton}
                onPress={() => setShowSpotModal(true)}
              >
                <Icon name="bookmark" size={16} color={theme.colors.primary} />
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.locationActionButton}
                onPress={() => navigation.navigate('AddSpot', { comeFromAddCatch: true })}
              >
                <Icon name="plus" size={16} color={theme.colors.primary} />
              </TouchableOpacity>
            </View>
          </View>
          
          {catchData.location ? (
            <TouchableOpacity 
              style={styles.selectedSpotContainer}
              onPress={() => setShowSpotModal(true)}
            >
              <Icon name="map-pin" size={20} color={theme.colors.primary} />
              <Text style={styles.selectedSpotText}>{catchData.location}</Text>
              <TouchableOpacity 
                style={styles.removeSpotButton}
                onPress={(e) => {
                  e.stopPropagation();
                  setCatchData(prev => ({ ...prev, location: '' }));
                }}
              >
                <Icon name="x" size={16} color={theme.colors.error} />
              </TouchableOpacity>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity 
              style={styles.emptySpotContainer}
              onPress={() => setShowSpotModal(true)}
            >
              <Icon name="map-pin" size={32} color={theme.colors.textSecondary} />
              <Text style={styles.emptyText}>Spot seçilmedi</Text>
              <Text style={styles.emptySubtext}>GPS, harita, spotlar veya yeni ekle</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Tarih ve Saat */}
        <View style={styles.row}>
          <View style={[styles.section, { flex: 1, marginRight: theme.spacing.sm, marginBottom: 0 }]}>
            <Text style={styles.sectionTitle}>Tarih</Text>
            <TextInput
              style={styles.input}
              value={catchData.date}
              onChangeText={(text) => setCatchData(prev => ({ ...prev, date: text }))}
              placeholder="gg.aa.yyyy"
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>
          <View style={[styles.section, { flex: 1, marginLeft: theme.spacing.sm, marginBottom: 0 }]}>
            <Text style={styles.sectionTitle}>Saat</Text>
            <TextInput
              style={styles.input}
              value={catchData.time}
              onChangeText={(text) => setCatchData(prev => ({ ...prev, time: text }))}
              placeholder="ss:dd"
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>
        </View>

        {/* Ekipman */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { marginBottom: 0 }]}>Kullanılan Ekipman</Text>
            <TouchableOpacity 
              style={styles.addButton}
              onPress={() => setShowEquipmentModal(true)}
            >
              <Icon name="plus" size={16} color={theme.colors.primary} />
              <Text style={styles.addButtonText}>Ekle</Text>
            </TouchableOpacity>
          </View>
          {catchData.equipment.length > 0 ? (
            <View style={styles.equipmentGrid}>
              {catchData.equipment.map((item, index) => (
                <View key={index} style={styles.equipmentCard}>
                  <View style={styles.equipmentIcon}>
                    <Icon name="package" size={20} color={theme.colors.primary} />
                  </View>
                  <View style={styles.equipmentContent}>
                    <Text style={styles.equipmentName} numberOfLines={1}>{item}</Text>
                    <Text style={styles.equipmentCategory}>Ekipman</Text>
                  </View>
                  <TouchableOpacity 
                    style={styles.removeEquipmentButton}
                    onPress={() => setCatchData(prev => ({ 
                      ...prev, 
                      equipment: prev.equipment.filter((_, i) => i !== index) 
                    }))}
                  >
                    <Icon name="x" size={12} color="#FFFFFF" />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          ) : (
            <View style={styles.emptyEquipmentContainer}>
              <Icon name="backpack" size={32} color={theme.colors.textSecondary} />
              <Text style={styles.emptyText}>Henüz ekipman eklenmedi</Text>
              <Text style={styles.emptySubtext}>Yukarıdaki "Ekle" butonunu kullanın</Text>
            </View>
          )}
        </View>

        {/* Teknik */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Balıkçılık Tekniği</Text>
          <TouchableOpacity 
            style={styles.dropdown}
            onPress={() => setShowTechniqueModal(true)}
          >
            <Text style={[styles.dropdownText, !catchData.technique && styles.placeholder]}>
              {catchData.technique || 'Balıkçılık Tekniği seçin...'}
            </Text>
            <Icon name="chevron-right" size={16} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        </View>

        {/* Hava Durumu */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Hava Durumu</Text>
          <TouchableOpacity 
            style={styles.dropdown}
            onPress={() => setShowWeatherModal(true)}
          >
            <Text style={[styles.dropdownText, !catchData.weather && styles.placeholder]}>
              {catchData.weather || 'Hava durumu seçin...'}
            </Text>
            <Icon name="chevron-right" size={16} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        </View>

        {/* Bottom Padding */}
        <View style={{ height: theme.spacing.xl }} />
        </ScrollView>
      </ScreenContainer>

      {/* Modals */}
      <PhotoPickerModal
        visible={showPhotoPickerModal}
        onClose={() => setShowPhotoPickerModal(false)}
        onCamera={handleCamera}
        onGallery={handleGallery}
        title="Fotoğraf Ekle"
        subtitle="Av fotoğrafını nereden eklemek istiyorsunuz?"
      />

      <FishSpeciesSelectorModal
        visible={showFishSpeciesModal}
        onClose={() => setShowFishSpeciesModal(false)}
        onSelect={(species, waterType) => setCatchData(prev => ({ ...prev, species, waterType }))}
        selectedSpecies={catchData.species}
      />

      <FishingTechniqueSelectorModal
        visible={showTechniqueModal}
        onClose={() => setShowTechniqueModal(false)}
        onSelect={(technique) => setCatchData(prev => ({ ...prev, technique }))}
        selectedTechnique={catchData.technique}
      />

      <EquipmentSelectorModal
        visible={showEquipmentModal}
        onClose={() => setShowEquipmentModal(false)}
        onSelect={(equipment) => setCatchData(prev => ({ ...prev, equipment }))}
        selectedEquipment={catchData.equipment}
        navigation={navigation}
      />

      <WeatherSelectorModal
        visible={showWeatherModal}
        onClose={() => setShowWeatherModal(false)}
        onSelect={(weather) => setCatchData(prev => ({ ...prev, weather }))}
        selectedWeather={catchData.weather}
      />

      {/* Spot Selection Modal */}
      <Modal
        visible={showSpotModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowSpotModal(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowSpotModal(false)}
        >
          <View style={styles.spotModal}>
            <View style={styles.modalHandle} />
            
            <View style={styles.modalTitleContainer}>
              <Icon name="anchor" size={24} color={theme.colors.primary} />
              <Text style={styles.modalTitle}>Spot Seç</Text>
            </View>
            <Text style={styles.modalSubtitle}>Kayıtlı spotlarınızdan seçin veya yeni spot ekleyin</Text>

            <ScrollView style={styles.spotsList} showsVerticalScrollIndicator={false}>
              {/* Saved Spots */}
              {savedSpots.length > 0 ? (
                <View style={styles.spotsGrid}>
                  {savedSpots.map((spot, index) => (
                    <LocationCard
                      key={spot.id}
                      location={{
                        ...spot,
                        type: spot.type as 'manual' | 'spot' | 'private-spot',
                        coordinates: {
                          latitude: spot.coordinates[1],
                          longitude: spot.coordinates[0]
                        }
                      }}
                      variant="grid"
                      onPress={handleSpotSelect}
                      showActions={false}
                    />
                  ))}
                </View>
              ) : (
                <View style={styles.emptyState}>
                  <Icon name="bookmark" size={48} color={theme.colors.textSecondary} />
                  <Text style={styles.emptyStateTitle}>Henüz kayıtlı spot yok</Text>
                  <Text style={styles.emptyStateDescription}>
                    Aşağıdaki "Spot Ekle" butonunu kullanarak ilk spotunuzu ekleyin
                  </Text>
                </View>
              )}

              {/* Add New Spot Button */}
              <TouchableOpacity 
                style={styles.addNewSpotButton}
                onPress={() => {
                  setShowSpotModal(false);
                  navigation.navigate('AddSpot', { comeFromAddCatch: true });
                }}
              >
                <Icon name="plus" size={20} color={theme.colors.primary} />
                <Text style={styles.addNewSpotText}>Spot Ekle</Text>
              </TouchableOpacity>
            </ScrollView>
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Map Modal */}
      <Modal
        visible={showMapModal}
        animationType="fade"
        presentationStyle="fullScreen"
      >
        <View style={styles.mapModalContainer}>
          <View style={styles.mapModalHeader}>
            <TouchableOpacity
              style={styles.mapModalCloseButton}
              onPress={() => setShowMapModal(false)}
            >
              <Icon name="x" size={24} color={theme.colors.text} />
            </TouchableOpacity>
            <Text style={styles.mapModalTitle}>Spot Seç</Text>
            <TouchableOpacity
              style={styles.mapModalSaveButton}
              onPress={() => {
                // Seçilen koordinatları kaydet
                if (mapCenter) {
                  const locationString = `${mapCenter.lat.toFixed(6)}°K, ${mapCenter.lng.toFixed(6)}°D`;
                  setCatchData(prev => ({ ...prev, location: locationString }));
                  setShowMapModal(false);
                }
              }}
            >
              <Text style={styles.mapModalSaveText}>Seç</Text>
            </TouchableOpacity>
          </View>

          <MapComponent
            initialCoordinates={mapCenter ? [mapCenter.lng, mapCenter.lat] : [29.0100, 41.0082]}
            showCrosshair={true}
            showCoordinates={true}
            showLocationButton={true}
            showLayerSelector={true}
            show3DToggle={true}
            onLocationSelect={(coordinates) => {
              setMapCenter({ lat: coordinates[1], lng: coordinates[0] });
            }}
            style={{ flex: 1 }}
          />
        </View>
      </Modal>

      <SuccessModal
        visible={showSuccessModal}
        title="Bilgi"
        message={successMessage}
        onClose={() => setShowSuccessModal(false)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',

    paddingVertical: theme.spacing.sm,
    backgroundColor: theme.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: theme.typography.lg,
    fontWeight: '600',
    color: theme.colors.text,
  },
  scrollView: {
    flex: 1,
    paddingTop: theme.spacing.md,
  },
  section: {
    marginBottom: theme.spacing.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.sm,
  },
  sectionTitle: {
    fontSize: theme.typography.base,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  photoCount: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
  },
  imageContainer: {
    aspectRatio: 1,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.lg,
    overflow: 'hidden',
  },
  imagePlaceholder: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    gap: theme.spacing.sm,
  },
  imagePlaceholderText: {
    fontSize: theme.typography.base,
    color: theme.colors.textSecondary,
  },
  imagePlaceholderSubtext: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
  },
  imageSlide: {
    width: Dimensions.get('window').width - (theme.spacing.md * 2),
    aspectRatio: 1,
    position: 'relative',
  },
  slideImage: {
    width: '100%',
    height: '100%',
    borderRadius: theme.borderRadius.lg,
  },
  removeImageButton: {
    position: 'absolute',
    top: theme.spacing.sm,
    right: theme.spacing.sm,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: theme.spacing.sm,
    gap: theme.spacing.xs,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: theme.colors.border,
  },
  paginationDotActive: {
    backgroundColor: theme.colors.primary,
  },
  addMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: theme.spacing.sm,
    padding: theme.spacing.md,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderStyle: 'dashed',
    gap: theme.spacing.sm,
  },
  addMoreText: {
    fontSize: theme.typography.sm,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  disabledText: {
    color: theme.colors.textSecondary,
  },
  dropdown: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.md,
  },
  dropdownText: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
  },
  placeholder: {
    color: theme.colors.textSecondary,
  },
  row: {
    flexDirection: 'row',
    marginBottom: theme.spacing.md,
  },
  input: {
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.md,
    fontSize: theme.typography.base,
    color: theme.colors.text,
  },
  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.md,
    gap: theme.spacing.sm,
  },
  locationInput: {
    flex: 1,
    fontSize: theme.typography.base,
    color: theme.colors.text,
  },
  spotText: {
    flex: 1,
    fontSize: theme.typography.base,
    color: theme.colors.text,
    marginLeft: theme.spacing.sm,
  },
  equipmentGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.sm,
  },
  equipmentCard: {
    width: '48%',
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    position: 'relative',
    minHeight: 80,
  },
  equipmentIcon: {
    width: 32,
    height: 32,
    borderRadius: theme.borderRadius.sm,
    backgroundColor: `${theme.colors.primary}15`,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: theme.spacing.sm,
  },
  equipmentContent: {
    flex: 1,
  },
  equipmentName: {
    fontSize: theme.typography.sm,
    color: theme.colors.text,
    fontWeight: theme.typography.semibold,
    marginBottom: 2,
  },
  equipmentCategory: {
    fontSize: theme.typography.xs,
    color: theme.colors.textSecondary,
    fontWeight: theme.typography.medium,
  },
  removeEquipmentButton: {
    position: 'absolute',
    top: 4,
    right: 4,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: `${theme.colors.error}dd`,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyEquipmentContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.xl,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderStyle: 'dashed',
  },
  emptySpotContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.xl,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderStyle: 'dashed',
  },
  selectedSpotContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.md,
    borderWidth: 1,
    borderColor: theme.colors.primary,
    gap: theme.spacing.sm,
  },
  selectedSpotText: {
    flex: 1,
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
  },
  removeSpotButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: `${theme.colors.error}20`,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    fontWeight: theme.typography.medium,
    marginTop: theme.spacing.sm,
    marginBottom: theme.spacing.xs,
  },
  emptySubtext: {
    fontSize: theme.typography.xs,
    color: theme.colors.textSecondary,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.primary,
    borderRadius: theme.borderRadius.sm,
    paddingHorizontal: theme.spacing.sm,
    height: 32,
    gap: 4,
  },
  addButtonText: {
    fontSize: theme.typography.sm,
    color: theme.colors.primary,
    fontWeight: theme.typography.medium,
  },
  locationActions: {
    flexDirection: 'row',
    gap: theme.spacing.xs,
  },
  locationActionButton: {
    width: 32,
    height: 32,
    borderRadius: theme.borderRadius.sm,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textArea: {
    height: 100,
    paddingTop: theme.spacing.md,
    textAlignVertical: 'top',
  },
  // Spot Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  spotModal: {
    backgroundColor: theme.colors.surface,
    borderTopLeftRadius: theme.borderRadius.xl,
    borderTopRightRadius: theme.borderRadius.xl,
    paddingTop: theme.spacing.md,
    paddingBottom: theme.spacing.xl,
    paddingHorizontal: theme.spacing.lg,
    maxHeight: '80%',
  },
  modalHandle: {
    width: 40,
    height: 4,
    backgroundColor: theme.colors.border,
    borderRadius: 2,
    alignSelf: 'center',
    marginBottom: theme.spacing.md,
  },
  modalTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: theme.spacing.sm,
    marginBottom: theme.spacing.xs,
  },
  modalTitle: {
    fontSize: theme.typography.xl,
    color: theme.colors.text,
    fontWeight: theme.typography.bold,
    textAlign: 'center',
  },
  modalSubtitle: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
  },
  spotsList: {
    flex: 1,
  },
  spotsListTitle: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.semibold,
    marginBottom: theme.spacing.sm,
  },
  spotItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.sm,
    borderRadius: theme.borderRadius.lg,
    backgroundColor: theme.colors.background,
    marginBottom: theme.spacing.xs,
    gap: theme.spacing.md,
  },
  spotInfo: {
    flex: 1,
  },
  spotName: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.semibold,
  },
  spotAddress: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  manualEntryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    borderRadius: theme.borderRadius.lg,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.md,
    marginBottom: theme.spacing.md,
    gap: theme.spacing.sm,
  },
  manualEntryInput: {
    flex: 1,
    fontSize: theme.typography.base,
    color: theme.colors.text,
  },
  confirmButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.md,
    paddingVertical: theme.spacing.md,
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  confirmButtonText: {
    fontSize: theme.typography.base,
    color: theme.colors.background,
    fontWeight: theme.typography.semibold,
  },
  addNewSpotButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.primary,
    borderStyle: 'dashed',
    gap: theme.spacing.sm,
  },
  addNewSpotText: {
    fontSize: theme.typography.base,
    color: theme.colors.primary,
    fontWeight: theme.typography.medium,
  },
  // Tab Navigation Styles
  tabNavigation: {
    flexDirection: 'row',
    backgroundColor: theme.colors.background,
    borderRadius: theme.borderRadius.lg,
    padding: 4,
    marginBottom: theme.spacing.lg,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
    gap: theme.spacing.xs,
  },
  tabButtonActive: {
    backgroundColor: theme.colors.surface,
  },
  tabButtonText: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    fontWeight: theme.typography.medium,
  },
  tabButtonTextActive: {
    color: theme.colors.primary,
  },
  // Grid and Content Styles
  spotsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.sm,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.xl * 2,
  },
  emptyStateTitle: {
    fontSize: theme.typography.lg,
    color: theme.colors.text,
    fontWeight: theme.typography.semibold,
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.xs,
  },
  emptyStateDescription: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  // Map Section Styles
  mapSection: {
    alignItems: 'center',
    paddingVertical: theme.spacing.lg,
  },
  sectionDescription: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
  },
  mapPreview: {
    width: '100%',
    height: 120,
    backgroundColor: theme.colors.background,
    borderRadius: theme.borderRadius.lg,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: theme.spacing.md,
  },
  mapOpenButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  mapOpenButtonText: {
    fontSize: theme.typography.base,
    color: theme.colors.primary,
    fontWeight: theme.typography.medium,
  },
  selectedLocationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
    backgroundColor: theme.colors.surface,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
  },
  selectedLocationText: {
    fontSize: theme.typography.sm,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
  },
  // Manual Section Styles
  manualSection: {
    paddingVertical: theme.spacing.md,
  },
  inputGroup: {
    marginBottom: theme.spacing.md,
  },
  inputLabel: {
    fontSize: theme.typography.sm,
    color: theme.colors.text,
    fontWeight: theme.typography.semibold,
    marginBottom: theme.spacing.xs,
  },
  textInput: {
    backgroundColor: theme.colors.background,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.md,
    fontSize: theme.typography.base,
    color: theme.colors.text,
  },
  saveSpotButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.md,
    paddingVertical: theme.spacing.md,
    alignItems: 'center',
    marginTop: theme.spacing.md,
  },
  saveSpotButtonText: {
    fontSize: theme.typography.base,
    color: theme.colors.background,
    fontWeight: theme.typography.semibold,
  },
  // Map Modal Styles
  mapContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  mapHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.md,
    backgroundColor: theme.colors.surface,
  },
  mapHeaderTitle: {
    fontSize: theme.typography.xl,
    color: theme.colors.text,
    fontWeight: theme.typography.bold,
  },
  mapView: {
    flex: 1,
  },
  mapOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    pointerEvents: 'none',
  },
  mapPin: {
    backgroundColor: theme.colors.primary,
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 3,
    borderColor: theme.colors.background,
  },
  mapControls: {
    position: 'absolute',
    bottom: theme.spacing.lg,
    left: theme.spacing.md,
    right: theme.spacing.md,
    flexDirection: 'row',
    gap: theme.spacing.sm,
  },
  mapCancelButton: {
    flex: 1,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    paddingVertical: theme.spacing.md,
    alignItems: 'center',
  },
  mapConfirmButton: {
    flex: 1,
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.md,
    paddingVertical: theme.spacing.md,
    alignItems: 'center',
  },
  mapCancelButtonText: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.semibold,
  },
  mapConfirmButtonText: {
    fontSize: theme.typography.base,
    color: theme.colors.background,
    fontWeight: theme.typography.semibold,
  },
  mapCrosshair: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    width: 60,
    height: 60,
    marginTop: -30,
    marginLeft: -30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  crosshairHorizontal: {
    position: 'absolute',
    width: 60,
    height: 2,
    backgroundColor: '#FF6B6B',
    borderRadius: 1,
  },
  crosshairVertical: {
    position: 'absolute',
    width: 2,
    height: 60,
    backgroundColor: '#FF6B6B',
    borderRadius: 1,
  },
  crosshairCenter: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#FF6B6B',
    backgroundColor: 'transparent',
  },
  mapLocationInfo: {
    position: 'absolute',
    top: theme.spacing.lg,
    left: theme.spacing.md,
    right: theme.spacing.md,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.sm,
    alignItems: 'center',
  },
  mapLocationText: {
    fontSize: theme.typography.sm,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
  },
  fabLocationButton: {
    position: 'absolute',
    top: 100, // Location info'nun altında
    right: 16,
    width: 40,
    height: 40,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  fabZoomContainer: {
    position: 'absolute',
    bottom: 120, // Bottom controls'un üstünde
    right: 16,
    flexDirection: 'column',
    gap: 8,
  },
  fabZoomButton: {
    width: 40,
    height: 40,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  // Map Modal Styles
  mapModalContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  mapModalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.sm,
    backgroundColor: theme.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  mapModalCloseButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  mapModalTitle: {
    fontSize: theme.typography.lg,
    fontWeight: '600',
    color: theme.colors.text,
  },
  mapModalSaveButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
  },
  mapModalSaveText: {
    color: '#FFFFFF',
    fontSize: theme.typography.base,
    fontWeight: '600',
  },
  saveButtonContainer: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    backgroundColor: theme.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
});

export default AddCatchScreen; 