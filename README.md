# 🎣 Fishivo - Balıkçılık Sosyal Platformu

Fishivo, balık tutma meraklıları için geliştirilmiş React Native tabanlı sosyal bir mobil uygulamadır.

## 🌐 **Deployment Architecture**

```
🏗️ FISHIVO - PRODUCTION ARCHITECTURE
┌─────────────────────────────────────────────────────────┐
│                📱 MOBILE APPS                           │
│         React Native 0.79.3 (iOS & Android)            │
└─────────────────────┬───────────────────────────────────┘
                      │ REST API
      ┌───────────────┼───────────────┐
      │               │               │
      ▼               ▼               ▼
┏━━━━━━━━━━━━━┓   ┏━━━━━━━━━━━┓   ┏━━━━━━━━━━━━━━━┓
┃   FLY.IO    ┃   ┃  FLY.IO   ┃   ┃   SUPABASE    ┃
┃   GLOBAL    ┃   ┃   EDGE    ┃   ┃    CLOUD      ┃
┃             ┃   ┃           ┃   ┃               ┃
┃ 🚀 Backend  ┃   ┃ 🌐 Web    ┃   ┃ 🗄️ Database   ┃
┃ Node.js API ┃   ┃ Next.js   ┃   ┃ PostgreSQL    ┃
┃ + Express   ┃   ┃ Dashboard ┃   ┃ + Auth + File ┃
┃ + WebSocket ┃   ┃ Landing   ┃   ┃ + Realtime    ┃
┗━━━━━━━━━━━━━┛   ┗━━━━━━━━━━━┛   ┗━━━━━━━━━━━━━━━┛
```

## 📱 Mobile-First Architecture

Bu proje **mobile-first** yaklaşımıyla geliştirilmiştir. Ana uygulama React Native mobil uygulamasıdır.

```
fishivo/
├── src/                    # React Native source code
├── android/                # Android native code  
├── ios/                    # iOS native code
├── App.tsx                 # Ana React Native component
├── index.js                # React Native entry point
├── packages/               # Supporting packages
│   ├── backend/           # Node.js API (Fly.io)
│   ├── web/               # Next.js web app (Fly.io)
│   └── shared/            # Shared utilities & types
└── package.json           # React Native ana package.json
```

## 🚀 Hızlı Başlangıç

### React Native Mobil Uygulama (Ana)
```bash
# Bağımlılıkları yükle
npm install

# iOS pods yükle (sadece macOS)
npm run pods

# Geliştirme sunucusunu başlat
npm start

# Android'de çalıştır
npm run android

# iOS'da çalıştır (sadece macOS)
npm run ios
```

### Packages (Opsiyonel)
```bash
# Backend ve web paketlerini yükle
npm run packages:install

# Backend ve web'i geliştirme modunda çalıştır
npm run packages:dev
```

## 🎯 Ana Özellikler

### 📱 React Native Mobil App
- **Balık Avlama Kaydı**: Fotoğraflı av paylaşımı
- **Harita & Spotlar**: GPS lokasyon, spot bulma
- **Balık Türleri**: Türk balık türleri rehberi
- **Sosyal Feed**: Takip, beğeni, yorum sistemi
- **Profil**: Av geçmişi, ekipman listesi
- **Arama**: Spot, tür, kullanıcı arama
- **Unit System**: 7 kategoride uluslararası ölçü birimleri

### 🌐 Supporting Packages (Fly.io)
- **Backend**: Supabase + Node.js API (Fly.io deployment)
- **Web**: Next.js bilgilendirme sitesi (Fly.io deployment)
- **Shared**: Ortak tipler ve tema

## 📋 React Native Gereksinimler

- Node.js 18+
- React Native CLI
- Android Studio (Android için)
- Xcode (iOS için - sadece macOS)

## 🔧 Geliştirme Komutları

```bash
# React Native
npm start              # Metro bundler başlat
npm run android        # Android emulator
npm run ios           # iOS simulator
npm run build:android # Android APK build
npm run build:ios     # iOS build

# Temizlik
npm run clean         # Android clean
npm run reset         # RN cache temizle

# Packages
npm run packages:dev  # Backend + Web dev mode
```

## 🌐 Production Deployment

### Fly.io Apps
```bash
# Backend deploy:
cd packages/backend
fly deploy -a fishivo-backend

# Web deploy:
cd packages/web  
fly deploy -a fishivo-web

# Status check:
fly status -a fishivo-backend
fly status -a fishivo-web
```

### Environment Variables
```bash
# Fly.io secrets:
fly secrets set SUPABASE_URL="https://xxx.supabase.co" -a fishivo-backend
fly secrets set SUPABASE_SERVICE_ROLE_KEY="xxx" -a fishivo-backend
fly secrets set NEXT_PUBLIC_SUPABASE_URL="https://xxx.supabase.co" -a fishivo-web
```

## 🗃️ Veritabanı

Supabase PostgreSQL kullanılmaktadır. SQL şeması `packages/database/` klasöründe bulunabilir.

### Database Migration
Veritabanı migration scriptleri `scripts/database-migration/` klasöründe organize edilmiştir:
- SQL şema dosyaları
- Otomatik migration scriptleri  
- Detaylı dokümantasyon

Detaylar için: [scripts/database-migration/README.md](./scripts/database-migration/README.md)

### 🎯 Unit System
7 kategoride comprehensive ölçü birimleri sistemi:
- **Ağırlık**: kg, g, lbs, oz (balık ağırlığı)
- **Uzunluk**: cm, m, inch, ft (balık boyu)
- **Mesafe**: km, m, miles, nm (lokasyon)
- **Sıcaklık**: celsius, fahrenheit (su/hava)
- **Derinlik**: meters, feet, fathoms (su derinliği)
- **Hız**: kmh, mph, knots (rüzgar/tekne)
- **Basınç**: hpa, inhg, mbar, mmhg (hava basıncı)

## 📁 Scripts

Proje utility scriptleri `scripts/` klasöründe organize edilmiştir:
- **database-migration/**: Supabase veritabanı kurulum ve migration scriptleri
- **React Native development**: Android build, screen generator vb.
- Her klasörde detaylı README dokümantasyonu mevcuttur

Detaylar için: [scripts/README.md](./scripts/README.md)

## 📱 Desteklenen Platformlar

- ✅ iOS 12.0+
- ✅ Android API 21+ (Android 5.0+)

## 🎨 Teknoloji Stack

### Mobile (Ana)
- React Native 0.79.3
- React Navigation 7
- React Native Maps
- Supabase Client
- TypeScript

### Packages (Fly.io)
- Backend: Node.js + Express + Supabase
- Web: Next.js + TailwindCSS
- Shared: TypeScript utilities

## 🔍 Monitoring & Debugging

### Fly.io Logs
```bash
# Real-time monitoring:
fly logs -a fishivo-backend --real-time
fly logs -a fishivo-web --real-time

# SSH access:
fly ssh console -a fishivo-backend
fly ssh console -a fishivo-web
```

Bu mobile-first yaklaşım sayesinde React Native uygulaması ana odak noktasıdır ve diğer packagelar (Fly.io'da host edilen) destekleyici rol oynar.

---

**Fishivo** - Türkiye'nin balıkçılık sosyal platformu 🎣  
**Powered by**: React Native + Supabase + Fly.io
