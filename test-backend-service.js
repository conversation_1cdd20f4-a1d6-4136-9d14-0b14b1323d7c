const r2Storage = require('./packages/backend/src/services/r2Storage.ts').default;
require('dotenv').config({ path: './.env' });

async function testBackendR2() {
  console.log('🔧 Backend R2 Service Test...');
  
  try {
    const testContent = Buffer.from('Backend service test - ' + new Date().toISOString());
    const testKey = 'backend-service-test-' + Date.now() + '.txt';
    
    console.log('📤 Testing backend upload service...');
    const result = await r2Storage.uploadFile(testContent, testKey, 'text/plain');
    
    if (result.success) {
      console.log('✅ Backend R2 Service SUCCESS!');
      console.log('   Key:', result.key);
      console.log('   URL:', result.url);
    } else {
      console.error('❌ Backend R2 Service FAILED:', result.error);
    }
    
  } catch (error) {
    console.error('❌ Backend R2 Service ERROR:', error.message);
  }
}

testBackendR2(); 