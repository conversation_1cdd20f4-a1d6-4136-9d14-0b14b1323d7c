const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');
require('dotenv').config({ path: './.env' });

async function testR2Upload() {
  console.log('🔧 R2 Test Started...');
  
  const s3Client = new S3Client({
    region: 'auto',
    endpoint: 'https://da180465fd6434ef8cb814aeaa9b30d9.r2.cloudflarestorage.com',
    credentials: {
      accessKeyId: 'ef26363a24155c5bf3a7e3fac46f1d75',
      secretAccessKey: '****************************************************************',
    },
    forcePathStyle: false,
  });

  try {
    const testContent = 'Test upload from Fishivo backend - ' + new Date().toISOString();
    const testKey = 'test/backend-test-' + Date.now() + '.txt';
    
    console.log('📤 Uploading test file:', testKey);
    
    const command = new PutObjectCommand({
      Bucket: 'fishivo',
      Key: testKey,
      Body: Buffer.from(testContent),
      ContentType: 'text/plain',
    });

    const result = await s3Client.send(command);
    
    console.log('✅ R2 Upload SUCCESS!');
    console.log('   ETag:', result.ETag);
    console.log('   Key:', testKey);
    console.log('   Public URL: https://pub-1a4e89800b334a4ab4c9b062ef375174.r2.dev/' + testKey);
    
  } catch (error) {
    console.error('❌ R2 Upload FAILED:', error.message);
    console.error('   Code:', error.Code);
    console.error('   Status:', error.$metadata?.httpStatusCode);
  }
}

testR2Upload(); 