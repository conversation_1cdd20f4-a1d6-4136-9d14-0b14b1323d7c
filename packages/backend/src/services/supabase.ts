import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Equipment interface
interface Equipment {
  id: number;
  name: string;
  brand: string;
  category: string;
  subcategory?: string;
  model?: string;
  specifications?: any;
  price_range?: {
    min: number;
    max: number;
  };
  image_url?: string;
  description?: string;
  user_rating?: number;
  review_count?: number;
}

// Lazy initialization to ensure environment variables are loaded
let _supabase: SupabaseClient | null = null;
let _supabaseClient: SupabaseClient | null = null;

function getSupabaseConfig() {
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  console.log('🔍 Supabase Config Debug:');
  console.log('  - URL:', url ? 'SET' : 'NOT SET');
  console.log('  - ANON_KEY:', anonKey ? 'SET' : 'NOT SET');
  console.log('  - SERVICE_KEY:', serviceRoleKey ? 'SET' : 'NOT SET');
  
  if (!url || !anonKey || !serviceRoleKey) {
    console.error('❌ Missing Supabase configuration:', { url: !!url, anonKey: !!anonKey, serviceRoleKey: !!serviceRoleKey });
    throw new Error('Missing Supabase configuration');
  }
  
  return { url, anonKey, serviceRoleKey };
}

// Create Supabase client for server-side operations (lazy getter)
export const getSupabase = () => {
  if (!_supabase) {
    const config = getSupabaseConfig();
    _supabase = createClient(
      config.url,
      config.serviceRoleKey,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );
  }
  return _supabase;
};

// Create Supabase client for client-side operations (lazy getter)
export const getSupabaseClient = () => {
  if (!_supabaseClient) {
    const config = getSupabaseConfig();
    _supabaseClient = createClient(
      config.url,
      config.anonKey,
      {
        auth: {
          autoRefreshToken: true,
          persistSession: true
        }
      }
    );
  }
  return _supabaseClient;
};

// Backward compatibility - these will be initialized when first accessed
export const supabase = new Proxy({} as SupabaseClient, {
  get(target, prop) {
    return getSupabase()[prop as keyof SupabaseClient];
  }
});

export const supabaseClient = new Proxy({} as SupabaseClient, {
  get(target, prop) {
    return getSupabaseClient()[prop as keyof SupabaseClient];
  }
});

// Sync user profile to Supabase auth.user_metadata
export async function syncUserMetadata(userId: string, updates: any) {
  try {
    // Sadece güncellenen alanları user_metadata'ya yaz
    const allowedFields = ['username', 'full_name', 'avatar_url'];
    const metadataUpdates: any = {};
    for (const key of allowedFields) {
      if (updates[key] !== undefined) {
        metadataUpdates[key] = updates[key];
      }
    }
    if (Object.keys(metadataUpdates).length === 0) return;
    await supabase.auth.admin.updateUserById(userId, {
      user_metadata: metadataUpdates
    });
  } catch (err) {
    console.error('Failed to sync user_metadata:', err);
  }
}

const USER_ALLOWED_FIELDS = [
  'id', 'email', 'username', 'full_name', 'avatar_url', 'is_premium', 'total_catches', 'total_spots', 'reputation_score', 'provider', 'created_at', 'updated_at', 'google_id', 'facebook_id', 'equipments'
];
const USER_METADATA_FIELDS = ['username', 'full_name', 'avatar_url'];

function filterUserData(userData: any) {
  return Object.fromEntries(Object.entries(userData).filter(([key]) => USER_ALLOWED_FIELDS.includes(key)));
}
function filterUserMetadata(userData: any) {
  return Object.fromEntries(Object.entries(userData).filter(([key]) => USER_METADATA_FIELDS.includes(key)));
}

function parseJSONSafe(str: any) {
  if (typeof str !== 'string') return str;
  try {
    return JSON.parse(str);
  } catch {
    return str;
  }
}

function getPagination(page: number, limit: number) {
  const from = (page - 1) * limit;
  const to = page * limit - 1;
  return [from, to];
}

// Database helper functions
export class DatabaseService {
  
  // User operations
  static async getUserById(userId: string) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();
    if (error && error.code !== 'PGRST116') throw error;
    if (data && typeof data.equipments === 'string') {
      data.equipments = parseJSONSafe(data.equipments);
    }
    return data;
  }

  static async getUserByEmail(email: string) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .single();
    if (error && error.code !== 'PGRST116') throw error;
    if (data && typeof data.equipments === 'string') {
      data.equipments = parseJSONSafe(data.equipments);
    }
    return data;
  }

  static async createUser(userData: any) {
    const safeData = filterUserData(userData);
    if (safeData.equipments && Array.isArray(safeData.equipments)) {
      safeData.equipments = JSON.stringify(safeData.equipments);
    }
    const { data, error } = await supabase
      .from('users')
      .insert(safeData)
      .select()
      .single();
    if (error) throw error;
    if (data && typeof data.equipments === 'string') {
      data.equipments = parseJSONSafe(data.equipments);
    }
    await syncUserMetadata(data.id, filterUserMetadata(safeData));
    return data;
  }

  static async updateUser(userId: string, updates: any) {
    const safeUpdates = filterUserData(updates);
    if (safeUpdates.equipments && Array.isArray(safeUpdates.equipments)) {
      safeUpdates.equipments = JSON.stringify(safeUpdates.equipments);
    }
    const { data, error } = await supabase
      .from('users')
      .update(safeUpdates)
      .eq('id', userId)
      .select()
      .single();
    if (error) throw error;
    if (data && typeof data.equipments === 'string') {
      data.equipments = parseJSONSafe(data.equipments);
    }
    await syncUserMetadata(userId, filterUserMetadata(safeUpdates));
    return data;
  }

  static async searchUsers(query: string, page = 1, limit = 20) {
    try {
      const { data, error, count } = await supabase
        .from('users')
        .select('id, username, full_name, avatar_url, bio, is_pro', { count: 'exact' })
        .or(`username.ilike.%${query}%, full_name.ilike.%${query}%`)
        .order('username')
        .range((page - 1) * limit, page * limit - 1);
      
      if (error) throw error;
      return { data: data || [], count: count || 0 };
    } catch (error) {
      console.error('Error searching users:', error);
      return { data: [], count: 0 };
    }
  }

    // Post operations
  static async getPosts(page = 1, limit = 10, filters: any = {}) {
    try {
      let query = supabase
        .from('posts')
        .select(`
          *,
          users:user_id (
            id,
            username,
            full_name,
            avatar_url,
            is_pro
          )
        `, { count: 'exact' })
        .order('created_at', { ascending: false });

      if (filters.userId) {
        query = query.eq('user_id', filters.userId);
      }

      if (filters.spotId) {
        query = query.eq('spot_id', filters.spotId);
      }

      if (filters.tripId) {
        query = query.eq('trip_id', filters.tripId);
      }

      const { data, error, count } = await query
        .range((page - 1) * limit, page * limit - 1);
      
      if (error) {
        console.error('Supabase getPosts error:', error);
        throw error;
      }
      
      // User bilgilerini düzelt
      const processedData = (data || []).map(post => ({
        ...post,
        user: post.users ? {
          id: post.users.id,
          username: post.users.username,
          full_name: post.users.full_name,
          avatar_url: post.users.avatar_url,
          is_pro: post.users.is_pro
        } : null
      }));
      
      return {
        items: processedData,
        total: count || 0,
        page,
        limit,
        hasMore: (page * limit) < (count || 0)
      };
    } catch (error) {
      console.error('DatabaseService.getPosts error:', error);
      // Return empty result instead of throwing
      return {
        items: [],
        total: 0,
        page,
        limit,
        hasMore: false
      };
    }
  }

  static async getPostById(postId: number) {
    const { data, error } = await supabase
      .from('posts')
      .select(`
        *,
        users:user_id (
          id,
          email,
          username,
          full_name,
          avatar_url
        ),
        spots:spot_id (
          id,
          name,
          location,
          spot_type
        )
      `)
      .eq('id', postId)
      .single();
    
    if (error) throw error;
    return data;
  }

  static async createPost(postData: any) {
    const { data, error } = await supabase
      .from('posts')
      .insert(postData)
      .select(`
        *,
        users:user_id (
          id,
          email,
          username,
          full_name,
          avatar_url
        )
      `)
      .single();
    
    if (error) throw error;
    return data;
  }

  static async updatePost(postId: number, updates: any) {
    const { data, error } = await supabase
      .from('posts')
      .update(updates)
      .eq('id', postId)
      .select(`
        *,
        users:user_id (
          id,
          email,
          username,
          full_name,
          avatar_url
        )
      `)
      .single();
    
    if (error) throw error;
    return data;
  }

  static async deletePost(postId: number) {
    const { error } = await supabase
      .from('posts')
      .delete()
      .eq('id', postId);
    
    if (error) throw error;
    return true;
  }

  static async searchPosts(query: string, filters: any = {}, page = 1, limit = 10) {
    let queryBuilder = supabase
      .from('posts')
      .select(`
        *,
        users:user_id (
          id,
          username,
          full_name,
          avatar_url
        )
      `, { count: 'exact' })
      .order('created_at', { ascending: false });

    if (query) {
      queryBuilder = queryBuilder.textSearch('content', query);
    }

    if (filters.species) {
      queryBuilder = queryBuilder.ilike('catch_details->species_name', `%${filters.species}%`);
    }

    if (filters.location) {
      queryBuilder = queryBuilder.ilike('location->city', `%${filters.location}%`);
    }

    if (filters.date_from) {
      queryBuilder = queryBuilder.gte('created_at', filters.date_from);
    }

    if (filters.date_to) {
      queryBuilder = queryBuilder.lte('created_at', filters.date_to);
    }

    if (filters.user_id) {
      queryBuilder = queryBuilder.eq('user_id', filters.user_id);
    }

    const { data, error, count } = await queryBuilder
      .range((page - 1) * limit, page * limit - 1);
    
    if (error) throw error;
    return { data, count };
  }

  // Species operations
  static async getSpecies() {
    const { data, error } = await supabase
      .from('fish_species')
      .select('*')
      .order('name');
    
    if (error) throw error;
    return data;
  }

  static async getSpeciesById(speciesId: number) {
    const { data, error } = await supabase
      .from('fish_species')
      .select('*')
      .eq('id', speciesId)
      .single();
    
    if (error) throw error;
    return data;
  }

  static async searchSpecies(query: string, page = 1, limit = 10) {
    const { data, error, count } = await supabase
      .from('fish_species')
      .select('*', { count: 'exact' })
      .or(`name.ilike.%${query}%, scientific_name.ilike.%${query}%`)
      .order('name')
      .range((page - 1) * limit, page * limit - 1);
    
    if (error) throw error;
    return { data, count };
  }

  // Spot operations
  static async getSpots(page = 1, limit = 10, filters: any = {}, userId?: string) {
    let query = supabase
      .from('spots')
      .select(`
        *,
        users:user_id (
          id,
          username,
          full_name,
          avatar_url,
          is_pro,
          pro_until
        )
      `, { count: 'exact' })
      .eq('status', 'approved')
      .order('created_at', { ascending: false });

    // Access type filtreleme: Public spotlar herkese, private spotlar sadece premium üyelere
    if (userId) {
      // Kullanıcının premium durumunu kontrol et
      const { data: user } = await supabase
        .from('users')
        .select('is_pro, pro_until')
        .eq('id', userId)
        .single();
      
      const isPremium = user?.is_pro && (!user.pro_until || new Date(user.pro_until) > new Date());
      
      if (isPremium) {
        // Premium üye: Tüm public spotları + kendi private spotlarını görebilir
        query = query.or(`access_type.eq.public,and(access_type.eq.private,user_id.eq.${userId})`);
      } else {
        // Normal üye: Sadece public spotları görebilir
        query = query.eq('access_type', 'public');
      }
    } else {
      // Giriş yapmamış kullanıcılar sadece public spotları görebilir
      query = query.eq('access_type', 'public');
    }

    if (filters.spot_type) {
      query = query.eq('spot_type', filters.spot_type);
    }

    if (filters.location) {
      query = query.ilike('location->city', `%${filters.location}%`);
    }

    const { data, error, count } = await query
      .range((page - 1) * limit, page * limit - 1);

    if (error) throw error;
    return { data, count };
  }

  static async getSpotById(spotId: number) {
    const { data, error } = await supabase
      .from('spots')
      .select(`
        *,
        users:user_id (
          id,
          username,
          full_name,
          avatar_url
        )
      `)
      .eq('id', spotId)
      .single();
    
    if (error) throw error;
    return data;
  }

  static async createSpot(spotData: any) {
    const { data, error } = await supabase
      .from('spots')
      .insert(spotData)
      .select(`
        *,
        users:user_id (
          id,
          username,
          full_name,
          avatar_url
        )
      `)
      .single();
    
    if (error) throw error;
    return data;
  }

  static async searchSpots(query: string, page = 1, limit = 10, userId?: string) {
    let spotQuery = supabase
      .from('spots')
      .select(`
        *,
        users:user_id (
          id,
          username,
          full_name,
          avatar_url,
          is_pro,
          pro_until
        )
      `, { count: 'exact' })
      .or(`name.ilike.%${query}%, description.ilike.%${query}%`)
      .eq('status', 'approved')
      .order('created_at', { ascending: false });

    // Access type filtreleme: Private spotlar sadece premium üyelere
    if (userId) {
      // Kullanıcının premium durumunu kontrol et
      const { data: user } = await supabase
        .from('users')
        .select('is_pro, pro_until')
        .eq('id', userId)
        .single();
      
      const isPremium = user?.is_pro && (!user.pro_until || new Date(user.pro_until) > new Date());
      
      if (isPremium) {
        // Premium üye: Tüm public spotları + kendi private spotlarını görebilir
        spotQuery = spotQuery.or(`access_type.eq.public,and(access_type.eq.private,user_id.eq.${userId})`);
      } else {
        // Normal üye: Sadece public spotları görebilir
        spotQuery = spotQuery.eq('access_type', 'public');
      }
    } else {
      // Giriş yapmamış kullanıcılar sadece public spotları görebilir
      spotQuery = spotQuery.eq('access_type', 'public');
    }

    const { data, error, count } = await spotQuery
      .range((page - 1) * limit, page * limit - 1);

    if (error) throw error;
    return { data, count };
  }

  // Like operations
  static async likePost(userId: string, postId: number) {
    const { data, error } = await supabase
      .from('likes')
      .insert({ user_id: userId, post_id: postId })
      .select()
      .single();
    
    if (error) throw error;

    // Update post likes count
    await supabase.rpc('increment_likes_count', { post_id: postId });
    
    return data;
  }

  static async unlikePost(userId: string, postId: number) {
    const { error } = await supabase
      .from('likes')
      .delete()
      .eq('user_id', userId)
      .eq('post_id', postId);
    
    if (error) throw error;

    // Update post likes count
    await supabase.rpc('decrement_likes_count', { post_id: postId });
    
    return true;
  }

  static async isPostLiked(userId: string, postId: number) {
    const { data, error } = await supabase
      .from('likes')
      .select('id')
      .eq('user_id', userId)
      .eq('post_id', postId)
      .single();
    
    return !error && !!data;
  }

  // Comment operations
  static async getComments(postId: number, page = 1, limit = 10) {
    const { data, error, count } = await supabase
      .from('comments')
      .select(`
        *,
        users:user_id (
          id,
          username,
          full_name,
          avatar_url
        )
      `, { count: 'exact' })
      .eq('post_id', postId)
      .order('created_at', { ascending: true })
      .range((page - 1) * limit, page * limit - 1);
    
    if (error) throw error;
    return { data, count };
  }

  static async createComment(commentData: any) {
    const { data, error } = await supabase
      .from('comments')
      .insert(commentData)
      .select(`
        *,
        users:user_id (
          id,
          username,
          full_name,
          avatar_url
        )
      `)
      .single();
    
    if (error) throw error;

    // Update post comments count
    await supabase.rpc('increment_comments_count', { post_id: commentData.post_id });
    
    return data;
  }

  // Follow operations
  static async followUser(followerId: string, followingId: string) {
    const { data, error } = await supabase
      .from('follows')
      .insert({ follower_id: followerId, following_id: followingId })
      .select()
      .single();
    
    if (error) throw error;

    // Update user counts
    await supabase.rpc('increment_followers_count', { user_id: followingId });
    await supabase.rpc('increment_following_count', { user_id: followerId });
    
    return data;
  }

  static async unfollowUser(followerId: string, followingId: string) {
    const { error } = await supabase
      .from('follows')
      .delete()
      .eq('follower_id', followerId)
      .eq('following_id', followingId);
    
    if (error) throw error;

    // Update user counts
    await supabase.rpc('decrement_followers_count', { user_id: followingId });
    await supabase.rpc('decrement_following_count', { user_id: followerId });
    
    return true;
  }

  static async isFollowing(followerId: string, followingId: string) {
    const { data, error } = await supabase
      .from('follows')
      .select('id')
      .eq('follower_id', followerId)
      .eq('following_id', followingId)
      .single();
    
    return !error && !!data;
  }

  static async getFollowers(userId: string, page = 1, limit = 10) {
    const { data, error, count } = await supabase
      .from('follows')
      .select(`
        follower_id,
        users:follower_id (
          id,
          username,
          full_name,
          avatar_url,
          followers_count
        )
      `, { count: 'exact' })
      .eq('following_id', userId)
      .range((page - 1) * limit, page * limit - 1);
    
    if (error) throw error;
    return { data, count };
  }

  static async getFollowing(userId: string, page = 1, limit = 10) {
    const { data, error, count } = await supabase
      .from('follows')
      .select(`
        following_id,
        users:following_id (
          id,
          username,
          full_name,
          avatar_url,
          followers_count
        )
      `, { count: 'exact' })
      .eq('follower_id', userId)
      .range((page - 1) * limit, page * limit - 1);
    
    if (error) throw error;
    return { data, count };
  }

  // Message operations
  static async getMessages(userId: string, otherUserId: string, page = 1, limit = 20) {
    const { data, error, count } = await supabase
      .from('messages')
      .select(`
        *,
        sender:sender_id (
          id,
          username,
          full_name,
          avatar_url
        ),
        receiver:receiver_id (
          id,
          username,
          full_name,
          avatar_url
        )
      `, { count: 'exact' })
      .or(`and(sender_id.eq.${userId},receiver_id.eq.${otherUserId}),and(sender_id.eq.${otherUserId},receiver_id.eq.${userId})`)
      .order('created_at', { ascending: true })
      .range((page - 1) * limit, page * limit - 1);
    
    if (error) throw error;
    return { data, count };
  }

  static async sendMessage(messageData: any) {
    const { data, error } = await supabase
      .from('messages')
      .insert(messageData)
      .select(`
        *,
        sender:sender_id (
          id,
          username,
          full_name,
          avatar_url
        ),
        receiver:receiver_id (
          id,
          username,
          full_name,
          avatar_url
        )
      `)
      .single();
    
    if (error) throw error;
    return data;
  }

  static async markMessageAsRead(messageId: number) {
    const { error } = await supabase
      .from('messages')
      .update({ read: true })
      .eq('id', messageId);
    
    if (error) throw error;
    return true;
  }

  // Notification operations
  static async getNotifications(userId: string, page = 1, limit = 20) {
    const { data, error, count } = await supabase
      .from('notifications')
      .select('id, user_id, title, is_read, created_at', { count: 'exact' })
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range((page - 1) * limit, page * limit - 1);
    if (error) throw error;
    return { data, count };
  }

  static async createNotification(notificationData: any) {
    const { data, error } = await supabase
      .from('notifications')
      .insert(notificationData)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }



  // Unit Preferences operations
  static async getUserPreferences(userId: string) {
    const { data, error } = await supabase
      .from('user_unit_preferences')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      throw error;
    }
    
    return data;
  }

  static async createUserPreferences(userId: string, units: any, region: string) {
    const { data, error } = await supabase
      .from('user_unit_preferences')
      .insert({
        user_id: userId,
        weight_unit: units.weight,
        length_unit: units.length,
        distance_unit: units.distance,
        temperature_unit: units.temperature,
        depth_unit: units.depth,
        speed_unit: units.speed,
        pressure_unit: units.pressure,
        region: region,
        auto_detect_region: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  static async updateUserPreferences(userId: string, units: any) {
    const { data, error } = await supabase
      .from('user_unit_preferences')
      .upsert({
        user_id: userId,
        weight_unit: units.weight,
        length_unit: units.length,
        distance_unit: units.distance,
        temperature_unit: units.temperature,
        depth_unit: units.depth,
        speed_unit: units.speed,
        pressure_unit: units.pressure,
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  // Trip operations
  static async getTripById(tripId: number) {
    const { data, error } = await supabase
      .from('fishing_trips')
      .select(`
        *,
        users:user_id (
          id,
          username,
          full_name,
          avatar_url
        ),
        spots:spot_id (
          id,
          name,
          location
        )
      `)
      .eq('id', tripId)
      .single();
    
    if (error) throw error;
    return data;
  }

  static async isTripParticipant(tripId: number, userId: string) {
    const { data, error } = await supabase
      .from('trip_participants')
      .select('id')
      .eq('trip_id', tripId)
      .eq('user_id', userId)
      .eq('status', 'accepted')
      .single();
    
    if (error && error.code !== 'PGRST116') throw error; // PGRST116 = no rows returned
    return !!data;
  }

  static async createTrip(tripData: any) {
    const { data, error } = await supabase
      .from('fishing_trips')
      .insert(tripData)
      .select(`
        *,
        users:user_id (
          id,
          username,
          full_name,
          avatar_url
        ),
        spots:spot_id (
          id,
          name,
          location
        )
      `)
      .single();
    
    if (error) throw error;
    return data;
  }

  // Spot reviews operations
  static async getSpotReviews(spotId: number, page = 1, limit = 10) {
    const { data, error, count } = await supabase
      .from('spot_reviews')
      .select(`
        *,
        users:user_id (
          id,
          username,
          full_name,
          avatar_url
        )
      `, { count: 'exact' })
      .eq('spot_id', spotId)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .range((page - 1) * limit, page * limit - 1);
    
    if (error) throw error;
    return { data, count };
  }

  static async createSpotReview(reviewData: any) {
    const { data, error } = await supabase
      .from('spot_reviews')
      .insert(reviewData)
      .select(`
        *,
        users:user_id (
          id,
          username,
          full_name,
          avatar_url
        )
      `)
      .single();
    
    if (error) throw error;
    return data;
  }

  static async hasUserReviewedSpot(spotId: number, userId: string) {
    const { data, error } = await supabase
      .from('spot_reviews')
      .select('id')
      .eq('spot_id', spotId)
      .eq('user_id', userId)
      .single();
    
    if (error && error.code !== 'PGRST116') throw error;
    return !!data;
  }

  // Spot favorites operations
  static async favoriteSpot(userId: string, spotId: number) {
    const { data, error } = await supabase
      .from('spot_favorites')
      .insert({ user_id: userId, spot_id: spotId })
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  static async unfavoriteSpot(userId: string, spotId: number) {
    const { error } = await supabase
      .from('spot_favorites')
      .delete()
      .eq('user_id', userId)
      .eq('spot_id', spotId);
    
    if (error) throw error;
    return true;
  }

  static async isSpotFavorited(userId: string, spotId: number) {
    const { data, error } = await supabase
      .from('spot_favorites')
      .select('id')
      .eq('user_id', userId)
      .eq('spot_id', spotId)
      .single();
    
    if (error && error.code !== 'PGRST116') throw error;
    return !!data;
  }

  static async getUserFavoriteSpots(userId: string, page = 1, limit = 10) {
    const { data, error, count } = await supabase
      .from('spot_favorites')
      .select(`
        spots (
          *,
          users:user_id (
            id,
            username,
            full_name,
            avatar_url
          )
        )
      `, { count: 'exact' })
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range((page - 1) * limit, page * limit - 1);
    
    if (error) throw error;
    return { data: data?.map(item => item.spots), count };
  }

  static async getUserEquipment(userId: string): Promise<Equipment[]> {
    try {
      const { data, error } = await supabase
        .from('user_equipment')
        .select(`
          *,
          equipment:equipment_id (
            *
          )
        `)
        .eq('user_id', userId);
      
      if (error) {
        console.error('Error fetching user equipment:', error);
        return [];
      }
      
      return data?.map(item => item.equipment) || [];
    } catch (error) {
      console.error('Error getting user equipment:', error);
      return [];
    }
  }

  static async addUserEquipment(equipmentData: any) {
    try {
      const { data, error } = await supabase
        .from('user_equipment')
        .insert(equipmentData)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error adding user equipment:', error);
      throw error;
    }
  }

  static async getBlockedUsers(userId: string, page = 1, limit = 20) {
    try {
      const { data, error, count } = await supabase
        .from('blocked_users')
        .select(`
          blocked_id,
          created_at,
          users:blocked_id (
            id,
            username,
            full_name,
            avatar_url
          )
        `, { count: 'exact' })
        .eq('blocker_id', userId)
        .order('created_at', { ascending: false })
        .range((page - 1) * limit, page * limit - 1);
      
      if (error) throw error;
      return { data, count };
    } catch (error) {
      console.error('Error fetching blocked users:', error);
      return { data: [], count: 0 };
    }
  }

  static async blockUser(blockerId: string, blockedId: string) {
    try {
      const { error } = await supabase
        .from('blocked_users')
        .insert({
          blocker_id: blockerId,
          blocked_id: blockedId
        });
      
      if (error) throw error;
    } catch (error) {
      console.error('Error blocking user:', error);
      throw error;
    }
  }

  static async unblockUser(blockerId: string, blockedId: string) {
    try {
      const { error } = await supabase
        .from('blocked_users')
        .delete()
        .eq('blocker_id', blockerId)
        .eq('blocked_id', blockedId);
      
      if (error) throw error;
    } catch (error) {
      console.error('Error unblocking user:', error);
      throw error;
    }
  }

  static async getEquipment(page = 1, limit = 50, filters: { category?: string, brand?: string } = {}) {
    try {
      let query = supabase
        .from('equipment')
        .select('*', { count: 'exact' })
        .order('name');

      if (filters.category) {
        query = query.eq('category', filters.category);
      }

      if (filters.brand) {
        query = query.eq('brand', filters.brand);
      }

      const { data, error, count } = await query
        .range((page - 1) * limit, page * limit - 1);
      
      if (error) throw error;
      
      return {
        items: data || [],
        total: count || 0,
        page,
        limit,
        hasMore: (page * limit) < (count || 0)
      };
    } catch (error) {
      console.error('Error getting equipment:', error);
      return {
        items: [],
        total: 0,
        page,
        limit,
        hasMore: false
      };
    }
  }

  static async searchEquipment(query: string, page = 1, limit = 20) {
    try {
      const { data, error, count } = await supabase
        .from('equipment')
        .select('*', { count: 'exact' })
        .or(`name.ilike.%${query}%, brand.ilike.%${query}%, category.ilike.%${query}%`)
        .order('name')
        .range((page - 1) * limit, page * limit - 1);
      
      if (error) throw error;
      return { data: data || [], count: count || 0 };
    } catch (error) {
      console.error('Error searching equipment:', error);
      return { data: [], count: 0 };
    }
  }

  static async getUserNotifications(userId: string, page = 1, limit = 20) {
    try {
      const { data, error, count } = await supabase
        .from('notifications')
        .select('*', { count: 'exact' })
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range((page - 1) * limit, page * limit - 1);
      
      if (error) throw error;
      
      return {
        items: data || [],
        total: count || 0,
        page,
        limit,
        hasMore: (page * limit) < (count || 0)
      };
    } catch (error) {
      console.error('Error getting user notifications:', error);
      return {
        items: [],
        total: 0,
        page,
        limit,
        hasMore: false
      };
    }
  }

  static async markNotificationAsRead(notificationId: number, userId: string) {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true, read_at: new Date().toISOString() })
        .eq('id', notificationId)
        .eq('user_id', userId);
      
      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      return false;
    }
  }

  static async markAllNotificationsAsRead(userId: string) {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true, read_at: new Date().toISOString() })
        .eq('user_id', userId)
        .eq('is_read', false);
      
      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      return false;
    }
  }

  // ================== FISHING TECHNIQUES OPERATIONS ==================
  
  static async getFishingTechniques() {
    const { data, error } = await supabase
      .from('fishing_techniques')
      .select('*')
      .eq('status', 'active')
      .order('name');
    
    if (error) throw error;
    return data;
  }

  static async searchFishingTechniques(query: string) {
    const { data, error } = await supabase
      .from('fishing_techniques')
      .select('*')
      .eq('status', 'active')
      .or(`name.ilike.%${query}%, description.ilike.%${query}%`)
      .order('name');
    
    if (error) throw error;
    return data;
  }

  static async getFishingTechniqueById(id: number) {
    try {
      const { data, error } = await supabase
        .from('fishing_techniques')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error getting fishing technique by ID:', error);
      throw error;
    }
  }

  // User locations for weather
  static async getUserLocations(userId: string) {
    try {
      // Kullanıcının kayıtlı lokasyonlarını çek
      const { data, error } = await supabase
        .from('user_locations')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      // Eğer hiç lokasyon yoksa default mevcut konum ekle
      if (!data || data.length === 0) {
        return [{
          id: 'current',
          name: 'Mevcut Konum',
          type: 'current',
          coordinates: { latitude: 41.0082, longitude: 28.9784 },
          address: 'İstanbul, Türkiye',
          isFavorite: false,
        }];
      }
      
      return data.map(location => ({
        id: location.id.toString(),
        name: location.name,
        type: location.type,
        coordinates: {
          latitude: location.latitude,
          longitude: location.longitude
        },
        address: location.address,
        isFavorite: location.is_favorite || false
      }));
    } catch (error) {
      console.error('Error getting user locations:', error);
      // Hata durumunda default lokasyon döndür
      return [{
        id: 'current',
        name: 'Mevcut Konum',
        type: 'current',
        coordinates: { latitude: 41.0082, longitude: 28.9784 },
        address: 'İstanbul, Türkiye',
        isFavorite: false,
      }];
    }
  }
}
