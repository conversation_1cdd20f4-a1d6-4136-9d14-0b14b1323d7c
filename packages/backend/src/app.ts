import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import { ENV } from './config';
// import rateLimit from 'express-rate-limit';
import { errorHandler } from './middleware/errorHandler';
// import { logger } from './middleware/logger';

// Import routes
import authRoutes from './routes/auth';
import userRoutes from './routes/users';
import postRoutes from './routes/posts';
import speciesRoutes from './routes/species';
import uploadRoutes from './routes/upload';
import spotsRoutes from './routes/spots';
import followsRoutes from './routes/follows';
import messagesRoutes from './routes/messages';
import searchRoutes from './routes/search';
import techniquesRoutes from './routes/techniques';


const app = express();

// Security middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS configuration - using centralized config
app.use(cors({
  origin: ENV.NODE_ENV === 'production' 
    ? ['https://fishivo.com', 'https://www.fishivo.com']
    : ENV.CORS_ORIGIN.split(','),
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Compression middleware
app.use(compression());

// Rate limiting - temporarily disabled
// const limiter = rateLimit({
//   windowMs: 15 * 60 * 1000, // 15 minutes
//   max: 100 // limit each IP to 100 requests per windowMs
// });
// app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
if (ENV.NODE_ENV !== 'test') {
  app.use(morgan('combined'));
}
// app.use(logger);

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/posts', postRoutes);
app.use('/api/species', speciesRoutes);
app.use('/api/upload', uploadRoutes);
app.use('/api/spots', spotsRoutes);
app.use('/api/follows', followsRoutes);
app.use('/api/messages', messagesRoutes);
app.use('/api/search', searchRoutes);
app.use('/api/techniques', techniquesRoutes);


// Health check endpoint
app.get('/health', (_req, res) => {
  res.status(200).json({
    success: true,
    message: 'Fishivo API is running!',
    timestamp: new Date().toISOString(),
    environment: ENV.NODE_ENV,
    version: '1.0.0'
  });
});

// Error handling
app.use(errorHandler);

export default app; 