import { Router } from 'express';
import { asyncHandler, AppError } from '../middleware/errorHandler';
import { ApiResponse } from '../types';

const router = Router();

// GET /api/weather/current - Get current weather for coordinates
router.get('/current', asyncHandler(async (req, res) => {
  const lat = parseFloat(req.query.lat as string);
  const lon = parseFloat(req.query.lon as string);

  if (isNaN(lat) || isNaN(lon)) {
    throw new AppError('Valid latitude and longitude required', 400);
  }

  // Mock weather data - gerçek weather API entegrasyonu eklenecek
  const weatherData = {
    current: {
      temperature: 22,
      condition: 'Güne<PERSON>li',
      windSpeed: 15,
      windDirection: 'KB',
      humidity: 65,
      pressure: 1013,
      uvIndex: 7,
      visibility: 10,
    },
    fishing: {
      rating: 'Mükemmel',
      score: 95,
      factors: [
        { label: '<PERSON>va Sıcaklığı', value: 'İdeal', icon: 'thermometer', color: '#22C55E' },
        { label: '<PERSON><PERSON><PERSON><PERSON>', value: 'Hafif', icon: 'wind', color: '#22C55E' },
        { label: 'Basınç', value: 'Stabil', icon: 'activity', color: '#22C55E' },
        { label: 'Ay Durumu', value: 'Yeni Ay', icon: 'moon', color: '#F59E0B' },
      ]
    },
    hourly: Array.from({ length: 24 }, (_, i) => ({
      time: `${(new Date().getHours() + i) % 24}:00`,
      temp: 20 + Math.random() * 8,
      condition: ['sunny', 'cloudy', 'rainy'][Math.floor(Math.random() * 3)],
      rain: Math.random() * 100
    })),
    daily: Array.from({ length: 7 }, (_, i) => ({
      day: ['Bugün', 'Yarın', 'Çarşamba', 'Perşembe', 'Cuma', 'Cumartesi', 'Pazar'][i],
      high: 20 + Math.random() * 10,
      low: 15 + Math.random() * 5,
      condition: ['sunny', 'cloudy', 'rainy'][Math.floor(Math.random() * 3)],
      rain: Math.random() * 100
    }))
  };

  const response: ApiResponse<any> = {
    success: true,
    data: weatherData
  };

  res.json(response);
}));

// GET /api/weather/forecast - Get weather forecast
router.get('/forecast', asyncHandler(async (req, res) => {
  const lat = parseFloat(req.query.lat as string);
  const lon = parseFloat(req.query.lon as string);
  const days = parseInt(req.query.days as string) || 5;

  if (isNaN(lat) || isNaN(lon)) {
    throw new AppError('Valid latitude and longitude required', 400);
  }

  // Mock forecast data
  const forecastData = Array.from({ length: Math.min(days, 7) }, (_, i) => ({
    date: new Date(Date.now() + i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    day: ['Bugün', 'Yarın', 'Çarşamba', 'Perşembe', 'Cuma', 'Cumartesi', 'Pazar'][i],
    temperature: {
      high: 20 + Math.random() * 10,
      low: 15 + Math.random() * 5
    },
    condition: ['sunny', 'cloudy', 'rainy'][Math.floor(Math.random() * 3)],
    wind: {
      speed: 5 + Math.random() * 20,
      direction: ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'][Math.floor(Math.random() * 8)]
    },
    humidity: 40 + Math.random() * 40,
    pressure: 1000 + Math.random() * 40,
    fishingScore: 60 + Math.random() * 40
  }));

  const response: ApiResponse<any> = {
    success: true,
    data: {
      location: { lat, lon },
      forecast: forecastData
    }
  };

  res.json(response);
}));

export default router; 