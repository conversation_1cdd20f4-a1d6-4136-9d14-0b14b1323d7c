import express from 'express';
import Joi from 'joi';
import { DatabaseService } from '../services/supabase';
import { authenticateSupabaseToken, optionalAuth } from '../middleware/auth';
import { asyncHandler } from '../utils/asyncHandler';
import { AppError } from '../utils/AppError';
import { ApiResponse } from '../types';

const router = express.Router();

// Validation schemas
const createSpotSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  description: Joi.string().max(500).optional(),
  location: Joi.object({
    latitude: Joi.number().min(-90).max(90).required(),
    longitude: Joi.number().min(-180).max(180).required(),
    address: Joi.string().optional(),
    city: Joi.string().optional(),
    country: Joi.string().optional()
  }).required(),
  spot_type: Joi.string().valid('fishing', 'marina', 'bait_shop', 'restaurant', 'accommodation').required(),
  access_type: Joi.string().valid('public', 'private', 'paid').optional(),
  difficulty: Joi.string().valid('Kolay', 'Orta', 'Zor').optional(),
  depth_min: Joi.number().min(0).optional(),
  depth_max: Joi.number().min(0).optional(),
  bottom_type: Joi.array().items(Joi.string()).optional(),
  facilities: Joi.array().items(Joi.string()).optional(),
  fish_species: Joi.array().items(Joi.number()).optional(),
  image_url: Joi.string().uri().optional(),
  images: Joi.array().items(Joi.string().uri()).max(5).optional()
});

const createSpotReviewSchema = Joi.object({
  rating: Joi.number().integer().min(1).max(5).required(),
  title: Joi.string().max(100).optional(),
  content: Joi.string().max(1000).optional(),
  accessibility_rating: Joi.number().integer().min(1).max(5).optional(),
  facilities_rating: Joi.number().integer().min(1).max(5).optional(),
  fish_diversity_rating: Joi.number().integer().min(1).max(5).optional()
});

const searchSpotsSchema = Joi.object({
  query: Joi.string().min(1).max(100).optional(),
  spot_type: Joi.string().valid('fishing', 'marina', 'bait_shop', 'restaurant', 'accommodation').optional(),
  location: Joi.string().max(100).optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(50).default(10)
});

// GET /api/spots/search - Search spots (MUST BE FIRST!)
router.get('/search', optionalAuth, asyncHandler(async (req, res) => {
  const { error, value } = searchSpotsSchema.validate(req.query);
  if (error) {
    throw new AppError(error.details[0].message, 400);
  }

  const { query, spot_type, location, page, limit } = value;
  
  if (!query) {
    throw new AppError('Search query is required', 400);
  }
  
  // Kullanıcı ID'sini al (eğer giriş yapmışsa)
  const userId = (req.user as any)?.id;

  const result = await DatabaseService.searchSpots(query, page, limit, userId);

  const response: ApiResponse = {
    success: true,
    data: {
      items: result.data,
      total: result.count || 0,
      page,
      limit,
      hasMore: (page * limit) < (result.count || 0)
    }
  };

  res.json(response);
}));

// GET /api/spots - Get all spots with pagination and filters
router.get('/', optionalAuth, asyncHandler(async (req, res) => {
  const { error, value } = searchSpotsSchema.validate(req.query);
  if (error) {
    throw new AppError(error.details[0].message, 400);
  }

  const { query, spot_type, location, page, limit } = value;
  
  // Kullanıcı ID'sini al (eğer giriş yapmışsa)
  const userId = (req.user as any)?.id;

  let result;
  if (query) {
    result = await DatabaseService.searchSpots(query, page, limit, userId);
  } else {
    const filters = { spot_type, location };
    result = await DatabaseService.getSpots(page, limit, filters, userId);
  }

  const response: ApiResponse = {
    success: true,
    data: {
      items: result.data,
      total: result.count || 0,
      page,
      limit,
      hasMore: (page * limit) < (result.count || 0)
    }
  };

  res.json(response);
}));

// GET /api/spots/:id - Get single spot by ID with reviews
router.get('/:id', optionalAuth, asyncHandler(async (req, res) => {
  const spotId = parseInt(req.params.id);
  if (isNaN(spotId)) {
    throw new AppError('Invalid spot ID', 400);
  }

  const spot = await DatabaseService.getSpotById(spotId);
  if (!spot) {
    throw new AppError('Spot not found', 404);
  }

  // Get recent reviews
  const reviews = await DatabaseService.getSpotReviews(spotId, 1, 5);
  spot.recent_reviews = reviews.data;
  spot.total_reviews = reviews.count;

  // Check if user has favorited this spot
  if (req.userId) {
    spot.is_favorited = await DatabaseService.isSpotFavorited(req.userId, spotId);
    spot.user_has_reviewed = await DatabaseService.hasUserReviewedSpot(spotId, req.userId);
  }

  const response: ApiResponse = {
    success: true,
    data: spot
  };

  res.json(response);
}));

// POST /api/spots - Create new spot (requires authentication)
router.post('/', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const { error, value } = createSpotSchema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400);
  }

  // Private spot oluşturma premium özelliği kontrolü
  if (value.access_type === 'private') {
    const user = await DatabaseService.getUserById(req.userId!);
    const isPremium = user?.is_pro && (!user.pro_until || new Date(user.pro_until) > new Date());
    
    if (!isPremium) {
      throw new AppError('Private spot oluşturma özelliği sadece premium üyeler için geçerlidir. Premium üyeliğe geçin!', 403);
    }
  }

  const spotData = {
    ...value,
    user_id: req.userId!
  };

  const spot = await DatabaseService.createSpot(spotData);

  const response: ApiResponse = {
    success: true,
    data: spot,
    message: value.access_type === 'private' 
      ? 'Private spot başarıyla oluşturuldu! (Premium özellik)' 
      : 'Spot created successfully and sent for approval'
  };

  res.status(201).json(response);
}));

// GET /api/spots/:id/reviews - Get spot reviews
router.get('/:id/reviews', asyncHandler(async (req, res) => {
  const spotId = parseInt(req.params.id);
  if (isNaN(spotId)) {
    throw new AppError('Invalid spot ID', 400);
  }

  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);

  const result = await DatabaseService.getSpotReviews(spotId, page, limit);

  const response: ApiResponse = {
    success: true,
    data: {
      reviews: result.data,
      pagination: {
        page,
        limit,
        total: result.count || 0,
        totalPages: Math.ceil((result.count || 0) / limit),
        hasNext: (page * limit) < (result.count || 0),
        hasPrev: page > 1
      }
    }
  };

  res.json(response);
}));

// POST /api/spots/:id/reviews - Create spot review
router.post('/:id/reviews', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const spotId = parseInt(req.params.id);
  if (isNaN(spotId)) {
    throw new AppError('Invalid spot ID', 400);
  }

  const { error, value } = createSpotReviewSchema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400);
  }

  // Check if spot exists
  const spot = await DatabaseService.getSpotById(spotId);
  if (!spot) {
    throw new AppError('Spot not found', 404);
  }

  // Check if user has already reviewed this spot
  const hasReviewed = await DatabaseService.hasUserReviewedSpot(spotId, req.userId!);
  if (hasReviewed) {
    throw new AppError('You have already reviewed this spot', 400);
  }

  const reviewData = {
    ...value,
    spot_id: spotId,
    user_id: req.userId!
  };

  const review = await DatabaseService.createSpotReview(reviewData);

  // Create notification for spot owner
  if (spot.user_id !== req.userId) {
    const user = await DatabaseService.getUserById(req.userId!);
    await DatabaseService.createNotification({
      user_id: spot.user_id,
      type: 'spot_review',
      title: 'Yeni Spot Değerlendirmesi',
      content: `${user?.full_name || 'Bir kullanıcı'} spotunuzu değerlendirdi!`,
      data: { spot_id: spotId, review_id: review.id },
      action_url: `/spots/${spotId}`
    });
  }

  const response: ApiResponse = {
    success: true,
    data: review,
    message: 'Review created successfully'
  };

  res.status(201).json(response);
}));

// POST /api/spots/:id/favorite - Favorite a spot
router.post('/:id/favorite', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const spotId = parseInt(req.params.id);
  if (isNaN(spotId)) {
    throw new AppError('Invalid spot ID', 400);
  }

  const spot = await DatabaseService.getSpotById(spotId);
  if (!spot) {
    throw new AppError('Spot not found', 404);
  }

  const isAlreadyFavorited = await DatabaseService.isSpotFavorited(req.userId!, spotId);
  if (isAlreadyFavorited) {
    throw new AppError('Spot already favorited', 400);
  }

  await DatabaseService.favoriteSpot(req.userId!, spotId);

  const response: ApiResponse = {
    success: true,
    message: 'Spot favorited successfully'
  };

  res.json(response);
}));

// DELETE /api/spots/:id/favorite - Unfavorite a spot
router.delete('/:id/favorite', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const spotId = parseInt(req.params.id);
  if (isNaN(spotId)) {
    throw new AppError('Invalid spot ID', 400);
  }

  const isFavorited = await DatabaseService.isSpotFavorited(req.userId!, spotId);
  if (!isFavorited) {
    throw new AppError('Spot not favorited', 400);
  }

  await DatabaseService.unfavoriteSpot(req.userId!, spotId);

  const response: ApiResponse = {
    success: true,
    message: 'Spot unfavorited successfully'
  };

  res.json(response);
}));

// GET /api/spots/:id/posts - Get posts from a specific spot
router.get('/:id/posts', optionalAuth, asyncHandler(async (req, res) => {
  const spotId = parseInt(req.params.id);
  if (isNaN(spotId)) {
    throw new AppError('Invalid spot ID', 400);
  }

  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);

  const result = await DatabaseService.getPosts(page, limit, { spotId });

  const response: ApiResponse = {
    success: true,
    data: result
  };

  res.json(response);
}));

export default router; 