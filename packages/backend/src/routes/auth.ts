import { Router, Request, Response } from 'express';
import { supabase } from '../services/supabase';
import { async<PERSON>and<PERSON>, AppError } from '../middleware/errorHandler';
import { authRateLimiterMiddleware } from '../middleware/rateLimiter';
import { LoginRequest, RegisterRequest, ApiResponse, AuthResponse } from '../types';
import Jo<PERSON> from 'joi';
import passport from '../config/passport';
import jwt from 'jsonwebtoken';
import { DatabaseService } from '../services/supabase';
import { SECURITY_CONFIG } from '../config';

const router = Router();

// Validation schemas
const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required()
});

const registerSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required(),
  username: Joi.string().alphanum().min(3).max(30).optional(),
  full_name: Joi.string().min(2).max(100).optional()
});

// POST /api/auth/register
router.post('/register', authRateLimiterMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { error, value } = registerSchema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400, 'VALIDATION_ERROR');
  }

  const { email, password, username, full_name }: RegisterRequest = value;

  // Register user with Supabase Auth
  const { data: authData, error: authError } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        username,
        full_name
      }
    }
  });

  if (authError) {
    throw new AppError(authError.message, 400, 'REGISTRATION_FAILED');
  }

  if (!authData.user) {
    throw new AppError('Registration failed', 400, 'REGISTRATION_FAILED');
  }

  // Create user profile in database
  const { data: userProfile, error: profileError } = await supabase
    .from('users')
    .insert({
      id: authData.user.id,
      email: authData.user.email,
      username,
      full_name,
      created_at: new Date().toISOString()
    })
    .select()
    .single();

  if (profileError) {
    console.error('Profile creation error:', profileError);
    // Don't throw error here, user is already created in auth
  }

  const response: ApiResponse<AuthResponse> = {
    success: true,
    message: 'Registration successful',
    data: {
      success: true,
      data: {
        user: userProfile || {
          id: authData.user.id,
          email: authData.user.email!,
          username,
          full_name,
          created_at: new Date().toISOString()
        },
        token: authData.session?.access_token
      },
      message: 'Registration successful'
    }
  };

  res.status(201).json(response);
}));

// POST /api/auth/login
router.post('/login', authRateLimiterMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { error, value } = loginSchema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400, 'VALIDATION_ERROR');
  }

  const { email, password }: LoginRequest = value;

  // Sign in with Supabase Auth
  const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
    email,
    password
  });

  if (authError) {
    throw new AppError('Invalid credentials', 401, 'INVALID_CREDENTIALS');
  }

  if (!authData.user || !authData.session) {
    throw new AppError('Login failed', 401, 'LOGIN_FAILED');
  }

  // Get user profile
  const { data: userProfile, error: profileError } = await supabase
    .from('users')
    .select('*')
    .eq('id', authData.user.id)
    .single();

  if (profileError) {
    // Create profile if it doesn't exist
    const { data: newProfile } = await supabase
      .from('users')
      .insert({
        id: authData.user.id,
        email: authData.user.email,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    const response: ApiResponse<AuthResponse> = {
      success: true,
      message: 'Login successful',
      data: {
        success: true,
        data: {
          user: newProfile || {
            id: authData.user.id,
            email: authData.user.email!,
            created_at: new Date().toISOString()
          },
          token: authData.session.access_token
        },
        message: 'Login successful'
      }
    };

    return res.json(response);
  }

  const response: ApiResponse<AuthResponse> = {
    success: true,
    message: 'Login successful',
    data: {
      success: true,
      data: {
        user: userProfile,
        token: authData.session.access_token
      },
      message: 'Login successful'
    }
  };

  res.json(response);
}));

// POST /api/auth/logout/web
router.post('/logout/web', asyncHandler(async (req: Request, res: Response) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];
  if (token) {
    await supabase.auth.signOut();
  }
  const response: ApiResponse = {
    success: true,
    message: 'Web logout successful'
  };
  res.json(response);
}));

// POST /api/auth/logout/mobile
router.post('/logout/mobile', asyncHandler(async (req: Request, res: Response) => {
  // Mobile logout - sadece başarılı response döndür
  const response: ApiResponse = {
    success: true,
    message: 'Mobile logout successful'
  };
  res.json(response);
}));

// POST /api/auth/refresh
router.post('/refresh', asyncHandler(async (req: Request, res: Response) => {
  const { refresh_token } = req.body;

  if (!refresh_token) {
    throw new AppError('Refresh token required', 400, 'REFRESH_TOKEN_REQUIRED');
  }

  const { data, error } = await supabase.auth.refreshSession({
    refresh_token
  });

  if (error) {
    throw new AppError('Invalid refresh token', 401, 'INVALID_REFRESH_TOKEN');
  }

  // Kullanıcıyı bul ve yeni JWT üret
  let user = null;
  if (data.session?.user) {
    user = await DatabaseService.getUserById(data.session.user.id);
  }
  let jwtToken = null;
  if (user) {
    jwtToken = generateJWT(user);
  }

  const response: ApiResponse = {
    success: true,
    message: 'Token refreshed successfully',
    data: {
      access_token: data.session?.access_token,
      refresh_token: data.session?.refresh_token,
      expires_at: data.session?.expires_at,
      jwt: jwtToken
    }
  };

  res.json(response);
}));

// Helper function to generate JWT token
const generateJWT = (user: any) => {
  if (!process.env.JWT_SECRET) throw new Error('JWT_SECRET env not set');
  return jwt.sign(
    { userId: user.id, email: user.email, provider: user.provider, isPremium: user.is_premium },
    process.env.JWT_SECRET,
    { expiresIn: '7d' }
  );
};

// Google OAuth Routes
router.get('/google',
  passport.authenticate('google', { scope: ['profile', 'email'] })
);

router.get('/google/callback',
  passport.authenticate('google', { session: false }),
  asyncHandler(async (req: Request, res: Response) => {
    const user = req.user as any;

    if (!user) {
      return res.redirect(`${process.env['WEB_URL']}/login?error=oauth_failed`);
    }

    // Generate JWT token
    const token = generateJWT(user);

    // Redirect to frontend with token
    res.redirect(`${process.env['WEB_URL']}/auth/callback?token=${token}&provider=google`);
  })
);

// Facebook OAuth Routes
router.get('/facebook',
  passport.authenticate('facebook', { scope: ['email'] })
);

router.get('/facebook/callback',
  passport.authenticate('facebook', { session: false }),
  asyncHandler(async (req: Request, res: Response) => {
    const user = req.user as any;

    if (!user) {
      return res.redirect(`${process.env['WEB_URL']}/login?error=oauth_failed`);
    }

    // Generate JWT token
    const token = generateJWT(user);

    // Redirect to frontend with token
    res.redirect(`${process.env['WEB_URL']}/auth/callback?token=${token}&provider=facebook`);
  })
);

// Mobile OAuth endpoint - OAuth code'u JWT token'a çevir
router.post('/mobile-oauth', authRateLimiterMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { code, provider, redirectUri } = req.body;

  if (!code || !provider || !redirectUri) {
    throw new AppError('Missing required fields: code, provider, redirectUri', 400);
  }

  if (!['google', 'facebook'].includes(provider)) {
    throw new AppError('Invalid provider. Only google and facebook are supported', 400);
  }

  try {
    let userProfile: any = null;

    if (provider === 'google') {
      // Google OAuth code'u token'a çevir
      const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
client_secret: process.env.GOOGLE_CLIENT_SECRET,
          code,
          grant_type: 'authorization_code',
          redirect_uri: redirectUri,
        }),
      });

      if (!tokenResponse.ok) {
        throw new AppError('Failed to exchange Google code for token', 400);
      }

      const tokenData = await tokenResponse.json();
      
      // Google user info al
      const userResponse = await fetch(`https://www.googleapis.com/oauth2/v2/userinfo?access_token=${tokenData.access_token}`);
      
      if (!userResponse.ok) {
        throw new AppError('Failed to get Google user info', 400);
      }

      const googleUser = await userResponse.json();
      userProfile = {
        id: googleUser.id,
        email: googleUser.email,
        name: googleUser.name,
        picture: googleUser.picture,
        provider: 'google'
      };
    } else if (provider === 'facebook') {
      // Facebook OAuth code'u token'a çevir
      const tokenResponse = await fetch('https://graph.facebook.com/v13.0/oauth/access_token', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // Facebook API call'ları buraya eklenecek
      userProfile = {
        id: 'facebook_temp',
        email: '<EMAIL>',
        name: 'Facebook User',
        picture: '',
        provider: 'facebook'
      };
    }

    // Kullanıcıyı database'de kontrol et veya oluştur
    const { data: existingUser, error: findError } = await supabase
      .from('users')
      .select('*')
      .eq('email', userProfile.email)
      .single();

    let user;
    if (existingUser && !findError) {
      // Kullanıcı zaten var, OAuth bilgilerini güncelle
      const updateData: any = {
        avatar_url: userProfile.picture,
        updated_at: new Date().toISOString()
      };

      if (provider === 'google') {
        updateData.google_id = userProfile.id;
      } else if (provider === 'facebook') {
        updateData.facebook_id = userProfile.id;
      }

      const { data: updatedUser, error: updateError } = await supabase
        .from('users')
        .update(updateData)
        .eq('id', existingUser.id)
        .select()
        .single();

      if (updateError) {
        throw new AppError('Failed to update user', 500);
      }

      user = updatedUser;
    } else {
      // Yeni kullanıcı oluştur
      const newUser: any = {
        id: `${provider}_${userProfile.id}`,
        email: userProfile.email,
        username: userProfile.name?.toLowerCase().replace(/\s+/g, '_') || `user_${userProfile.id}`,
        full_name: userProfile.name || '',
        avatar_url: userProfile.picture || '',
        provider,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      if (provider === 'google') {
        newUser.google_id = userProfile.id;
      } else if (provider === 'facebook') {
        newUser.facebook_id = userProfile.id;
      }

      const { data: createdUser, error: createError } = await supabase
        .from('users')
        .insert(newUser)
        .select()
        .single();

      if (createError) {
        throw new AppError('Failed to create user', 500);
      }

      user = createdUser;
    }

    // JWT token oluştur
    const token = generateJWT(user);

    const response: AuthResponse = {
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          full_name: user.full_name,
          avatar_url: user.avatar_url,
          provider: user.provider,
          created_at: user.created_at
        },
        token
      },
      message: 'Mobile OAuth login successful'
    };

    res.json(response);
  } catch (error) {
    console.error('Mobile OAuth error:', error);
    throw new AppError('OAuth authentication failed', 500);
  }
}));

// POST /auth/google/mobile - Mobile Google OAuth
router.post('/google/mobile', asyncHandler(async (req, res) => {
  const { accessToken, idToken } = req.body;

  if (!accessToken || !idToken) {
    throw new AppError('Access token and ID token are required', 400);
  }

  try {
    // Verify Google idToken
    const tokenInfoRes = await fetch(`https://oauth2.googleapis.com/tokeninfo?id_token=${idToken}`);
    if (!tokenInfoRes.ok) {
      throw new AppError('Invalid Google idToken', 401);
    }
    const tokenInfo = await tokenInfoRes.json();

    // Verify Google accessToken
    const googleResponse = await fetch(`https://www.googleapis.com/oauth2/v1/userinfo?access_token=${accessToken}`);
    if (!googleResponse.ok) {
      throw new AppError('Invalid Google accessToken', 401);
    }
    const googleUser = await googleResponse.json();

    // idToken sub ile accessToken'dan gelen id aynı mı kontrol et
    if (tokenInfo.sub !== googleUser.id) {
      throw new AppError('Google token mismatch', 401);
    }

    // Check if user exists (email ile kontrol)
    let user = await DatabaseService.getUserByEmail(googleUser.email);
    if (!user) {
      // Create new user
      const newUser = {
        id: `google_${googleUser.id}`,
        email: googleUser.email,
        username: googleUser.email.split('@')[0],
        full_name: googleUser.name,
        avatar_url: googleUser.picture,
        is_premium: false,
        total_catches: 0,
        total_spots: 0,
        reputation_score: 0,
        provider: 'google',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      user = await DatabaseService.createUser(newUser);
    }

    // Generate JWT token
    const token = generateJWT(user);

    const response: ApiResponse<any> = {
      success: true,
      data: {
        user,
        token,
        message: 'Google authentication successful'
      }
    };

    res.json(response);
  } catch (error) {
    console.error('Google mobile auth error:', error);
    throw new AppError('Google authentication failed', 500);
  }
}));

const OAUTH_CONFIGS = {
  google: {
    tokeninfo: 'https://oauth2.googleapis.com/tokeninfo',
    userinfo: 'https://www.googleapis.com/oauth2/v2/userinfo',
    token: 'https://oauth2.googleapis.com/token',
  },
  facebook: {
    token: 'https://graph.facebook.com/v13.0/oauth/access_token',
    userinfo: 'https://graph.facebook.com/me?fields=id,name,email,picture',
  }
};

function devLog(...args: any[]) {
  if (process.env.NODE_ENV !== 'production') {
    console.log('[DEV]', ...args);
  }
}

export default router;
