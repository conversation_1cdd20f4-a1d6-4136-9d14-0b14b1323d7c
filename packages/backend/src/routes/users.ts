import { Router } from 'express';
import { DatabaseService } from '../services/supabase';
import { asyncHand<PERSON>, AppError } from '../middleware/errorHandler';
import { authenticateSupabaseToken } from '../middleware/auth';
import { ApiResponse, User } from '../types';
import Joi from 'joi';

const router = Router();

// Validation schema for user updates
const updateUserSchema = Joi.object({
  username: Joi.string().alphanum().min(3).max(30).optional(),
  full_name: Joi.string().min(2).max(100).optional(),
  avatar_url: Joi.string().uri().optional(),
  equipments: Joi.array().items(
    Joi.object({
      id: Joi.string().required(),
      name: Joi.string().required(),
      brand: Joi.string().optional(),
      type: Joi.string().optional(),
      description: Joi.string().optional()
    })
  ).optional()
});

// GET /api/users/me - Get current user profile
router.get('/me', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const response: ApiResponse<User> = {
    success: true,
    data: req.user!
  };

  res.json(response);
}));

// PUT /api/users/me - Update current user profile
router.put('/me', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const { error, value } = updateUserSchema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400, 'VALIDATION_ERROR');
  }

  const updatedUser = await DatabaseService.updateUser(req.userId!, value);

  const response: ApiResponse<User> = {
    success: true,
    data: updatedUser,
    message: 'Profile updated successfully'
  };

  res.json(response);
}));

// GET /api/users/:id - Get user profile by ID
router.get('/:id', asyncHandler(async (req, res) => {
  const userId = req.params.id;

  const user = await DatabaseService.getUserById(userId);

  if (!user) {
    throw new AppError('User not found', 404, 'USER_NOT_FOUND');
  }

  // Remove sensitive information for public profile
  const publicUser = {
    id: user.id,
    username: user.username,
    full_name: user.full_name,
    avatar_url: user.avatar_url,
    created_at: user.created_at
  };

  const response: ApiResponse<Partial<User>> = {
    success: true,
    data: publicUser
  };

  res.json(response);
}));

// GET /api/users/me/equipment - Get current user's equipment
router.get('/me/equipment', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const equipment = await DatabaseService.getUserEquipment(req.userId!);

  const response: ApiResponse<any[]> = {
    success: true,
    data: equipment
  };

  res.json(response);
}));

// POST /api/users/me/equipment - Add equipment to user
router.post('/me/equipment', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const equipmentData = {
    user_id: req.userId!,
    ...req.body
  };

  const equipment = await DatabaseService.addUserEquipment(equipmentData);

  const response: ApiResponse<any> = {
    success: true,
    data: equipment,
    message: 'Equipment added successfully'
  };

  res.status(201).json(response);
}));

// GET /api/users/me/blocked - Get blocked users
router.get('/me/blocked', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);

  const result = await DatabaseService.getBlockedUsers(req.userId!, page, limit);

  const response: ApiResponse<any> = {
    success: true,
    data: {
      items: result.data,
      total: result.count || 0,
      page,
      limit,
      hasMore: (page * limit) < (result.count || 0)
    }
  };

  res.json(response);
}));

// POST /api/users/block/:userId - Block a user
router.post('/block/:userId', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const targetUserId = req.params.userId;

  if (targetUserId === req.userId) {
    throw new AppError('Cannot block yourself', 400, 'INVALID_OPERATION');
  }

  await DatabaseService.blockUser(req.userId!, targetUserId);

  const response: ApiResponse<any> = {
    success: true,
    message: 'User blocked successfully'
  };

  res.json(response);
}));

// DELETE /api/users/block/:userId - Unblock a user
router.delete('/block/:userId', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const targetUserId = req.params.userId;

  await DatabaseService.unblockUser(req.userId!, targetUserId);

  const response: ApiResponse<any> = {
    success: true,
    message: 'User unblocked successfully'
  };

  res.json(response);
}));

// GET /api/users/:id/catches - Get user's catch posts
router.get('/:id/catches', asyncHandler(async (req, res) => {
  const userId = req.params.id;
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);

  const result = await DatabaseService.getPosts(page, limit, { userId });

  const response: ApiResponse<any> = {
    success: true,
    data: {
      items: result.items,
      total: result.total,
      page: result.page,
      limit: result.limit,
      hasMore: result.hasMore
    }
  };

  res.json(response);
}));

// GET /api/users/me/catches - Get current user's catch posts
router.get('/me/catches', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);

  const result = await DatabaseService.getPosts(page, limit, { userId: req.userId! });

  const response: ApiResponse<any> = {
    success: true,
    data: {
      items: result.items,
      total: result.total,
      page: result.page,
      limit: result.limit,
      hasMore: result.hasMore
    }
  };

  res.json(response);
}));

// GET /api/users/me/locations - Get current user's saved weather locations
router.get('/me/locations', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const locations = await DatabaseService.getUserLocations(req.userId!);

  const response: ApiResponse<any[]> = {
    success: true,
    data: locations
  };

  res.json(response);
}));

export default router;
