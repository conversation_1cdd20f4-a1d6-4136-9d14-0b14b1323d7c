import express, { Request, Response } from 'express';
import Jo<PERSON> from 'joi';
import { DatabaseService } from '../services/supabase';
import { authenticateSupabaseToken } from '../middleware/auth';
import { asyncHandler } from '../utils/asyncHandler';
import { AppError } from '../utils/AppError';
import { ApiResponse } from '../types';

const router = express.Router();

// Validation schemas
const followUserSchema = Joi.object({
  following_id: Joi.string().uuid().required()
});

const paginationSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(50).default(10)
});

// POST /api/follows - Follow a user
router.post('/', authenticateSupabaseToken, asyncHandler(async (req: Request, res: Response) => {
  const { error, value } = followUserSchema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400);
  }

  const { following_id } = value;
  const follower_id = (req.user as any)!.id;

  if (follower_id === following_id) {
    throw new AppError('You cannot follow yourself', 400);
  }

  // Check if already following
  const isAlreadyFollowing = await DatabaseService.isFollowing(follower_id, following_id);
  if (isAlreadyFollowing) {
    throw new AppError('You are already following this user', 400);
  }

  const follow = await DatabaseService.followUser(follower_id, following_id);

  const response: ApiResponse = {
    success: true,
    data: follow,
    message: 'User followed successfully'
  };

  res.status(201).json(response);
}));

// DELETE /api/follows/:userId - Unfollow a user
router.delete('/:userId', authenticateSupabaseToken, asyncHandler(async (req: Request, res: Response) => {
  const following_id = req.params.userId;
  const follower_id = (req.user as any)!.id;

  if (follower_id === following_id) {
    throw new AppError('You cannot unfollow yourself', 400);
  }

  // Check if following
  const isFollowing = await DatabaseService.isFollowing(follower_id, following_id);
  if (!isFollowing) {
    throw new AppError('You are not following this user', 400);
  }

  await DatabaseService.unfollowUser(follower_id, following_id);

  const response: ApiResponse = {
    success: true,
    message: 'User unfollowed successfully'
  };

  res.json(response);
}));

// GET /api/follows/:userId/followers - Get user's followers
router.get('/:userId/followers', asyncHandler(async (req: Request, res: Response) => {
  const { error, value } = paginationSchema.validate(req.query);
  if (error) {
    throw new AppError(error.details[0].message, 400);
  }

  const { page, limit } = value;
  const userId = req.params.userId;

  const result = await DatabaseService.getFollowers(userId, page, limit);

  const response: ApiResponse = {
    success: true,
    data: {
      followers: result.data,
      pagination: {
        page,
        limit,
        total: result.count || 0,
        totalPages: Math.ceil((result.count || 0) / limit),
        hasNext: (page * limit) < (result.count || 0),
        hasPrev: page > 1
      }
    }
  };

  res.json(response);
}));

// GET /api/follows/:userId/following - Get users that this user is following
router.get('/:userId/following', asyncHandler(async (req: Request, res: Response) => {
  const { error, value } = paginationSchema.validate(req.query);
  if (error) {
    throw new AppError(error.details[0].message, 400);
  }

  const { page, limit } = value;
  const userId = req.params.userId;

  const result = await DatabaseService.getFollowing(userId, page, limit);

  const response: ApiResponse = {
    success: true,
    data: {
      following: result.data,
      pagination: {
        page,
        limit,
        total: result.count || 0,
        totalPages: Math.ceil((result.count || 0) / limit),
        hasNext: (page * limit) < (result.count || 0),
        hasPrev: page > 1
      }
    }
  };

  res.json(response);
}));

// GET /api/follows/check/:userId - Check if current user is following another user
router.get('/check/:userId', authenticateSupabaseToken, asyncHandler(async (req: Request, res: Response) => {
  const following_id = req.params.userId;
  const follower_id = (req.user as any)!.id;

  const isFollowing = await DatabaseService.isFollowing(follower_id, following_id);

  const response: ApiResponse = {
    success: true,
    data: { isFollowing }
  };

  res.json(response);
}));

export default router; 