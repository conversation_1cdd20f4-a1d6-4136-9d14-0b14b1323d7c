import { Router } from 'express';
import multer from 'multer';
import path from 'path';
import { authenticateSupabaseToken } from '../middleware/auth';
import { uploadRateLimiterMiddleware } from '../middleware/rateLimiter';
import { asyncHandler, AppError } from '../middleware/errorHandler';
import { ApiResponse } from '../types';
import r2StorageService from '../services/r2Storage';
import { r2Service } from '../services/cloudflareR2';

const router = Router();

// Configure multer for memory storage (for Cloudflare Images)
const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Check file type
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed!'));
  }
};

const upload = multer({
  storage: multer.memoryStorage(), // Store in memory for Cloudflare upload
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE || '5242880'), // 5MB for high-quality fish photos
    files: 1
  }
});

// Cloudflare Images endpoint removed - using R2 instead

// POST /api/upload/r2 - Upload to R2 Storage
router.post('/r2', 
  authenticateSupabaseToken,
  uploadRateLimiterMiddleware,
  upload.single('image'),
  asyncHandler(async (req, res) => {
    if (!req.file) {
      throw new AppError('No file uploaded', 400, 'NO_FILE_UPLOADED');
    }

    // Get user ID from authenticated token
    const userId = (req as any).user?.id;
    if (!userId) {
      throw new AppError('User not authenticated', 401, 'UNAUTHORIZED');
    }

    // Get upload metadata from request body
    const { type, fishSpecies, spotName } = req.body;
    const uploadType = type || 'catch'; // Default to catch if not specified

    try {
      // Generate unique file key
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(2, 15);
      const fileExtension = req.file.originalname.split('.').pop() || 'jpg';
      const key = `${uploadType}/${userId}/${timestamp}_${randomId}.${fileExtension}`;

      // Upload to R2 Storage
      console.log('🔄 Starting R2 upload via route...');
      const uploadResult = await r2StorageService.uploadFile(
        req.file.buffer,
        key,
        req.file.mimetype
      );
      console.log('📊 Upload result:', uploadResult);

      if (!uploadResult.success) {
        throw new AppError(uploadResult.error || 'Failed to upload to R2', 500, 'UPLOAD_FAILED');
      }

      const response: ApiResponse<{
        url: string;
        key: string;
        originalFilename: string;
        size: number;
        uploadType: string;
      }> = {
        success: true,
        message: 'File uploaded successfully to R2 Storage',
        data: {
          url: uploadResult.url!,
          key: uploadResult.key!,
          originalFilename: req.file.originalname,
          size: req.file.size,
          uploadType
        }
      };

      res.json(response);

    } catch (error) {
      console.error('❌ Error in R2 upload:', error);
      throw new AppError('Failed to upload to R2', 500, 'UPLOAD_FAILED');
    }
  })
);
  
// Configure multer for test endpoint (no file filter)
const testUpload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE || '5242880'),
    files: 1
  }
});

// POST /api/upload/r2-test - Test R2 upload without authentication (development only)
router.post('/r2-test', 
  uploadRateLimiterMiddleware,
  testUpload.single('image'),
  asyncHandler(async (req, res) => {
    if (process.env.NODE_ENV === 'production') {
      throw new AppError('Test endpoint not available in production', 403, 'FORBIDDEN');
    }

    if (!req.file) {
      throw new AppError('No file uploaded', 400, 'NO_FILE_UPLOADED');
    }

    // Get upload metadata from request body
    const { type, fishSpecies, spotName } = req.body;
    const uploadType = type || 'catch'; // Default to catch if not specified

    try {
      // Generate unique file key
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(2, 15);
      const fileExtension = req.file.originalname.split('.').pop() || 'txt';
      const key = `catch/test-user/${timestamp}_${randomId}.${fileExtension}`;

      // Upload to R2 Storage  
      console.log('🔄 Starting R2 test upload via route...');
      const uploadResult = await r2StorageService.uploadFile(
        req.file.buffer,
        key,
        req.file.mimetype || 'application/octet-stream'
      );
      console.log('📊 Test upload result:', uploadResult);

      if (!uploadResult.success) {
        throw new AppError(uploadResult.error || 'Failed to upload to R2', 500, 'UPLOAD_FAILED');
      }

      const response: ApiResponse<{
        url: string;
        key: string;
        originalFilename: string;
        size: number;
        uploadType: string;
      }> = {
        success: true,
        message: 'File uploaded successfully to R2 Storage (TEST)',
        data: {
          url: uploadResult.url!,
          key: uploadResult.key!,
          originalFilename: req.file.originalname,
          size: req.file.size,
          uploadType
        }
      };

      res.json(response);

    } catch (error: any) {
      console.error('❌ Error in R2 test upload:', error);
      console.error('❌ Full error details:', JSON.stringify(error, null, 2));
      console.error('❌ Error stack:', error.stack);
      console.error('❌ R2 service check:', typeof r2StorageService);
      console.error('❌ R2 service methods:', Object.keys(r2StorageService));
      throw new AppError(`Failed to upload to R2: ${error.message}`, 500, 'UPLOAD_FAILED');
    }
  })
);

// Error handling for multer
router.use((error: any, req: any, res: any, next: any) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        error: 'File too large',
        message: 'File size exceeds the maximum allowed size'
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        error: 'Too many files',
        message: 'Only one file is allowed'
      });
    }
  }
  
  if (error.message === 'Only image files are allowed!') {
    return res.status(400).json({
      success: false,
      error: 'Invalid file type',
      message: 'Only image files are allowed'
    });
  }

  next(error);
});

interface UploadResponse {
  url: string;
  key: string;
  size: number;
  type: string;
}

// POST /api/upload/image - Tek görsel yükleme
router.post('/image', 
  authenticateSupabaseToken,
  upload.single('image'),
  asyncHandler(async (req, res) => {
    if (!req.file) {
      throw new AppError('Dosya bulunamadı', 400);
    }

    const userId = req.user!.id;
    const folder = `users/${userId}/images`;

    try {
      const result = await r2Service.uploadFile(
        req.file.buffer,
        req.file.originalname,
        folder,
        req.file.mimetype
      );

      const response: ApiResponse<UploadResponse> = {
        success: true,
        data: {
          url: result.url,
          key: result.key,
          size: req.file.size,
          type: req.file.mimetype
        }
      };

      res.json(response);
    } catch (error) {
      console.error('Image upload error:', error);
      throw new AppError('Görsel yüklenirken hata oluştu', 500);
    }
  })
);

// POST /api/upload/video - Video yükleme
router.post('/video',
  authenticateSupabaseToken,
  upload.single('video'),
  asyncHandler(async (req, res) => {
    if (!req.file) {
      throw new AppError('Video dosyası bulunamadı', 400);
    }

    const userId = req.user!.id;
    const folder = `users/${userId}/videos`;

    try {
      const result = await r2Service.uploadFile(
        req.file.buffer,
        req.file.originalname,
        folder,
        req.file.mimetype
      );

      const response: ApiResponse<UploadResponse> = {
        success: true,
        data: {
          url: result.url,
          key: result.key,
          size: req.file.size,
          type: req.file.mimetype
        }
      };

      res.json(response);
    } catch (error) {
      console.error('Video upload error:', error);
      throw new AppError('Video yüklenirken hata oluştu', 500);
    }
  })
);

// POST /api/upload/multiple - Çoklu dosya yükleme
router.post('/multiple',
  authenticateSupabaseToken,
  upload.array('files', 5), // Maksimum 5 dosya
  asyncHandler(async (req, res) => {
    const files = req.files as Express.Multer.File[];
    
    if (!files || files.length === 0) {
      throw new AppError('Dosya bulunamadı', 400);
    }

    const userId = req.user!.id;
    const folder = `users/${userId}/posts`;

    try {
      const uploadPromises = files.map(file => 
        r2Service.uploadFile(
          file.buffer,
          file.originalname,
          folder,
          file.mimetype
        )
      );

      const results = await Promise.all(uploadPromises);
      
      const uploadedFiles: UploadResponse[] = results.map((result, index) => ({
        url: result.url,
        key: result.key,
        size: files[index].size,
        type: files[index].mimetype
      }));

      const response: ApiResponse<UploadResponse[]> = {
        success: true,
        data: uploadedFiles
      };

      res.json(response);
    } catch (error) {
      console.error('Multiple upload error:', error);
      throw new AppError('Dosyalar yüklenirken hata oluştu', 500);
    }
  })
);

// DELETE /api/upload/:key - Dosya silme
router.delete('/:key(*)',
  authenticateSupabaseToken,
  asyncHandler(async (req, res) => {
    const key = req.params.key;
    const userId = req.user!.id;

    // Security check - kullanıcı sadece kendi dosyalarını silebilir
    if (!key.startsWith(`users/${userId}/`)) {
      throw new AppError('Bu dosyayı silme yetkiniz yok', 403);
    }

    try {
      await r2Service.deleteFile(key);

      const response: ApiResponse<{ message: string }> = {
        success: true,
        data: { message: 'Dosya başarıyla silindi' }
      };

      res.json(response);
    } catch (error) {
      console.error('Delete file error:', error);
      throw new AppError('Dosya silinirken hata oluştu', 500);
    }
  })
);

// GET /api/upload/presigned - Presigned URL al (frontend'den direkt yükleme için)
router.get('/presigned',
  authenticateSupabaseToken,
  asyncHandler(async (req, res) => {
    const { filename, contentType, folder = 'temp' } = req.query;
    
    if (!filename || !contentType) {
      throw new AppError('Filename ve contentType gerekli', 400);
    }

    const userId = req.user!.id;
    const key = `users/${userId}/${folder}/${filename}`;

    try {
      const uploadUrl = await r2Service.getUploadUrl(
        key,
        contentType as string,
        3600 // 1 saat
      );

      const response: ApiResponse<{ uploadUrl: string; key: string }> = {
        success: true,
        data: {
          uploadUrl,
          key
        }
      };

      res.json(response);
    } catch (error) {
      console.error('Presigned URL error:', error);
      throw new AppError('Upload URL oluşturulamadı', 500);
    }
  })
);

export default router;
