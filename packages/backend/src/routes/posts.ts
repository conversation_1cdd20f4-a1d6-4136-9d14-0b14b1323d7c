import { Router } from 'express';
import { DatabaseService } from '../services/supabase';
import { as<PERSON><PERSON><PERSON><PERSON>, AppError } from '../middleware/errorHandler';
import { authenticateSupabaseToken, optionalAuth } from '../middleware/auth';
import { CreatePostRequest, UpdatePostRequest, ApiResponse, PaginatedResponse, Post } from '../types';
import { UnitConversionService } from '../services/unitConversion';
import { UserPreferencesService } from '../services/userPreferences';

import Joi from 'joi';

const router = Router();

// Validation schemas
const createPostSchema = Joi.object({
  content: Joi.string().min(1).max(1000).required(),
  image_url: Joi.string().uri().optional(),
  images: Joi.array().items(Joi.string().uri()).max(5).optional(),
  location: Joi.object({
    latitude: Joi.number().min(-90).max(90).required(),
    longitude: Joi.number().min(-180).max(180).required(),
    address: Joi.string().optional(),
    city: Joi.string().optional(),
    country: Joi.string().optional()
  }).optional(),
  spot_id: Joi.number().integer().positive().optional(),
  trip_id: Joi.number().integer().positive().optional(),
  catch_details: Joi.object({
    species_id: Joi.number().integer().positive().optional(),
    species_name: Joi.string().optional(),
    weight: Joi.number().positive().optional(),
    length: Joi.number().positive().optional(),
    bait_used: Joi.string().optional(),
    technique: Joi.string().optional(),
    weather_conditions: Joi.string().optional(),
    water_temperature: Joi.number().optional(),
    time_of_day: Joi.string().valid('morning', 'afternoon', 'evening', 'night').optional(),
    equipment_used: Joi.array().items(Joi.string()).optional()
  }).optional()
});

const updatePostSchema = Joi.object({
  content: Joi.string().min(1).max(1000).optional(),
  image_url: Joi.string().uri().optional(),
  images: Joi.array().items(Joi.string().uri()).max(5).optional(),
  location: Joi.object({
    latitude: Joi.number().min(-90).max(90).required(),
    longitude: Joi.number().min(-180).max(180).required(),
    address: Joi.string().optional(),
    city: Joi.string().optional(),
    country: Joi.string().optional()
  }).optional(),
  spot_id: Joi.number().integer().positive().optional(),
  trip_id: Joi.number().integer().positive().optional(),
  catch_details: Joi.object({
    species_id: Joi.number().integer().positive().optional(),
    species_name: Joi.string().optional(),
    weight: Joi.number().positive().optional(),
    length: Joi.number().positive().optional(),
    bait_used: Joi.string().optional(),
    technique: Joi.string().optional(),
    weather_conditions: Joi.string().optional(),
    water_temperature: Joi.number().optional(),
    time_of_day: Joi.string().valid('morning', 'afternoon', 'evening', 'night').optional(),
    equipment_used: Joi.array().items(Joi.string()).optional()
  }).optional()
});

// 🧠 INSTAGRAM TARZI ALGORİTMA - FEED ENDPOİNTİ
// GET /api/posts/feed - Akıllı feed algoritması
router.get('/feed', optionalAuth, asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 15, 50); // İlk açılışta 15 post
  const userId = req.userId;

  // 🎯 ALGORİTMA PARAMETRELERİ
  const algorithmWeights = {
    recency: 0.3,        // Yenilik %30
    engagement: 0.25,    // Beğeni/yorum %25  
    following: 0.2,      // Takip edilen kişiler %20
    location: 0.15,      // Yakın lokasyon %15
    fishSize: 0.1        // Büyük balık %10
  };

  let feedQuery = `
    WITH user_follows AS (
      SELECT followed_id 
      FROM follows 
      WHERE follower_id = $1 AND status = 'accepted'
    ),
    post_scores AS (
      SELECT 
        p.*,
        u.full_name,
        u.avatar_url,
        u.username,
        
        -- 📊 SKOR HESAPLAMA
        (
          -- ⏰ Yenilik skoru (24 saat içinde max puan)
          CASE 
            WHEN p.created_at > NOW() - INTERVAL '1 hour' THEN 100
            WHEN p.created_at > NOW() - INTERVAL '6 hours' THEN 80
            WHEN p.created_at > NOW() - INTERVAL '24 hours' THEN 60
            WHEN p.created_at > NOW() - INTERVAL '3 days' THEN 40
            WHEN p.created_at > NOW() - INTERVAL '7 days' THEN 20
            ELSE 10
          END * ${algorithmWeights.recency}
          
          -- 👍 Engagement skoru
          + (p.likes_count * 2 + p.comments_count * 3) * ${algorithmWeights.engagement}
          
          -- 👥 Takip skoru
          + CASE 
            WHEN uf.followed_id IS NOT NULL THEN 100 
            ELSE 0 
          END * ${algorithmWeights.following}
          
          -- 📍 Lokasyon skoru (aynı şehir/ülke)
          + CASE 
            WHEN p.location IS NOT NULL THEN 50
            ELSE 0
          END * ${algorithmWeights.location}
          
          -- 🐟 Balık boyutu skoru
          + CASE 
            WHEN (p.catch_details->>'weight')::float > 5 THEN 100
            WHEN (p.catch_details->>'weight')::float > 2 THEN 70
            WHEN (p.catch_details->>'weight')::float > 1 THEN 40
            ELSE 20
          END * ${algorithmWeights.fishSize}
          
        ) as algorithm_score,
        
        -- 🏆 Özel etiketler
        CASE 
          WHEN (p.catch_details->>'weight')::float > 5 THEN 'big_catch'
          WHEN p.likes_count > 50 THEN 'viral'
          WHEN p.created_at > NOW() - INTERVAL '1 hour' THEN 'fresh'
          ELSE null
        END as special_tag
        
      FROM posts p
      JOIN users u ON p.user_id = u.id
      LEFT JOIN user_follows uf ON p.user_id = uf.followed_id
      WHERE p.status = 'active'
      AND p.created_at > NOW() - INTERVAL '30 days' -- Son 30 gün
    )
    SELECT *
    FROM post_scores
    ORDER BY 
      -- 🎯 Ana algoritma: Skor + randomizasyon
      algorithm_score DESC,
      RANDOM() * 0.1 -- Küçük randomizasyon ekle
    LIMIT $2 OFFSET $3
  `;

  // Basit algoritma: Yeni + popüler postları karıştır
  const result = await DatabaseService.getPosts(page, limit, {});
  
  // Postları algoritma skoruna göre sırala
  const scoredPosts = result.items.map((post: any) => {
    let score = 0;
    
    // Yenilik skoru
    const hoursAgo = (Date.now() - new Date(post.created_at).getTime()) / (1000 * 60 * 60);
    if (hoursAgo < 1) score += 100;
    else if (hoursAgo < 6) score += 80;
    else if (hoursAgo < 24) score += 60;
    else score += 20;
    
    // Engagement skoru
    score += (post.likes_count * 2 + post.comments_count * 3);
    
    // Balık boyutu skoru
    const weight = post.catch_details?.weight || 0;
    if (weight > 5) score += 50;
    else if (weight > 2) score += 30;
    else if (weight > 1) score += 15;
    
    return { ...post, algorithm_score: score };
  });
  
  // Skora göre sırala
  scoredPosts.sort((a: any, b: any) => b.algorithm_score - a.algorithm_score);

  // 📊 Feed istatistikleri
  const feedStats = {
    totalPosts: scoredPosts.length,
    freshPosts: scoredPosts.filter((p: any) => {
      const hoursAgo = (Date.now() - new Date(p.created_at).getTime()) / (1000 * 60 * 60);
      return hoursAgo < 1;
    }).length,
    bigCatches: scoredPosts.filter((p: any) => (p.catch_details?.weight || 0) > 5).length,
    viralPosts: scoredPosts.filter((p: any) => p.likes_count > 10).length,
    followingPosts: 0 // TODO: Takip sistemi eklenince
  };

  const response: ApiResponse<any> = {
    success: true,
    data: {
      items: scoredPosts,
      total: scoredPosts.length,
      page,
      limit,
      hasMore: false, // Feed için always false
      algorithm: 'smart_feed',
      stats: feedStats
    }
  };

  res.json(response);
}));

// GET /api/posts - Get all posts with pagination and enhanced relationships
router.get('/', optionalAuth, asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);
  const userId = req.query.user_id as string;
  const spotId = req.query.spot_id as string;
  const tripId = req.query.trip_id as string;

  const result = await DatabaseService.getPosts(page, limit, {
    userId,
    spotId: spotId ? parseInt(spotId) : undefined,
    tripId: tripId ? parseInt(tripId) : undefined
  });

  const response: ApiResponse<any> = {
    success: true,
    data: {
      items: result.items,
      total: result.total,
      page: result.page,
      limit: result.limit,
      hasMore: result.hasMore
    }
  };

  res.json(response);
}));

// GET /api/posts/:id - Get single post with full relationships
router.get('/:id', optionalAuth, asyncHandler(async (req, res) => {
  const postId = parseInt(req.params.id);

  if (isNaN(postId)) {
    throw new AppError('Invalid post ID', 400, 'INVALID_POST_ID');
  }

  const post = await DatabaseService.getPostById(postId);

  if (!post) {
    throw new AppError('Post not found', 404, 'POST_NOT_FOUND');
  }

  // Get spot details if post has spot_id
  if (post.spot_id) {
    const spot = await DatabaseService.getSpotById(post.spot_id);
    post.spot = spot;
  }

  // Get trip details if post has trip_id
  if (post.trip_id) {
    const trip = await DatabaseService.getTripById(post.trip_id);
    post.trip = trip;
  }

  // Convert units to user preferences if user is authenticated
  if (req.userId && post.catch_details) {
    const userUnits = await UserPreferencesService.getUserPreferences(req.userId);
    post.catch_details = UnitConversionService.convertCatchFromBaseUnits(
      post.catch_details,
      userUnits
    );
  }

  const response: ApiResponse<Post> = {
    success: true,
    data: post
  };

  res.json(response);
}));

// POST /api/posts - Create new post with enhanced relationships
router.post('/', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const { error, value } = createPostSchema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400, 'VALIDATION_ERROR');
  }

  // Validate spot_id if provided
  if (value.spot_id) {
    const spot = await DatabaseService.getSpotById(value.spot_id);
    if (!spot) {
      throw new AppError('Spot not found', 404, 'SPOT_NOT_FOUND');
    }
    if (spot.status !== 'approved') {
      throw new AppError('Spot is not approved', 400, 'SPOT_NOT_APPROVED');
    }
  }

  // Validate trip_id if provided
  if (value.trip_id) {
    const trip = await DatabaseService.getTripById(value.trip_id);
    if (!trip) {
      throw new AppError('Trip not found', 404, 'TRIP_NOT_FOUND');
    }
    // Check if user is participant of the trip
    const isParticipant = await DatabaseService.isTripParticipant(value.trip_id, req.userId!);
    if (!isParticipant) {
      throw new AppError('Not authorized to post to this trip', 403, 'NOT_TRIP_PARTICIPANT');
    }
  }

  // Get user's unit preferences
  const userUnits = await UserPreferencesService.getUserPreferences(req.userId!);
  
  // Convert catch details from user units to base units for database storage
  let convertedCatchDetails = value.catch_details;
  if (value.catch_details) {
    convertedCatchDetails = UnitConversionService.convertCatchToBaseUnits(
      value.catch_details, 
      userUnits
    );
  }

  const postData: CreatePostRequest & { user_id: string } = {
    ...value,
    catch_details: convertedCatchDetails,
    user_id: req.userId!,
    created_at: new Date().toISOString()
  };

  const post = await DatabaseService.createPost(postData);

  // Convert response data back to user units for frontend
  let responsePost = post;
  if (post.catch_details) {
    responsePost = {
      ...post,
      catch_details: UnitConversionService.convertCatchFromBaseUnits(
        post.catch_details,
        userUnits
      )
    };
  }

  // Create notification for spot owner if posting to a spot
  if (value.spot_id) {
    const spot = await DatabaseService.getSpotById(value.spot_id);
    if (spot && spot.user_id !== req.userId) {
      await DatabaseService.createNotification({
        user_id: spot.user_id,
        type: 'spot_catch',
        title: 'Yeni Av Paylaşımı',
        content: `${post.users?.full_name || 'Bir kullanıcı'} spotunuzda av paylaştı!`,
        data: { post_id: post.id, spot_id: value.spot_id },
        action_url: `/posts/${post.id}`
      });
    }
  }

  const response: ApiResponse<Post> = {
    success: true,
    message: 'Post created successfully',
    data: responsePost
  };

  res.status(201).json(response);
}));

// PUT /api/posts/:id - Update post
router.put('/:id', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const postId = parseInt(req.params.id);

  if (isNaN(postId)) {
    throw new AppError('Invalid post ID', 400, 'INVALID_POST_ID');
  }

  const { error, value } = updatePostSchema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400, 'VALIDATION_ERROR');
  }

  // Check if post exists and belongs to user
  const existingPost = await DatabaseService.getPostById(postId);
  if (!existingPost) {
    throw new AppError('Post not found', 404, 'POST_NOT_FOUND');
  }

  if (existingPost.user_id !== req.userId) {
    throw new AppError('Not authorized to update this post', 403, 'NOT_AUTHORIZED');
  }

  // Validate spot_id if provided
  if (value.spot_id) {
    const spot = await DatabaseService.getSpotById(value.spot_id);
    if (!spot) {
      throw new AppError('Spot not found', 404, 'SPOT_NOT_FOUND');
    }
    if (spot.status !== 'approved') {
      throw new AppError('Spot is not approved', 400, 'SPOT_NOT_APPROVED');
    }
  }

  // Get user's unit preferences and convert catch details
  let convertedCatchDetails = value.catch_details;
  if (value.catch_details) {
    const userUnits = await UserPreferencesService.getUserPreferences(req.userId!);
    convertedCatchDetails = UnitConversionService.convertCatchToBaseUnits(
      value.catch_details, 
      userUnits
    );
  }

  const updateData = {
    ...value,
    catch_details: convertedCatchDetails,
    updated_at: new Date().toISOString()
  };

  const updatedPost = await DatabaseService.updatePost(postId, updateData);

  const response: ApiResponse<Post> = {
    success: true,
    message: 'Post updated successfully',
    data: updatedPost
  };

  res.json(response);
}));

// DELETE /api/posts/:id - Delete post
router.delete('/:id', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const postId = parseInt(req.params.id);

  if (isNaN(postId)) {
    throw new AppError('Invalid post ID', 400, 'INVALID_POST_ID');
  }

  // Check if post exists and belongs to user
  const existingPost = await DatabaseService.getPostById(postId);
  if (!existingPost) {
    throw new AppError('Post not found', 404, 'POST_NOT_FOUND');
  }

  if (existingPost.user_id !== req.userId) {
    throw new AppError('Not authorized to delete this post', 403, 'NOT_AUTHORIZED');
  }

  await DatabaseService.deletePost(postId);

  const response: ApiResponse<null> = {
    success: true,
    message: 'Post deleted successfully',
    data: null
  };

  res.json(response);
}));

// POST /api/posts/:id/like - Like a post
router.post('/:id/like', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const postId = parseInt(req.params.id);

  if (isNaN(postId)) {
    throw new AppError('Invalid post ID', 400, 'INVALID_POST_ID');
  }

  const post = await DatabaseService.getPostById(postId);
  if (!post) {
    throw new AppError('Post not found', 404, 'POST_NOT_FOUND');
  }

  const isAlreadyLiked = await DatabaseService.isPostLiked(req.userId!, postId);
  if (isAlreadyLiked) {
    throw new AppError('Post already liked', 400, 'ALREADY_LIKED');
  }

  await DatabaseService.likePost(req.userId!, postId);

  // Create notification for post owner
  if (post.user_id !== req.userId) {
    const user = await DatabaseService.getUserById(req.userId!);
    await DatabaseService.createNotification({
      user_id: post.user_id,
      type: 'like',
      title: 'Yeni Beğeni',
      content: `${user?.full_name || 'Bir kullanıcı'} gönderinizi beğendi!`,
      data: { post_id: postId, user_id: req.userId },
      action_url: `/posts/${postId}`
    });
  }

  const response: ApiResponse<null> = {
    success: true,
    message: 'Post liked successfully',
    data: null
  };

  res.json(response);
}));

// DELETE /api/posts/:id/like - Unlike a post
router.delete('/:id/like', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const postId = parseInt(req.params.id);

  if (isNaN(postId)) {
    throw new AppError('Invalid post ID', 400, 'INVALID_POST_ID');
  }

  const isLiked = await DatabaseService.isPostLiked(req.userId!, postId);
  if (!isLiked) {
    throw new AppError('Post not liked', 400, 'NOT_LIKED');
  }

  await DatabaseService.unlikePost(req.userId!, postId);

  const response: ApiResponse<null> = {
    success: true,
    message: 'Post unliked successfully',
    data: null
  };

  res.json(response);
}));

export default router;
