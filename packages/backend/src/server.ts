import app from './app';
import { ENV } from './config';

const PORT = ENV.PORT || 4000;

app.listen(PORT, () => {
  console.log(`🚀 Fishivo API Server running on port ${PORT}`);
  console.log(`📱 Environment: ${ENV.NODE_ENV}`);
  console.log(`🌐 API URL: ${ENV.API_URL}`);
  console.log(`🔗 Health check: ${ENV.API_URL}/health`);
  
  if (ENV.NODE_ENV === 'development') {
    console.log('🔧 Development mode - detailed logging enabled');
  } else {
    console.log('🔒 Production mode - security enhanced');
  }
}); 