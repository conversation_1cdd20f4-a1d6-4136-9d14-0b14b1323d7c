// Backend Configuration for Fishivo Project
// Server-side configuration with enhanced security

// Environment validation helper
function requireEnvVar(name: string, fallback?: string): string {
  const value = process.env[name];
  if (!value) {
    if (fallback) {
      if (process.env.NODE_ENV === 'development') {
        console.warn(`⚠️  Using fallback value for ${name} in development mode`);
      }
      return fallback;
    }
    console.error(`❌ Required environment variable ${name} is not set`);
    if (process.env.NODE_ENV === 'production') {
      throw new Error(`Required environment variable ${name} is not set`);
    }
    return '';
  }
  return value;
}

// Mask sensitive values for logging
function maskSensitiveValue(value: string): string {
  if (!value || value.length < 8) return '***';
  return value.substring(0, 4) + '***' + value.substring(value.length - 4);
}

// Environment Configuration
export const ENV = {
  NODE_ENV: process.env.NODE_ENV || 'development',
  API_URL: process.env.API_URL || process.env.NEXT_PUBLIC_API_URL || 'https://api.fishivo.com',
  WEB_URL: process.env.WEB_URL || process.env.NEXT_PUBLIC_APP_URL || 'https://fishivo.com',
  PORT: parseInt(process.env.PORT || '4000'),
} as const;

// Supabase Configuration
export const SUPABASE_CONFIG = {
  url: requireEnvVar('NEXT_PUBLIC_SUPABASE_URL'),
  anonKey: requireEnvVar('NEXT_PUBLIC_SUPABASE_ANON_KEY'),
  serviceRoleKey: requireEnvVar('SUPABASE_SERVICE_ROLE_KEY'),
  jwtSecret: requireEnvVar('SUPABASE_JWT_SECRET'),
} as const;

// Google OAuth Configuration
export const GOOGLE_OAUTH = {
  clientId: requireEnvVar('GOOGLE_CLIENT_ID'),
  clientSecret: requireEnvVar('GOOGLE_CLIENT_SECRET'),
  callbackUrl: process.env.GOOGLE_CALLBACK_URL || 'https://api.fishivo.com/auth/google/callback',
  redirectUri: process.env.GOOGLE_REDIRECT_URI || 'https://fishivo.com/auth/callback',
} as const;

// Facebook OAuth Configuration
export const FACEBOOK_OAUTH = {
  appId: requireEnvVar('FACEBOOK_APP_ID'),
  appSecret: requireEnvVar('FACEBOOK_APP_SECRET'),
  callbackUrl: process.env.FACEBOOK_CALLBACK_URL || 'https://api.fishivo.com/auth/facebook/callback',
} as const;

// API Configuration
export const API_CONFIG = {
  baseURL: ENV.API_URL,
  timeout: parseInt(process.env.API_TIMEOUT || '10000'),
  retries: parseInt(process.env.API_RETRIES || '3'),
} as const;

// Security Configuration
export const SECURITY_CONFIG = {
  jwtSecret: requireEnvVar('JWT_SECRET'),
  jwtExpiresIn: process.env.JWT_EXPIRES_IN || '7d',
  bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12'),
  sessionSecret: requireEnvVar('SESSION_SECRET'),
} as const;

// Log configuration (mask sensitive values)
export function logConfig() {
  if (ENV.NODE_ENV === 'development') {
    console.log('🔧 Configuration loaded:');
    console.log(`   NODE_ENV: ${ENV.NODE_ENV}`);
    console.log(`   API_URL: ${ENV.API_URL}`);
    console.log(`   WEB_URL: ${ENV.WEB_URL}`);
    console.log(`   PORT: ${ENV.PORT}`);
    console.log(`   Supabase URL: ${SUPABASE_CONFIG.url}`);
    console.log(`   Supabase Anon Key: ${maskSensitiveValue(SUPABASE_CONFIG.anonKey)}`);
    console.log(`   Google Client ID: ${maskSensitiveValue(GOOGLE_OAUTH.clientId)}`);
    console.log('   ✅ All required environment variables loaded');
  }
}
