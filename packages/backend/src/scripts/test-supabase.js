require('dotenv').config({path: '../../.env'});
const { createClient } = require('@supabase/supabase-js');

console.log('🔍 Supabase Connection Test:');
console.log('URL:', process.env.NEXT_PUBLIC_SUPABASE_URL);
console.log('ANON_KEY exists:', !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
console.log('SERVICE_KEY exists:', !!process.env.SUPABASE_SERVICE_ROLE_KEY);

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testConnection() {
  try {
    console.log('\n🧪 Testing connection...');
    
    // Test 1: Simple query
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);
    
    if (error) {
      console.error('❌ Connection test failed:', error.message);
      console.error('Error details:', error);
      return false;
    }
    
    console.log('✅ Connection successful!');
    console.log('Test result:', data);
    
    // Test 2: Check tables
    const { data: tables, error: tablesError } = await supabase
      .rpc('exec_sql', { 
        sql: "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;" 
      });
    
    if (tablesError) {
      console.log('⚠️ Could not list tables:', tablesError.message);
    } else {
      console.log('📋 Available tables:', tables);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Connection error:', error.message);
    return false;
  }
}

testConnection(); 