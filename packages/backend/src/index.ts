import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import dotenv from 'dotenv';
import path from 'path';
import { createServer } from 'http';
import session from 'express-session';
import passport, { initializePassport } from './config/passport';
import rateLimit from 'express-rate-limit';

// Load environment variables from project root FIRST - BEFORE any other imports
const envPath = path.resolve(__dirname, '../../../.env');
console.log('🔍 Loading .env from:', envPath);
console.log('🔍 File exists:', require('fs').existsSync(envPath));
dotenv.config({ path: envPath, override: true });

// Debug environment variables
console.log('🔍 Environment Debug:');
console.log('  - NODE_ENV:', process.env.NODE_ENV);
console.log('  - NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL ? 'SET' : 'NOT SET');
console.log('  - SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? 'SET' : 'NOT SET');
console.log('  - GOOGLE_CLIENT_SECRET:', process.env.GOOGLE_CLIENT_SECRET ? 'SET' : 'NOT SET');
console.log('  - NEXT_PUBLIC_GOOGLE_CLIENT_ID:', process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID ? 'SET' : 'NOT SET');

// Import and validate configuration AFTER env is loaded
import { ENV, logConfig } from './config';

// Validate environment and log configuration
try {
  logConfig();
  
  // Validate R2 credentials
  const r2Credentials = {
    accountId: process.env.CLOUDFLARE_ACCOUNT_ID,
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
    bucketName: process.env.CLOUDFLARE_R2_BUCKET_NAME,
    publicUrl: process.env.CLOUDFLARE_R2_PUBLIC_URL
  };
  
  if (r2Credentials.accountId && r2Credentials.accessKeyId && r2Credentials.secretAccessKey) {
    console.log('✅ R2 credentials loaded successfully');
  } else {
    console.warn('⚠️ R2 credentials missing - uploads will not work');
  }
  
  console.log('✅ Environment validation successful');
} catch (error) {
  console.error('❌ Environment validation failed:', error);
  if (ENV.NODE_ENV === 'production') {
    process.exit(1);
  }
}

// Initialize passport strategies after env is loaded
initializePassport();

// Import routes
import authRoutes from './routes/auth';
import userRoutes from './routes/users';
import postRoutes from './routes/posts';
import speciesRoutes from './routes/species';
import uploadRoutes from './routes/upload';
import spotsRoutes from './routes/spots';
import followsRoutes from './routes/follows';
import messagesRoutes from './routes/messages';
import searchRoutes from './routes/search';
import adminRoutes from './routes/admin';
import userPreferencesRoutes from './routes/userPreferences';
import unitsRoutes from './routes/units';
import tripsRoutes from './routes/trips';
import reviewsRoutes from './routes/reviews';
import notificationRoutes from './routes/notifications';
import equipmentRoutes from './routes/equipment';
import weatherRoutes from './routes/weather';
import techniquesRoutes from './routes/techniques';

// Import middleware
import { errorHandler } from './middleware/errorHandler';
import { requestLogger } from './middleware/logger';

// Import additional config
import { SECURITY_CONFIG } from './config';

const app = express();
const server = createServer(app);

// Security middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS configuration
app.use(cors({
  origin: process.env['CORS_ORIGIN']?.split(',') || ENV.CORS_ORIGIN.split(','),
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Compression middleware
app.use(compression());

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Session middleware (required for passport)
app.use(session({
  secret: process.env.SESSION_SECRET || SECURITY_CONFIG.jwtSecret,
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: ENV.NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    sameSite: ENV.NODE_ENV === 'production' ? 'strict' : 'lax'
  }
}));

// Passport middleware
app.use(passport.initialize());
app.use(passport.session());

// Logging middleware
app.use(morgan('combined'));
app.use(requestLogger);

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// Health check endpoint
app.get('/health', (_req, res) => {
  res.status(200).json({
    success: true,
    message: 'Fishivo API is running!',
    timestamp: new Date().toISOString(),
    environment: ENV.NODE_ENV,
    version: '1.0.0'
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/auth', authRoutes); // OAuth routes (without /api prefix)
app.use('/api/users', userRoutes);
app.use('/api/catches', postRoutes); // 🎣 Ana endpoint - balık avları
app.use('/api/posts', postRoutes); // 🔄 Geriye uyumluluk için
app.use('/api/species', speciesRoutes);
app.use('/api/upload', uploadRoutes);
app.use('/api/spots', spotsRoutes);
app.use('/api/follows', followsRoutes);
app.use('/api/messages', messagesRoutes);
app.use('/api/search', searchRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/user-preferences', userPreferencesRoutes);
app.use('/api/units', unitsRoutes);
app.use('/api/trips', tripsRoutes);
app.use('/api/reviews', reviewsRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/equipment', equipmentRoutes);
app.use('/api/weather', weatherRoutes);
app.use('/api/techniques', techniquesRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    message: `Route ${req.originalUrl} not found`
  });
});

// Error handling middleware (must be last)
app.use(errorHandler);

// Start server
const PORT = ENV.PORT;

server.listen(PORT, () => {
  console.log(`🚀 Fishivo API Server running on port ${PORT}`);
  console.log(`📱 Environment: ${ENV.NODE_ENV}`);
  console.log(`🌐 API URL: ${ENV.API_URL}`);
  console.log(`🔗 Health check: ${ENV.API_URL}/health`);

  if (ENV.NODE_ENV === 'development') {
    console.log('🔧 Development mode - detailed logging enabled');
  } else {
    console.log('🔒 Production mode - security enhanced');
  }
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

export default app;
