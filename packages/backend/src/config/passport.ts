import passport from 'passport';
import { Strategy as GoogleStrategy } from 'passport-google-oauth20';
import { Strategy as FacebookStrategy } from 'passport-facebook';
import { supabase } from '../services/supabase';

// Initialize passport strategies
export const initializePassport = () => {
  console.log('🔍 Passport Config Debug:');
  console.log('  - GOOGLE_CLIENT_ID:', process.env['GOOGLE_CLIENT_ID'] ? 'SET' : 'NOT SET');
  console.log('  - NEXT_PUBLIC_GOOGLE_CLIENT_ID:', process.env['NEXT_PUBLIC_GOOGLE_CLIENT_ID'] ? 'SET' : 'NOT SET');
  console.log('  - GOOGLE_CLIENT_SECRET:', process.env['GOOGLE_CLIENT_SECRET'] ? 'SET' : 'NOT SET');
  console.log('  - FACEBOOK_APP_ID:', process.env['FACEBOOK_APP_ID'] ? 'SET' : 'NOT SET');
  console.log('  - NEXT_PUBLIC_FACEBOOK_APP_ID:', process.env['NEXT_PUBLIC_FACEBOOK_APP_ID'] ? 'SET' : 'NOT SET');
  console.log('  - FACEBOOK_APP_SECRET:', process.env['FACEBOOK_APP_SECRET'] ? 'SET' : 'NOT SET');
  
  // Google OAuth Strategy
  passport.use(new GoogleStrategy({
    clientID: process.env['GOOGLE_CLIENT_ID'] || process.env['NEXT_PUBLIC_GOOGLE_CLIENT_ID'] || '',
    clientSecret: process.env['GOOGLE_CLIENT_SECRET'] || '',
    callbackURL: process.env['GOOGLE_CALLBACK_URL'] || ''
  }, async (_accessToken, _refreshToken, profile, done) => {
  try {
    console.log('Google OAuth Profile:', profile);
    
    // Check if user already exists
    const { data: existingUser, error: findError } = await supabase
      .from('users')
      .select('*')
      .eq('email', profile.emails?.[0]?.value)
      .single();

    if (existingUser && !findError) {
      // User exists, update Google info if needed
      const { data: updatedUser, error: updateError } = await supabase
        .from('users')
        .update({
          google_id: profile.id,
          avatar_url: profile.photos?.[0]?.value,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingUser.id)
        .select()
        .single();

      if (updateError) {
        console.error('Error updating user:', updateError);
        return done(updateError, false);
      }

      return done(null, updatedUser);
    }

    // Create new user
    const newUser = {
      id: `google_${profile.id}`,
      email: profile.emails?.[0]?.value || '',
      username: profile.displayName?.toLowerCase().replace(/\s+/g, '_') || `user_${profile.id}`,
      full_name: profile.displayName || '',
      avatar_url: profile.photos?.[0]?.value || '',
      google_id: profile.id,
      provider: 'google',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: createdUser, error: createError } = await supabase
      .from('users')
      .insert(newUser)
      .select()
      .single();

    if (createError) {
      console.error('Error creating user:', createError);
      return done(createError, false);
    }

    return done(null, createdUser);
  } catch (error) {
    console.error('Google OAuth error:', error);
    return done(error as Error, false);
  }
}));

  // Facebook OAuth Strategy
  passport.use(new FacebookStrategy({
    clientID: process.env['FACEBOOK_APP_ID'] || process.env['NEXT_PUBLIC_FACEBOOK_APP_ID'] || '',
    clientSecret: process.env['FACEBOOK_APP_SECRET'] || '',
    callbackURL: process.env['FACEBOOK_CALLBACK_URL'] || '',
    profileFields: ['id', 'emails', 'name', 'picture.type(large)']
  }, async (_accessToken, _refreshToken, profile, done) => {
  try {
    console.log('Facebook OAuth Profile:', profile);
    
    // Check if user already exists
    const { data: existingUser, error: findError } = await supabase
      .from('users')
      .select('*')
      .eq('email', profile.emails?.[0]?.value)
      .single();

    if (existingUser && !findError) {
      // User exists, update Facebook info if needed
      const { data: updatedUser, error: updateError } = await supabase
        .from('users')
        .update({
          facebook_id: profile.id,
          avatar_url: profile.photos?.[0]?.value,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingUser.id)
        .select()
        .single();

      if (updateError) {
        console.error('Error updating user:', updateError);
        return done(updateError, null);
      }

      return done(null, updatedUser);
    }

    // Create new user
    const newUser = {
      id: `facebook_${profile.id}`,
      email: profile.emails?.[0]?.value || '',
      username: `${profile.name?.givenName}_${profile.name?.familyName}`.toLowerCase().replace(/\s+/g, '_') || `user_${profile.id}`,
      full_name: `${profile.name?.givenName} ${profile.name?.familyName}` || '',
      avatar_url: profile.photos?.[0]?.value || '',
      facebook_id: profile.id,
      provider: 'facebook',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: createdUser, error: createError } = await supabase
      .from('users')
      .insert(newUser)
      .select()
      .single();

    if (createError) {
      console.error('Error creating user:', createError);
      return done(createError, null);
    }

    return done(null, createdUser);
  } catch (error) {
    console.error('Facebook OAuth error:', error);
    return done(error, null);
  }
}));

  // Serialize user for session
  passport.serializeUser((user: any, done) => {
    done(null, user.id);
  });

  // Deserialize user from session
  passport.deserializeUser(async (id: string, done) => {
    try {
      const { data: user, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        return done(error, null);
      }

      done(null, user);
    } catch (error) {
      done(error, null);
    }
  });
};

export default passport;
