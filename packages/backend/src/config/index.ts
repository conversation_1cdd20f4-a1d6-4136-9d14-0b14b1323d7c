// Environment variables are loaded in index.ts - no need to load here

// Development IP configuration - centralized IP management
const DEVELOPMENT_IP = '***********';

// Environment validation function
function getRequiredEnvVar(name: string): string {
  const value = process.env[name];
  if (!value) {
    console.error(`❌ Required environment variable ${name} is not set`);
    throw new Error(`Required environment variable ${name} is not set`);
  }
  return value;
}

function getOptionalEnvVar(name: string, defaultValue: string): string {
  return process.env[name] || defaultValue;
}

// Environment configuration
export const ENV = {
  NODE_ENV: getOptionalEnvVar('NODE_ENV', 'development'),
  PORT: parseInt(getOptionalEnvVar('PORT', '4000')),
  API_URL: getOptionalEnvVar('API_URL', `http://${DEVELOPMENT_IP}:4000`),
  WEB_URL: getOptionalEnvVar('WEB_URL', `http://${DEVELOPMENT_IP}:3010`),
  
  // Supabase
  SUPABASE_URL: getRequiredEnvVar('NEXT_PUBLIC_SUPABASE_URL'),
  SUPABASE_ANON_KEY: getRequiredEnvVar('NEXT_PUBLIC_SUPABASE_ANON_KEY'),
  SUPABASE_SERVICE_ROLE_KEY: getRequiredEnvVar('SUPABASE_SERVICE_ROLE_KEY'),
  SUPABASE_JWT_SECRET: getRequiredEnvVar('SUPABASE_JWT_SECRET'),
  
  // OAuth
  GOOGLE_CLIENT_ID: getRequiredEnvVar('GOOGLE_CLIENT_ID'),
  GOOGLE_CLIENT_SECRET: getRequiredEnvVar('GOOGLE_CLIENT_SECRET'),
  FACEBOOK_APP_ID: getRequiredEnvVar('FACEBOOK_APP_ID'),
  FACEBOOK_APP_SECRET: getRequiredEnvVar('FACEBOOK_APP_SECRET'),
  
  // JWT
  JWT_SECRET: getRequiredEnvVar('JWT_SECRET'),
  SESSION_SECRET: getRequiredEnvVar('SESSION_SECRET'),
  
  // CORS - using centralized IP
  CORS_ORIGIN: getOptionalEnvVar('CORS_ORIGIN', `http://${DEVELOPMENT_IP}:3010,exp://${DEVELOPMENT_IP}:8081,exp://${DEVELOPMENT_IP}:8083`),
};

// Supabase configuration
export const SUPABASE_CONFIG = {
  url: ENV.SUPABASE_URL,
  anonKey: ENV.SUPABASE_ANON_KEY,
  serviceRoleKey: ENV.SUPABASE_SERVICE_ROLE_KEY,
  jwtSecret: ENV.SUPABASE_JWT_SECRET,
};

// Security configuration
export const SECURITY_CONFIG = {
  jwtSecret: ENV.JWT_SECRET,
  jwtExpiresIn: getOptionalEnvVar('JWT_EXPIRES_IN', '7d'),
  sessionSecret: ENV.SESSION_SECRET,
  bcryptRounds: 12,
};

// Log configuration function
export function logConfig() {
  console.log('🔧 Configuration loaded:');
  console.log(`   NODE_ENV: ${ENV.NODE_ENV}`);
  console.log(`   API_URL: ${ENV.API_URL}`);
  console.log(`   WEB_URL: ${ENV.WEB_URL}`);
  console.log(`   PORT: ${ENV.PORT}`);
  console.log(`   Supabase URL: ${ENV.SUPABASE_URL}`);
  console.log(`   Supabase Anon Key: ${ENV.SUPABASE_ANON_KEY.substring(0, 4)}***${ENV.SUPABASE_ANON_KEY.slice(-4)}`);
  console.log(`   Google Client ID: ${ENV.GOOGLE_CLIENT_ID.substring(0, 4)}***.com`);
  console.log('   ✅ All required environment variables loaded');
}

// Environment validation function
export function validateEnvironment() {
  try {
    // Test all required environment variables
    console.log('🔍 Environment doğrulaması başlıyor...');
    
         const requiredVars = [
       'NEXT_PUBLIC_SUPABASE_URL',
       'NEXT_PUBLIC_SUPABASE_ANON_KEY', 
       'SUPABASE_SERVICE_ROLE_KEY',
       'SUPABASE_JWT_SECRET',
       'JWT_SECRET',
       'SESSION_SECRET'
     ];
     
     // R2 Storage variables
     const r2Vars = [
       'CLOUDFLARE_ACCOUNT_ID',
       'CLOUDFLARE_R2_ACCESS_KEY_ID',
       'CLOUDFLARE_R2_SECRET_ACCESS_KEY',
       'CLOUDFLARE_R2_BUCKET_NAME'
     ];
     
     // Check R2 variables
     const hasR2 = r2Vars.every(varName => process.env[varName]);
     
     if (hasR2) {
       console.log('✅ R2 Storage Service initialized');
     } else {
       console.log('⚠️ R2 Storage credentials not found. File upload will be disabled.');
     }

    for (const varName of requiredVars) {
      if (!process.env[varName]) {
        throw new Error(`Required environment variable ${varName} is missing`);
      }
    }
    
    console.log('✅ Tüm gerekli environment değişkenleri mevcut');
    return true;
  } catch (error) {
    console.error('❌ Environment validation failed:', error);
    throw error;
  }
}
