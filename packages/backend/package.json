{"name": "fishivo-backend", "version": "1.0.0", "description": "Fishivo Backend API - Fishbrain Clone", "main": "dist/server.js", "scripts": {"dev": "ts-node --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["fishivo", "fishing", "social", "api", "backend", "fishbrain", "clone"], "author": "Fishivo Team", "license": "MIT", "dependencies": {"@aws-sdk/client-s3": "^3.828.0", "@aws-sdk/s3-request-presigner": "^3.828.0", "@supabase/supabase-js": "^2.39.0", "@types/compression": "^1.8.0", "@types/express-session": "^1.18.1", "@types/passport": "^1.0.17", "@types/passport-facebook": "^3.0.3", "@types/passport-google-oauth20": "^2.0.16", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "form-data": "^4.0.3", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "rate-limiter-flexible": "^7.1.1", "uuid": "^9.0.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.0", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "ts-node": "^10.9.1", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0"}}