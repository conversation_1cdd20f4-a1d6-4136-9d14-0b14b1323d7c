{"name": "fishivo-web", "version": "1.0.0", "description": "Fishivo Web Application - Next.js Frontend", "private": true, "scripts": {"dev": "next dev -p 3010", "build": "next build", "start": "next start -p 3010", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@mapbox/mapbox-gl-geocoder": "^5.0.3", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-slot": "^1.2.3", "@supabase/supabase-js": "^2.49.10", "@turf/turf": "^7.2.0", "@types/mapbox-gl": "^3.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3-contour": "^4.0.2", "define-properties": "^1.2.1", "dlv": "^1.1.3", "es-abstract": "^1.24.0", "lucide-react": "^0.511.0", "mapbox-gl": "^3.12.0", "next": "15.3.3", "object-hash": "^3.0.0", "postcss-selector-parser": "^6.1.2", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.14", "eslint": "^9", "eslint-config-next": "15.3.3", "postcss": "^8.4.21", "tailwindcss": "^3.4.1", "typescript": "^5"}, "keywords": ["fishivo", "fishing", "social", "nextjs", "web"], "author": "Fishivo Team", "license": "MIT"}