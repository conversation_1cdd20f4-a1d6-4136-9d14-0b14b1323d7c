# 🐟 Fishivo Web Application

This is the Next.js web application for Fishivo, built with Next.js 15 and App Router.

## 🚨 **CRITICAL: File Structure Rules**

### **✅ CORRECT Structure (ALWAYS USE THIS):**
```
web/
├── src/
│   ├── app/              # ✅ ONLY app directory for Next.js App Router
│   │   ├── layout.tsx    # Root layout
│   │   ├── page.tsx      # Home page (/)
│   │   ├── login/        # Login page (/login)
│   │   │   └── page.tsx
│   │   ├── dashboard/    # Dashboard page (/dashboard)
│   │   │   └── page.tsx
│   │   └── auth/         # Auth callback (/auth/callback)
│   │       └── callback/
│   │           └── page.tsx
│   ├── components/       # Reusable components
│   │   └── NoSSR.tsx    # Client-only wrapper
│   └── lib/             # Utilities and API clients
│       ├── api.ts       # API client
│       └── supabase.ts  # Supabase client
├── public/              # Static assets
└── package.json
```

### **❌ NEVER DO THIS:**
```
web/
├── app/          # ❌ WRONG: Don't create app directory here
└── src/
    └── app/      # ❌ WRONG: This creates conflicts
```

## 🚀 **Getting Started**

### **Development Server**
```bash
npm run dev
```

Open [http://localhost:3010](http://localhost:3010) with your browser.

### **Key Features**
- ✅ Next.js 15 with App Router
- ✅ TypeScript support
- ✅ Tailwind CSS styling
- ✅ Supabase integration
- ✅ OAuth authentication (Google, Facebook)
- ✅ Hydration error prevention
- ✅ SSR/SSG support

## 🔧 **Development Guidelines**

### **Adding New Pages**
1. Create folder in `src/app/`
2. Add `page.tsx` file
3. Export default React component

Example:
```bash
# Create new page
mkdir src/app/profile
touch src/app/profile/page.tsx
```

### **Preventing Hydration Errors**
```tsx
// Use NoSSR for client-only content
import NoSSR from '@/components/NoSSR';

<NoSSR fallback={<div>Loading...</div>}>
  <ClientOnlyComponent />
</NoSSR>

// Or use suppressHydrationWarning
<div suppressHydrationWarning>
  {dynamicContent}
</div>
```

### **API Integration**
```tsx
// Use the API client
import { apiClient } from '@/lib/api';

const data = await apiClient.get('/posts');
```

## 🚨 **Common Issues & Solutions**

### **404 Page Not Found**
- ✅ Check `src/app/page.tsx` exists
- ✅ Ensure no duplicate `app/` directories
- ✅ Restart development server

### **Hydration Mismatch**
- ✅ Use `NoSSR` component for client-only content
- ✅ Add `suppressHydrationWarning` to dynamic elements
- ✅ Avoid `Date.now()` or `Math.random()` in SSR

### **Import Errors**
- ✅ Use `@/` for imports from `src/`
- ✅ Check `tsconfig.json` paths configuration

## 📚 **Learn More**

- [Next.js Documentation](https://nextjs.org/docs)
- [App Router Guide](https://nextjs.org/docs/app)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [Supabase Docs](https://supabase.com/docs)
