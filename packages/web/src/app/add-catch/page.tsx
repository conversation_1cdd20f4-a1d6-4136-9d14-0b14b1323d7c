'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Layout, Container, Card, Button, Badge, Icon, Input } from '../../components/ui';
import { apiClient } from '../../lib/api';

export default function AddCatch() {
  const [formData, setFormData] = useState({
    species: '',
    weight: '',
    length: '',
    location: '',
    caption: '',
    weather: '',
    bait: '',
    technique: ''
  });
  const [photo, setPhoto] = useState<File | null>(null);
  const [coordinates, setCoordinates] = useState<{lat: number, lng: number} | null>(null);
  const [speciesList, setSpeciesList] = useState<string[]>([]);
  const [speciesLoading, setSpeciesLoading] = useState(false);
  const [speciesError, setSpeciesError] = useState<string | null>(null);

  useEffect(() => {
    setSpeciesLoading(true);
    setSpeciesError(null);
    apiClient.getSpecies()
      .then(res => {
        if (res.success) {
          setSpeciesList((res.data || []).map((s: any) => s.name));
        } else {
          setSpeciesError('Balık türleri alınamadı');
        }
      })
      .catch(e => setSpeciesError(e.message))
      .finally(() => setSpeciesLoading(false));
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handlePhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setPhoto(e.target.files[0]);
    }
  };

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setCoordinates({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          });
          setFormData({
            ...formData,
            location: `${position.coords.latitude.toFixed(4)}, ${position.coords.longitude.toFixed(4)}`
          });
        },
        (error) => {
          console.error('Konum alınamadı:', error);
          alert('Konum izni gerekli!');
        }
      );
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form Data:', formData);
    console.log('Photo:', photo);
    console.log('Coordinates:', coordinates);
    
    alert('Balık başarıyla eklendi! 🎣');
    window.location.href = '/';
  };

  return (
    <Layout>
      {/* Page Header */}
      <div className="py-6">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <h1 className="text-3xl font-bold text-foreground flex items-center gap-3">
              <Icon name="plus" size="lg" className="text-primary" />
              Balık Ekle
            </h1>
          </div>
          <Button 
            type="submit" 
            form="catch-form"
            variant="primary"
            size="lg"
          >
            <Icon name="share" size="sm" className="mr-2" />
            Paylaş
          </Button>
        </div>

        {/* Form */}
        <div className="max-w-2xl mx-auto">
          <form id="catch-form" onSubmit={handleSubmit} className="space-y-8">
            {/* Photo Upload */}
            <Card className="p-6">
              <h3 className="font-semibold mb-4 flex items-center gap-2">
                <Icon name="fish" size="sm" className="text-primary" />
                Fotoğraf
              </h3>
              <div className="border-2 border-dashed border-border rounded-lg p-8 text-center bg-muted/30">
                {photo ? (
                  <div>
                    <Icon name="check" size="lg" className="text-green-500 mx-auto mb-3" />
                    <p className="text-foreground mb-2">Fotoğraf seçildi: {photo.name}</p>
                    <Button 
                      type="button"
                      variant="danger"
                      size="sm"
                      onClick={() => setPhoto(null)}
                    >
                      Kaldır
                    </Button>
                  </div>
                ) : (
                  <div>
                    <Icon name="fish" size="2xl" className="text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground mb-4">Balık fotoğrafını seç</p>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handlePhotoChange}
                      className="hidden"
                      id="photo-upload"
                    />
                    <label 
                      htmlFor="photo-upload"
                      className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none border border-border bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 cursor-pointer"
                    >
                      <Icon name="plus" size="sm" className="mr-2" />
                      Fotoğraf Seç
                    </label>
                  </div>
                )}
              </div>
            </Card>

            {/* Fish Details */}
            <Card className="p-6">
              <h3 className="font-semibold mb-4 flex items-center gap-2">
                <Icon name="fish" size="sm" className="text-primary" />
                Balık Bilgileri
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-foreground font-medium mb-2">Balık Türü</label>
                  <select
                    name="species"
                    value={formData.species}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-background border border-border rounded-md text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                    required
                  >
                    <option value="">Tür seçin</option>
                    {speciesLoading && <option disabled>Yükleniyor...</option>}
                    {speciesError && <option disabled>Hata: {speciesError}</option>}
                    {!speciesLoading && !speciesError && speciesList.length === 0 && <option disabled>Tür bulunamadı</option>}
                    {speciesList.map((species: string) => (
                      <option key={species} value={species}>{species}</option>
                    ))}
                  </select>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-foreground font-medium mb-2">Ağırlık (kg)</label>
                    <Input
                      type="number"
                      step="0.1"
                      name="weight"
                      value={formData.weight}
                      onChange={handleInputChange}
                      placeholder="2.5"
                    />
                  </div>
                  <div>
                    <label className="block text-foreground font-medium mb-2">Uzunluk (cm)</label>
                    <Input
                      type="number"
                      name="length"
                      value={formData.length}
                      onChange={handleInputChange}
                      placeholder="45"
                    />
                  </div>
                </div>
              </div>
            </Card>

            {/* Location */}
            <Card className="p-6">
              <h3 className="font-semibold mb-4 flex items-center gap-2">
                <Icon name="location" size="sm" className="text-primary" />
                Konum
              </h3>
              <div className="space-y-4">
                <div className="flex gap-3">
                  <Input
                    type="text"
                    name="location"
                    value={formData.location}
                    onChange={handleInputChange}
                    placeholder="Konum adı veya koordinatlar"
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={getCurrentLocation}
                  >
                    <Icon name="location" size="sm" className="mr-2" />
                    Mevcut Konum
                  </Button>
                </div>
                {coordinates && (
                  <div className="text-sm text-muted-foreground">
                    Koordinatlar: {coordinates.lat.toFixed(4)}, {coordinates.lng.toFixed(4)}
                  </div>
                )}
              </div>
            </Card>

            {/* Additional Details */}
            <Card className="p-6">
              <h3 className="font-semibold mb-4 flex items-center gap-2">
                <Icon name="info" size="sm" className="text-primary" />
                Ek Bilgiler
              </h3>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-foreground font-medium mb-2">Yem</label>
                    <Input
                      type="text"
                      name="bait"
                      value={formData.bait}
                      onChange={handleInputChange}
                      placeholder="Solucan, karides, vb."
                    />
                  </div>
                  <div>
                    <label className="block text-foreground font-medium mb-2">Teknik</label>
                    <Input
                      type="text"
                      name="technique"
                      value={formData.technique}
                      onChange={handleInputChange}
                      placeholder="Oltayla, ağla, vb."
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-foreground font-medium mb-2">Hava Durumu</label>
                  <Input
                    type="text"
                    name="weather"
                    value={formData.weather}
                    onChange={handleInputChange}
                    placeholder="Açık, bulutlu, rüzgarlı, vb."
                  />
                </div>
                <div>
                  <label className="block text-foreground font-medium mb-2">Açıklama</label>
                  <textarea
                    name="caption"
                    value={formData.caption}
                    onChange={handleInputChange}
                    rows={4}
                    className="w-full px-3 py-2 bg-background border border-border rounded-md text-foreground focus:outline-none focus:ring-2 focus:ring-primary resize-none"
                    placeholder="Avlama deneyiminizi paylaşın..."
                  />
                </div>
              </div>
            </Card>

            {/* Submit Button */}
            <div className="flex gap-4">
              <Link href="/" className="flex-1">
                <Button variant="outline" size="lg" className="w-full">
                  <Icon name="arrowLeft" size="sm" className="mr-2" />
                  İptal
                </Button>
              </Link>
              <Button type="submit" variant="primary" size="lg" className="flex-1">
                <Icon name="check" size="sm" className="mr-2" />
                Balığı Paylaş
              </Button>
            </div>
          </form>
        </div>
      </div>
    </Layout>
  );
}
