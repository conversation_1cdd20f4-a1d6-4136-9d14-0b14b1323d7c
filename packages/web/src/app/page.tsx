'use client';

import Link from 'next/link';
import { Layout, Container, Card, Button, Badge, Icon } from '../components/ui';

export default function HomePage() {
  return (
    <Layout showContainer={false}>
      {/* Hero Section */}
      <section className="py-16 md:py-24 lg:py-32 bg-background">
        <Container size="2xl">
          <div className="text-center max-w-6xl mx-auto space-y-8">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-foreground">
              Balık Tut<PERSON> Tu<PERSON>kunu<br className="hidden sm:block" />
              <span className="text-blue-500"> Topluluğu</span>
            </h1>
            <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              15 milyon balıkçının deneyimlerini paylaştığı, en iyi spotları keşfettiği ve 
              avlarını kaydettiği sosyal platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link href="/dashboard">
                <Button size="lg" className="min-w-[200px]">
                  <Icon name="fish" size="md" />
                  Hemen Başla
                </Button>
              </Link>
              <Link href="/map">
                <Button variant="outline" size="lg" className="min-w-[200px]">
                  <Icon name="map" size="md" />
                  Haritayı Keşfet
                </Button>
              </Link>
            </div>
          </div>
        </Container>
      </section>

      {/* Features Section */}
      <section className="py-16 md:py-24 bg-muted/30">
        <Container size="2xl">
          <div className="text-center mb-12 md:mb-20 space-y-6">
            <h2 className="text-3xl md:text-5xl font-bold text-foreground">
              Neden Fishivo?
            </h2>
            <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Balıkçılar için özel olarak tasarlanmış özelliklerle avlama deneyiminizi geliştirin
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-8 lg:gap-12 xl:gap-16 max-w-none mx-auto">
            <Card className="text-center hover:shadow-lg transition-shadow" hover>
              <div className="w-16 h-16 md:w-20 md:h-20 bg-blue-500/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <Icon name="map" size="xl" className="text-blue-500" />
              </div>
              <h3 className="text-xl font-bold text-foreground mb-4">İnteraktif Harita</h3>
              <p className="text-muted-foreground leading-relaxed">
                GPS koordinatları, derinlik haritaları ve gerçek zamanlı spot konumları ile en iyi spotları keşfedin.
              </p>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow" hover>
              <div className="w-16 h-16 md:w-20 md:h-20 bg-green-500/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <Icon name="fish" size="xl" className="text-green-500" />
              </div>
                              <h3 className="text-xl font-bold text-foreground mb-4">Spot Takibi</h3>
              <p className="text-muted-foreground leading-relaxed">
                Yakaladığınız balıkları kaydedin, fotoğraflarla paylaşın ve kişisel istatistiklerinizi takip edin.
              </p>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow" hover>
              <div className="w-16 h-16 md:w-20 md:h-20 bg-orange-500/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <Icon name="weather" size="xl" className="text-orange-500" />
              </div>
              <h3 className="text-xl font-bold text-foreground mb-4">Hava Durumu</h3>
              <p className="text-muted-foreground leading-relaxed">
                7 günlük hava tahminleri, rüzgar bilgileri ve balık tutma için ideal zamanları öğrenin.
              </p>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow" hover>
              <div className="w-16 h-16 md:w-20 md:h-20 bg-purple-500/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <Icon name="profile" size="xl" className="text-purple-500" />
              </div>
              <h3 className="text-xl font-bold text-foreground mb-4">Sosyal Topluluk</h3>
              <p className="text-muted-foreground leading-relaxed">
                Diğer balıkçılarla bağlantı kurun, deneyimlerinizi paylaşın ve yeni arkadaşlıklar edinin.
              </p>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow" hover>
              <div className="w-16 h-16 md:w-20 md:h-20 bg-yellow-500/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <Icon name="star" size="xl" className="text-yellow-500" />
              </div>
              <h3 className="text-xl font-bold text-foreground mb-4">AI Spot Tahmini</h3>
              <p className="text-muted-foreground leading-relaxed mb-4">
                Yapay zeka destekli algoritmalarla en verimli spot noktalarını keşfedin.
              </p>
              <Badge variant="pro">PRO</Badge>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow" hover>
              <div className="w-16 h-16 md:w-20 md:h-20 bg-cyan-500/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <Icon name="dashboard" size="xl" className="text-cyan-500" />
              </div>
              <h3 className="text-xl font-bold text-foreground mb-4">Detaylı Analiz</h3>
              <p className="text-muted-foreground leading-relaxed">
                Spot performansınızı analiz edin, başarı oranlarınızı görün ve gelişim kaydedin.
              </p>
            </Card>
          </div>
        </Container>
      </section>

      {/* Stats Section */}
      <section className="py-16 md:py-24 bg-background">
        <Container size="2xl">
          <div className="text-center mb-12 md:mb-20 space-y-6">
            <h2 className="text-3xl md:text-5xl font-bold text-foreground">
              Türkiye'nin En Büyük Balıkçı Topluluğu
            </h2>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-16 xl:gap-24 max-w-none mx-auto">
            <div className="text-center space-y-3">
              <div className="text-3xl md:text-5xl font-bold text-blue-500">15M+</div>
              <div className="text-muted-foreground">Kayıtlı Balıkçı</div>
            </div>
            <div className="text-center space-y-3">
              <div className="text-3xl md:text-5xl font-bold text-green-500">50M+</div>
                              <div className="text-muted-foreground">Paylaşılan Spot</div>
            </div>
            <div className="text-center space-y-3">
              <div className="text-3xl md:text-5xl font-bold text-orange-500">25K+</div>
              <div className="text-muted-foreground">Balık Türü</div>
            </div>
            <div className="text-center space-y-3">
              <div className="text-3xl md:text-5xl font-bold text-purple-500">180+</div>
              <div className="text-muted-foreground">Ülke</div>
            </div>
          </div>
        </Container>
      </section>

      {/* Pro Features Section */}
      <section className="py-16 md:py-24 bg-muted/30">
        <Container size="2xl">
          <div className="text-center mb-12 md:mb-20 space-y-6">
            <Badge variant="pro" size="lg">PRO ÖZELLİKLER</Badge>
            <h2 className="text-3xl md:text-5xl font-bold text-foreground">
              Fishivo Pro ile Avantajını Katla
            </h2>
            <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Profesyonel balıkçılar için özel olarak tasarlanmış gelişmiş özellikler
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 md:gap-20 xl:gap-32 items-center max-w-none mx-auto">
            <div className="space-y-8">
              <div className="flex items-start gap-5">
                <div className="w-10 h-10 bg-yellow-500/10 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <Icon name="star" size="md" className="text-yellow-500" />
                </div>
                <div className="space-y-2">
                  <h3 className="text-xl font-bold text-foreground">BiteTime™ AI Tahminleri</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    Yapay zeka destekli algoritma ile balıkların en aktif olduğu saatleri öğrenin
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-5">
                <div className="w-10 h-10 bg-blue-500/10 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <Icon name="location" size="md" className="text-blue-500" />
                </div>
                <div className="space-y-2">
                  <h3 className="text-xl font-bold text-foreground">Tam GPS Koordinatları</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    Diğer kullanıcıların paylaştığı meraların tam koordinatlarına erişin
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-5">
                <div className="w-10 h-10 bg-green-500/10 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <Icon name="info" size="md" className="text-green-500" />
                </div>
                <div className="space-y-2">
                  <h3 className="text-xl font-bold text-foreground">Derinlik Haritaları</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    Detaylı batimetrik haritalar ile su derinliklerini görüntüleyin
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-5">
                <div className="w-10 h-10 bg-purple-500/10 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <Icon name="dashboard" size="md" className="text-purple-500" />
                </div>
                <div className="space-y-2">
                  <h3 className="text-xl font-bold text-foreground">Gelişmiş Analizler</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    Kişiselleştirilmiş istatistikler ve performans raporları
                  </p>
                </div>
              </div>
            </div>

            <Card variant="pro" className="text-center">
              <Badge variant="pro" className="mb-4">EN POPÜLER</Badge>
              <h3 className="text-2xl font-bold text-foreground mb-2">Fishivo Pro</h3>
              <div className="text-4xl font-bold text-yellow-500 mb-1">₺29.99</div>
              <div className="text-muted-foreground mb-8">/ay</div>
              
              <ul className="space-y-4 mb-8 text-left">
                <li className="flex items-center gap-3">
                  <Icon name="check" size="sm" className="text-green-500" />
                  <span className="text-foreground">BiteTime™ AI Tahminleri</span>
                </li>
                <li className="flex items-center gap-3">
                  <Icon name="check" size="sm" className="text-green-500" />
                  <span className="text-foreground">Tam GPS Koordinatları</span>
                </li>
                <li className="flex items-center gap-3">
                  <Icon name="check" size="sm" className="text-green-500" />
                  <span className="text-foreground">Derinlik Haritaları</span>
                </li>
                <li className="flex items-center gap-3">
                  <Icon name="check" size="sm" className="text-green-500" />
                  <span className="text-foreground">Gelişmiş Analizler</span>
                </li>
                <li className="flex items-center gap-3">
                  <Icon name="check" size="sm" className="text-green-500" />
                  <span className="text-foreground">Reklamsız Deneyim</span>
                </li>
              </ul>

              <Button variant="pro" size="lg" className="w-full mb-4">
                <Icon name="star" size="sm" />
                Pro'ya Geç
              </Button>
              
              <p className="text-sm text-muted-foreground">
                7 gün ücretsiz deneme • İstediğin zaman iptal et
              </p>
            </Card>
          </div>
        </Container>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-background">
        <Container size="2xl">
          <div className="text-center max-w-4xl mx-auto space-y-8">
            <h2 className="text-3xl md:text-5xl font-bold text-foreground">
              Balık Tutma Tutkunuz mu?
            </h2>
            <p className="text-lg md:text-xl text-muted-foreground leading-relaxed">
              Hemen katılın ve Türkiye'nin en büyük balıkçı topluluğunun bir parçası olun.
              İlk meranızı paylaşın ve diğer balıkçılarla bağlantı kurun.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link href="/add-catch">
                <Button size="lg" className="min-w-[200px]">
                  <Icon name="plus" size="md" />
                  İlk Meranı Ekle
                </Button>
              </Link>
              <Link href="/profile">
                <Button variant="outline" size="lg" className="min-w-[200px]">
                  <Icon name="profile" size="md" />
                  Profil Oluştur
                </Button>
              </Link>
            </div>
          </div>
        </Container>
      </section>
    </Layout>
  );
}
