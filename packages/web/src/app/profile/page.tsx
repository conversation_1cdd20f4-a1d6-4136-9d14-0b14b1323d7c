'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { User, Settings, Fish, Trophy, MapPin, Calendar, Edit2, Plus, CheckCircle, Star, Activity } from 'lucide-react';

import ThemeSettings from '../../components/ThemeSettings';

export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState('overview');
  const [isEditing, setIsEditing] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [catches, setCatches] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const tabs = [
    { id: 'overview', label: 'Genel Bakış', icon: <Activity className="w-4 h-4" /> },
    { id: 'catches', label: 'Avlar', icon: <Fish className="w-4 h-4" /> },
    { id: 'settings', label: '<PERSON>yarlar', icon: <Settings className="w-4 h-4" /> }
  ];

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const userRes = await fetch('/api/users/me');
        if (!userRes.ok) throw new Error('Kullanıcı bilgisi alınamadı');
        const userData = await userRes.json();
        setUser(userData.data);

        const catchesRes = await fetch('/api/users/me/catches');
        if (!catchesRes.ok) throw new Error('Avlar alınamadı');
        const catchesData = await catchesRes.json();
        setCatches(catchesData.data.items || []);
        setError(null);
      } catch (err: any) {
        setError(err.message || 'Bilinmeyen hata');
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  if (loading) return <div>Yükleniyor...</div>;
  if (error) return <div>Hata: {error}</div>;

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b border-border bg-card">
        <div className="max-w-6xl mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg">
                <span className="text-white font-semibold text-xl">{user?.avatar || 'AB'}</span>
              </div>
              
              <div>
                <h1 className="text-2xl font-semibold text-foreground">{user?.full_name || user?.username}</h1>
                <p className="text-muted-foreground">@{user?.username}</p>
                <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <MapPin className="w-4 h-4" />
                    {user?.location || '-'}
                  </span>
                  <span className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    {user?.created_at ? new Date(user.created_at).toLocaleDateString() : '-'} tarihinde katıldı
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Link href="/add-catch" className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                <Plus className="w-4 h-4" />
                Avım Ekle
              </Link>
              <button 
                onClick={() => setIsEditing(!isEditing)}
                className="inline-flex items-center gap-2 border border-border hover:bg-muted text-foreground px-4 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                <Edit2 className="w-4 h-4" />
                Düzenle
              </button>
            </div>
          </div>

          {user?.bio && (
            <p className="mt-4 text-foreground max-w-2xl">{user.bio}</p>
          )}
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-6 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-4 gap-6 mb-8">
          <div className="bg-card border border-border rounded-xl p-6 text-center">
            <div className="text-3xl font-bold text-blue-600 mb-1">{catches.length}</div>
            <div className="text-sm text-muted-foreground">Toplam Av</div>
          </div>
          {/* Diğer istatistikler backendde varsa eklenebilir */}
        </div>

        {/* Tab Navigation */}
        <div className="flex gap-1 mb-8 bg-muted/50 rounded-lg p-1 w-fit">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-background text-foreground shadow-sm' 
                  : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
              }`}
            >
              {tab.icon}
              {tab.label}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-card border border-border rounded-xl p-6">
              <h3 className="font-semibold mb-4 flex items-center gap-2">
                <Activity className="w-5 h-5 text-blue-600" />
                Son Aktiviteler
              </h3>
              <div className="space-y-3">
                <div className="flex items-start gap-3 p-3 rounded-lg bg-muted/30">
                  <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-foreground">2.5 kg Levrek yakaladı</p>
                    <p className="text-xs text-muted-foreground">Bosphorus • 2 gün önce</p>
                  </div>
                </div>
                <div className="flex items-start gap-3 p-3 rounded-lg bg-muted/30">
                  <MapPin className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-foreground">Yeni lokasyon keşfetti</p>
                    <p className="text-xs text-muted-foreground">Karaköy Sahili • 1 hafta önce</p>
                  </div>
                </div>
                <div className="flex items-start gap-3 p-3 rounded-lg bg-muted/30">
                  <Star className="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-foreground">2.1 kg Levrek yakaladı</p>
                    <p className="text-xs text-muted-foreground">2 hafta önce</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-card border border-border rounded-xl p-6">
              <h3 className="font-semibold mb-4 flex items-center gap-2">
                <Trophy className="w-5 h-5 text-orange-600" />
                Bu Ay
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Yakalanan balık</span>
                  <span className="font-semibold">12</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Toplam ağırlık</span>
                  <span className="font-semibold">28.4 kg</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Ziyaret edilen spot</span>
                  <span className="font-semibold">5</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Aktif gün</span>
                  <span className="font-semibold">8</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'catches' && (
          <div className="bg-card border border-border rounded-xl p-6">
            <h3 className="font-semibold mb-4 flex items-center gap-2">
              <Fish className="w-5 h-5 text-blue-600" />
              Son Avlar
            </h3>
            <div className="space-y-3">
              {catches.map((catch_item) => (
                <div key={catch_item.id} className="flex items-center gap-4 p-4 rounded-lg border border-border hover:bg-muted/30 transition-colors">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                    <Fish className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-foreground">{catch_item.species || catch_item.catch_details?.species || '-'}</div>
                    <div className="text-xs text-muted-foreground">{catch_item.weight || catch_item.catch_details?.weight || '-'} • {catch_item.location?.name || '-'}</div>
                    <div className="text-xs text-muted-foreground">{catch_item.created_at ? new Date(catch_item.created_at).toLocaleDateString() : '-'}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'settings' && (
          <div className="space-y-6">
            <div className="bg-card border border-border rounded-xl p-6">
              <h3 className="font-semibold mb-6 flex items-center gap-2">
                <User className="w-5 h-5 text-blue-600" />
                Profil Bilgileri
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">Ad Soyad</label>
                    <input
                      type="text"
                      defaultValue={user?.full_name || user?.username}
                      disabled={!isEditing}
                      className="w-full px-3 py-2 bg-background border border-border rounded-lg text-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">Kullanıcı Adı</label>
                    <input
                      type="text"
                      defaultValue={user?.username}
                      disabled={!isEditing}
                      className="w-full px-3 py-2 bg-background border border-border rounded-lg text-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
                    />
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">E-posta</label>
                    <input
                      type="email"
                      defaultValue={user?.email}
                      disabled={!isEditing}
                      className="w-full px-3 py-2 bg-background border border-border rounded-lg text-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">Konum</label>
                    <input
                      type="text"
                      defaultValue={user?.location || '-'}
                      disabled={!isEditing}
                      className="w-full px-3 py-2 bg-background border border-border rounded-lg text-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
                    />
                  </div>
                </div>
              </div>
              <div className="mt-4">
                <label className="block text-sm font-medium text-foreground mb-2">Hakkımda</label>
                <textarea
                  rows={3}
                  defaultValue={user?.bio}
                  disabled={!isEditing}
                  className="w-full px-3 py-2 bg-background border border-border rounded-lg text-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none disabled:opacity-50 disabled:cursor-not-allowed"
                />
              </div>
              {isEditing && (
                <div className="flex gap-3 mt-6">
                  <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    Kaydet
                  </button>
                  <button 
                    onClick={() => setIsEditing(false)}
                    className="border border-border hover:bg-muted text-foreground px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                  >
                    İptal
                  </button>
                </div>
              )}
            </div>

            <div className="bg-card border border-border rounded-xl p-6">
              <h3 className="font-semibold mb-6 flex items-center gap-2">
                <Settings className="w-5 h-5 text-blue-600" />
                Görünüm Ayarları
              </h3>
              <ThemeSettings />
            </div>

            <div className="bg-card border border-border rounded-xl p-6">
              <h3 className="font-semibold mb-6 flex items-center gap-2">
                <Settings className="w-5 h-5 text-blue-600" />
                Diğer Ayarlar
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between py-3 border-b border-border last:border-0">
                  <div>
                    <h4 className="font-medium text-foreground">Bildirimler</h4>
                    <p className="text-sm text-muted-foreground">Yeni takipçi ve beğeni bildirimleri</p>
                  </div>
                  <button className="bg-muted hover:bg-muted/80 text-foreground px-3 py-1.5 rounded-md text-sm transition-colors">
                    Ayarla
                  </button>
                </div>
                <div className="flex items-center justify-between py-3">
                  <div>
                    <h4 className="font-medium text-foreground">Gizlilik</h4>
                    <p className="text-sm text-muted-foreground">Profil görünürlük ayarları</p>
                  </div>
                  <button className="bg-muted hover:bg-muted/80 text-foreground px-3 py-1.5 rounded-md text-sm transition-colors">
                    Düzenle
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 