import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { Poppins } from "next/font/google";
import { Navigation } from "../components/ui";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  variable: '--font-poppins'
});

export const metadata: Metadata = {
  title: "Fishivo - Balıkçılık Sosyal Platformu",
  description: "Balık tutma deneyimlerini paylaş, spotları keşfet, balıkçılık topluluğuna katıl.",
  keywords: ['balık tutma', 'fishing', 'balı<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'sosyal platform', 'spot'],
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="tr" className={`${geistSans.variable} ${geistMono.variable} ${poppins.variable} antialiased`} suppressHydrationWarning={true} data-theme="dark">
      <body className={`${geistSans.className} ${geistMono.className} ${poppins.className} antialiased`} suppressHydrationWarning={true}>
        <Navigation />
        {children}
      </body>
    </html>
  );
}
