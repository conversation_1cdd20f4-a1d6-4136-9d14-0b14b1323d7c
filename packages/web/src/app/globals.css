@tailwind base;
@tailwind components;
@tailwind utilities;

/* Poppins Font Variables */
:root {
  --font-poppins: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
}

/* CSS Variables for Theme System */
:root {
  /* Colors - Ana renk #121212 */
  --background: 220 8% 7%;
  --foreground: 0 0% 95%;
  --card: 220 8% 8%;
  --card-foreground: 0 0% 95%;
  --popover: 220 8% 8%;
  --popover-foreground: 0 0% 95%;
  --primary: 220 8% 7%;
  --primary-foreground: 0 0% 98%;
  --secondary: 220 8% 12%;
  --secondary-foreground: 0 0% 95%;
  --muted: 220 8% 12%;
  --muted-foreground: 0 0% 65%;
  --accent: 220 8% 12%;
  --accent-foreground: 0 0% 95%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --border: 220 8% 20%;
  --input: 220 8% 15%;
  --ring: 220 8% 7%;
  --radius: 0.75rem;
}

[data-theme="dark"] {
  --background: 220 8% 7%;
  --foreground: 0 0% 95%;
  --card: 220 8% 8%;
  --card-foreground: 0 0% 95%;
  --popover: 220 8% 8%;
  --popover-foreground: 0 0% 95%;
  --primary: 220 8% 7%;
  --primary-foreground: 0 0% 98%;
  --secondary: 220 8% 12%;
  --secondary-foreground: 0 0% 95%;
  --muted: 220 8% 12%;
  --muted-foreground: 0 0% 65%;
  --accent: 220 8% 12%;
  --accent-foreground: 0 0% 95%;
  --destructive: 0 62.8% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --border: 220 8% 20%;
  --input: 220 8% 15%;
  --ring: 220 8% 25%;
}

/* Base Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-poppins);
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground));
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  color: hsl(var(--foreground));
  line-height: 1.3;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
  color: hsl(var(--muted-foreground));
  margin-bottom: 1rem;
}

/* Animations */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  h1 { font-size: 2rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }
}

/* Icon tema uyumlu renkler */
.lucide {
  color: hsl(var(--muted-foreground));
}

/* Button içindeki icon'lar */
button .lucide {
  color: currentColor;
}

/* Link içindeki icon'lar */
a .lucide {
  color: currentColor;
}

/* Fishivo Custom Styles */
.fishivo-header {
  background-color: hsl(var(--card));
  border-bottom: 1px solid hsl(var(--border));
}

.fishivo-nav-link {
  color: hsl(var(--muted-foreground));
  transition: color 0.2s ease;
}

.fishivo-nav-link:hover {
  color: hsl(var(--foreground));
}

.fishivo-button {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border-radius: var(--radius);
  transition: all 0.2s ease;
}

.fishivo-button:hover {
  opacity: 0.9;
}

.fishivo-button-secondary {
  background-color: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
  border-radius: var(--radius);
  transition: all 0.2s ease;
}

.fishivo-button-secondary:hover {
  background-color: hsl(var(--muted));
}
