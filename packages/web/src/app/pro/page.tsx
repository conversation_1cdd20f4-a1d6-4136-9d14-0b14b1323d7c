'use client';

import Link from 'next/link';
import { useState } from 'react';

// Mock user subscription status
const SUBSCRIPTION_STATUS = {
  isPro: false,
  plan: null,
  expiresAt: null,
  cancelledAt: null
};

const PRO_FEATURES = [
  {
    icon: '📍',
    title: 'Tam Konum Bilgileri',
    description: '<PERSON><PERSON><PERSON> balıkçıların yakaladığı balıkların tam GPS koordinatlarını görün',
    category: 'Konum'
  },
  {
    icon: '⭐',
    title: 'AI Spot Tahmini',
    description: 'Ya<PERSON>y zeka ile henüz keşfedilmemiş balık tutma noktalarını keşfedin',
    category: 'AI'
  },
  {
    icon: '⏰',
    title: 'BiteTime™ Tahminleri',
    description: 'Hangi balığın ne zaman en çok ısıracağını öğrenen AI tahminleri',
    category: 'Tahmin'
  },
  {
    icon: '🪝',
    title: '<PERSON> <PERSON><PERSON> Yem Önerileri',
    description: 'Her balık türü ve lokasyon için en etkili yem önerilerini alın',
    category: 'Yem'
  },
  {
    icon: '📊',
    title: 'Derinlik Haritaları',
    description: 'Navionics ve C-Map ile detaylı su altı topografyası',
    category: 'Harita'
  },
  {
    icon: '🌊',
    title: 'Nehir Seviye Verileri',
    description: '10,000+ USGS istasyonundan anlık nehir durumu bilgileri',
    category: 'Veri'
  },
  {
    icon: '🌤️',
    title: '7 Günlük Hava Durumu',
    description: 'Balık tutma için optimize edilmiş detaylı hava tahminleri',
    category: 'Hava'
  },
  {
    icon: '📈',
    title: 'Detaylı İstatistikler',
    description: 'Ay evresi, hava koşulları ve zamanla av performansı analizi',
    category: 'Analiz'
  },
  {
    icon: '🔒',
    title: 'Özel Gruplar',
    description: 'Sadece davetli kişilerle paylaşım yapabileceğiniz özel gruplar',
    category: 'Sosyal'
  },
  {
    icon: '📝',
    title: 'Özel Notlar',
    description: 'Avlarınıza sadece size özel gizli notlar ekleyin',
    category: 'Kişisel'
  },
  {
    icon: '🎯',
    title: 'Waypoint Yönetimi',
            description: 'Gizli spotlarınızı işaretleyin ve arkadaşlarınızla paylaşın',
    category: 'Navigasyon'
  },
  {
    icon: '🚫',
    title: 'Reklamsız Deneyim',
    description: 'Hiç reklam olmadan kesintisiz balık tutma deneyimi',
    category: 'Deneyim'
  }
];

const PRICING_PLANS = [
  {
    id: 'monthly',
    name: 'Aylık Pro',
    price: '₺29.99',
    period: '/ay',
    savings: null,
    features: ['Tüm Pro özellikler', 'İstediğin zaman iptal', 'Anlık aktivasyon'],
    popular: false
  },
  {
    id: 'yearly',
    name: 'Yıllık Pro',
    price: '₺199.99',
    period: '/yıl',
    savings: '₺160 tasarruf',
    features: ['Tüm Pro özellikler', '2 ay ücretsiz', '%45 indirim', 'Öncelikli destek'],
    popular: true
  },
  {
    id: 'lifetime',
    name: 'Ömür Boyu Pro',
    price: '₺999.99',
    period: 'tek seferlik',
    savings: 'En iyi değer',
    features: ['Tüm Pro özellikler', 'Ömür boyu erişim', 'Gelecekteki özellikler', 'VIP destek'],
    popular: false
  }
];

const TESTIMONIALS = [
  {
    name: 'Ahmet Balıkçı',
    location: 'İstanbul',
    text: 'Pro özellikler sayesinde av başarım %60 arttı. BiteTime tahminleri gerçekten çalışıyor!',
    avatar: '👨‍🎣',
    catches: 234
  },
  {
    name: 'Fatma Denizci',
    location: 'Antalya',
    text: 'Spot tahminleri sayesinde kimsenin bilmediği muhteşem yerler keşfettim.',
    avatar: '👩‍🎣',
    catches: 187
  },
  {
    name: 'Mehmet Göl',
    location: 'Ankara',
    text: 'Derinlik haritaları olmadan artık balığa çıkmıyorum. Vazgeçilmez!',
    avatar: '🧑‍🎣',
    catches: 156
  }
];

export default function ProPage() {
  const [selectedPlan, setSelectedPlan] = useState('yearly');
  const [showTrialModal, setShowTrialModal] = useState(false);

  return (
    <div className="min-h-screen bg-black">
      {/* Header */}
      <header className="sticky top-0 z-50 bg-black border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-4 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/" className="text-white hover:text-gray-300">
              ← Ana Sayfa
            </Link>
            <h1 className="text-xl font-bold text-white">⭐ Fishivo Pro</h1>
          </div>
          <Link href="/map" className="fishivo-button">
            🗺️ Haritaya Dön
          </Link>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <div className="mb-8">
            <div className="inline-flex items-center space-x-2 bg-yellow-500 text-black px-4 py-2 rounded-full font-bold mb-6">
              <span>⭐</span>
              <span>PRO ÜYELERİ %60 DAHA FAZLA BALIK YAKALIYOR</span>
            </div>
            <h1 className="text-5xl font-bold text-white mb-6">
              Fishivo Pro ile <br />
              <span className="text-yellow-400">Daha Akıllı</span> Balık Tutun
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Yapay zeka destekli tahminler, detaylı konum bilgileri ve profesyonel araçlarla 
              balık tutma deneyiminizi bir üst seviyeye taşıyın.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button 
              onClick={() => setShowTrialModal(true)}
              className="bg-yellow-500 text-black px-8 py-4 rounded-lg font-bold text-lg hover:bg-yellow-400 transition-colors"
            >
              🎁 7 Gün Ücretsiz Deneyin
            </button>
            <button className="bg-gray-800 text-white px-8 py-4 rounded-lg font-medium text-lg hover:bg-gray-700 transition-colors">
              📺 Demo Videosu İzle
            </button>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-16 px-4 bg-gray-900">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">
            Pro Özellikler
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {PRO_FEATURES.map((feature, index) => (
              <div key={index} className="fishivo-card hover:border-yellow-400 transition-all duration-300 transform hover:-translate-y-1">
                <div className="flex items-start space-x-4">
                  <div className="text-3xl">{feature.icon}</div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="text-white font-semibold">{feature.title}</h3>
                      <span className="bg-yellow-500 text-black text-xs px-2 py-1 rounded font-medium">
                        {feature.category}
                      </span>
                    </div>
                    <p className="text-gray-300 text-sm">{feature.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-16 px-4">
        <div className="max-w-5xl mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">
            Pro Planları
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {PRICING_PLANS.map((plan) => (
              <div 
                key={plan.id}
                className={`relative p-6 rounded-lg border-2 transition-all duration-300 cursor-pointer ${
                  plan.popular 
                    ? 'border-yellow-400 bg-gradient-to-b from-yellow-900/20 to-gray-900' 
                    : selectedPlan === plan.id
                    ? 'border-blue-400 bg-gray-900'
                    : 'border-gray-700 bg-gray-900 hover:border-gray-600'
                }`}
                onClick={() => setSelectedPlan(plan.id)}
              >
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-yellow-500 text-black px-4 py-1 rounded-full text-sm font-bold">
                      EN POPÜLER
                    </span>
                  </div>
                )}
                
                <div className="text-center mb-6">
                  <h3 className="text-white font-bold text-lg mb-2">{plan.name}</h3>
                  <div className="flex items-baseline justify-center space-x-1">
                    <span className="text-3xl font-bold text-white">{plan.price}</span>
                    <span className="text-gray-400">{plan.period}</span>
                  </div>
                  {plan.savings && (
                    <p className="text-yellow-400 text-sm font-medium mt-2">{plan.savings}</p>
                  )}
                </div>

                <ul className="space-y-3 mb-6">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center space-x-3">
                      <span className="text-green-400">✓</span>
                      <span className="text-gray-300 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>

                <button 
                  className={`w-full py-3 rounded-lg font-medium transition-colors ${
                    plan.popular
                      ? 'bg-yellow-500 text-black hover:bg-yellow-400'
                      : 'bg-blue-600 text-white hover:bg-blue-500'
                  }`}
                >
                  {plan.id === 'lifetime' ? 'Şimdi Satın Al' : 'Denemeyi Başlat'}
                </button>
              </div>
            ))}
          </div>

          <div className="text-center mt-8">
            <p className="text-gray-400 text-sm mb-4">
              💳 Güvenli ödeme • 🔄 İstediğin zaman iptal • 🛡️ 30 gün para iade garantisi
            </p>
            <div className="flex justify-center space-x-4 text-gray-500">
              <span>💳 Kredi Kartı</span>
              <span>📱 Apple Pay</span>
              <span>🅿️ PayPal</span>
              <span>💰 Google Pay</span>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16 px-4 bg-gray-900">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">
            Pro Üyeler Ne Diyor?
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {TESTIMONIALS.map((testimonial, index) => (
              <div key={index} className="fishivo-card">
                <div className="flex items-center space-x-3 mb-4">
                  <span className="text-3xl">{testimonial.avatar}</span>
                  <div>
                    <h4 className="text-white font-semibold">{testimonial.name}</h4>
                    <p className="text-gray-400 text-sm">{testimonial.location}</p>
                    <p className="text-yellow-400 text-xs">{testimonial.catches} av kaydı</p>
                  </div>
                </div>
                <p className="text-gray-300 italic">"{testimonial.text}"</p>
                <div className="flex text-yellow-400 mt-3">
                  {'⭐'.repeat(5)}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">
            Sıkça Sorulan Sorular
          </h2>
          <div className="space-y-6">
            {[
              {
                q: 'Pro üyeliği nasıl iptal edebilirim?',
                a: 'İstediğiniz zaman profil ayarlarınızdan tek tıkla iptal edebilirsiniz. İptal sonrası mevcut dönem sonuna kadar Pro özellikleriniz aktif kalır.'
              },
              {
                q: 'Ücretsiz deneme döneminde ücret kesilir mi?',
                a: 'Hayır, 7 günlük deneme tamamen ücretsizdir. Deneme bitiminden önce iptal ederseniz hiç ücret ödememezsiniz.'
              },
              {
                q: 'Pro özellikler çevrimdışı çalışır mı?',
                a: 'Evet, indirilen haritalar ve kayıtlı veriler çevrimdışı kullanılabilir. Ancak güncel tahminler için internet bağlantısı gerekir.'
              },
              {
                q: 'Aile paylaşımı var mı?',
                a: 'Evet, Pro üyeliğinizi aynı evde yaşayan 6 kişiye kadar paylaşabilirsiniz.'
              }
            ].map((faq, index) => (
              <div key={index} className="fishivo-card">
                <h3 className="text-white font-semibold mb-2">❓ {faq.q}</h3>
                <p className="text-gray-300">{faq.a}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Trial Modal */}
      {showTrialModal && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-900 rounded-lg p-6 max-w-md w-full">
            <div className="text-center">
              <div className="text-4xl mb-4">🎁</div>
              <h3 className="text-white font-bold text-xl mb-4">7 Gün Ücretsiz Deneme</h3>
              <p className="text-gray-300 mb-6">
                Pro özelliklerinizi hemen aktive edin. İlk 7 gün tamamen ücretsiz, 
                istediğiniz zaman iptal edebilirsiniz.
              </p>
              
              <div className="space-y-3">
                <button className="w-full bg-yellow-500 text-black py-3 rounded-lg font-bold hover:bg-yellow-400 transition-colors">
                  Denemeyi Başlat
                </button>
                <button 
                  onClick={() => setShowTrialModal(false)}
                  className="w-full bg-gray-700 text-white py-3 rounded-lg font-medium hover:bg-gray-600 transition-colors"
                >
                  Daha Sonra
                </button>
              </div>
              
              <p className="text-gray-500 text-xs mt-4">
                Kredi kartı bilgisi gereklidir, ancak 7 gün boyunca ücret kesilmez
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 