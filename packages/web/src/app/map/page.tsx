'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Map as MapIcon, Satellite, Copy, Share2, Navigation, Trees, Sun, Moon } from 'lucide-react';
import MapComponent from '@/components/map/MapComponent';

interface LocationData {
  lat: number;
  lng: number;
}

export default function MapPage() {
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(null);
  const [currentStyle, setCurrentStyle] = useState('streets-v12');

  // MAPBOX DOKÜMANTASYONU: Style seçenekleri
  const mapStyles = [
    { id: 'streets-v12', name: '<PERSON><PERSON>', icon: 'map' },
    { id: 'satellite-streets-v12', name: 'Uydu', icon: 'satellite' },
    { id: 'outdoors-v12', name: 'Do<PERSON><PERSON>', icon: 'trees' },
    { id: 'light-v11', name: '<PERSON><PERSON><PERSON><PERSON>', icon: 'sun' },
    { id: 'dark-v11', name: '<PERSON><PERSON>', icon: 'moon' }
  ];

  const handleLocationClick = (lat: number, lng: number) => {
    setSelectedLocation({ lat, lng });
    console.log('📍 Konum seçildi:', { lat, lng });
  };

  // MAPBOX DOKÜMANTASYONU: Style değiştirme
  const handleStyleChange = (styleId: string) => {
    setCurrentStyle(styleId);
    console.log('🎨 Harita stili değiştirildi:', styleId);
  };



  // KOPYALA FONKSİYONU
  const handleCopyCoordinates = async () => {
    if (!selectedLocation) return;

    const coordinates = `${selectedLocation.lat.toFixed(6)}, ${selectedLocation.lng.toFixed(6)}`;

    try {
      await navigator.clipboard.writeText(coordinates);
      console.log('✅ Koordinatlar kopyalandı:', coordinates);
      // TODO: Toast notification eklenebilir
    } catch (err) {
      console.error('❌ Kopyalama hatası:', err);
    }
  };

  // PAYLAŞ FONKSİYONU
  const handleShareLocation = async () => {
    if (!selectedLocation) return;

    const { lat, lng } = selectedLocation;
    const shareData = {
      title: 'Balıkçılık Noktası - Fishivo',
      text: `Koordinatlar: ${lat.toFixed(6)}, ${lng.toFixed(6)}`,
      url: `https://www.google.com/maps?q=${lat},${lng}`
    };

    try {
      if (navigator.share) {
        await navigator.share(shareData);
        console.log('✅ Konum paylaşıldı');
      } else {
        // Fallback: URL'yi kopyala
        await navigator.clipboard.writeText(shareData.url);
        console.log('✅ Konum linki kopyalandı');
      }
    } catch (err) {
      console.log('ℹ️ Paylaşım iptal edildi');
    }
  };

  // NAVİGASYON FONKSİYONU
  const handleNavigate = () => {
    if (!selectedLocation) return;

    const { lat, lng } = selectedLocation;

    // Mobil cihazlarda Google Maps uygulamasını aç
    if (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
      window.open(`https://maps.google.com/maps?q=${lat},${lng}&navigate=yes`, '_blank');
    } else {
      // Desktop'ta Google Maps web sitesini aç
      window.open(`https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`, '_blank');
    }

    console.log('🧭 Navigasyon açıldı:', { lat, lng });
  };

  console.log('🏗️ MapPage render edildi');

  return (
    <div className="fixed inset-0 bg-background overflow-hidden">
      {/* Icon renkleri için tema uyumlu CSS */}
      <style jsx global>{`
        .lucide {
          color: hsl(var(--foreground)) !important;
        }

        /* Button içindeki icon'lar için */
        button .lucide {
          color: currentColor !important;
        }

        /* Primary button'larda beyaz icon */
        button[class*="bg-blue"] .lucide {
          color: white !important;
        }
      `}</style>
      <div className="h-[calc(100vh-64px)] w-full flex overflow-hidden mt-16">
        {/* Sol Sidebar - Başka içerik için */}
        <div className="w-64 bg-card border-r border-border flex flex-col">
          <div className="px-6 py-4 border-b border-border">
            <h2 className="text-base font-semibold text-foreground">Harita Kontrolleri</h2>
            <p className="text-xs text-muted-foreground mt-1">Harita stilini seçin</p>
          </div>

          {/* Harita Style Kontrolleri */}
          <div className="px-6 py-4 border-b border-border">
            <div className="space-y-2">
              {mapStyles.map((style) => (
                <Button
                  key={style.id}
                  variant={currentStyle === style.id ? "primary" : "secondary"}
                  size="sm"
                  onClick={() => handleStyleChange(style.id)}
                  icon={style.icon}
                  className="w-full justify-start"
                >
                  {style.name}
                </Button>
              ))}
            </div>
          </div>

          {/* Seçili Konum */}
          {selectedLocation && (
            <div className="px-6 py-4 border-b border-border bg-muted/30">
              <div className="mb-3">
                <h3 className="text-sm font-medium text-foreground mb-2">Seçili Konum</h3>
                <div className="text-xs text-muted-foreground space-y-1">
                  <div>Enlem: {selectedLocation.lat.toFixed(6)}</div>
                  <div>Boylam: {selectedLocation.lng.toFixed(6)}</div>
                </div>
              </div>

              {/* Aksiyon Butonları */}
              <div className="grid grid-cols-2 gap-2">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={handleCopyCoordinates}
                  icon={<Copy size={14} />}
                  className="w-full"
                >
                  Kopyala
                </Button>

                <Button
                  variant="secondary"
                  size="sm"
                  onClick={handleShareLocation}
                  icon={<Share2 size={14} />}
                  className="w-full"
                >
                  Paylaş
                </Button>

                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleNavigate}
                  icon={<Navigation size={14} />}
                  className="col-span-2 w-full"
                >
                  Navigasyon
                </Button>
              </div>
            </div>
          )}

          <div className="flex-1 px-6 py-4">
            <p className="text-xs text-muted-foreground">
              Harita üzerine tıklayarak konum seçebilirsiniz.
            </p>
          </div>
        </div>

        {/* Harita container - FILL & FIT */}
        <div className="flex-1 relative h-full w-full overflow-hidden">
          {/* Style kontrol butonları - CONTAINER YOK */}
          <div className="absolute top-4 right-4 z-10 flex flex-col space-y-2">
              {mapStyles.map((style) => (
                <Button
                  key={style.id}
                  variant={currentStyle === style.id ? "primary" : "secondary"}
                  size="sm"
                  onClick={() => handleStyleChange(style.id)}
                  icon={style.icon}
                  className="w-10 h-10 p-0 justify-center"
                >
                  <span className="sr-only">{style.name}</span>
                </Button>
              ))}
          </div>



          {/* Harita - FULL SIZE */}
          <div className="absolute inset-0">
            <MapComponent
              onLocationClick={handleLocationClick}
              mapStyle={currentStyle}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
