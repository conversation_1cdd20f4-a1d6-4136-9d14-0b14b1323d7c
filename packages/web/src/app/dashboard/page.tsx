'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Layout, Container, Card, Button, Badge, Icon } from '../../components/ui';
import { supabase } from '../../lib/supabase';
import { apiClient } from '../../lib/api';
import type { FishSpecies, FishingLocation, Equipment, User } from '../../../../shared/types/index';


// Admin kontrolü geçici olarak devre dışı
function useAdminAuth() {
  return { isAdmin: true, isLoading: false };
}

export default function AdminDashboardPage() {
  const [activeTab, setActiveTab] = useState('overview');
  const { isAdmin, isLoading } = useAdminAuth();

  // State for each data section
  const [posts, setPosts] = useState<any[]>([]);
  const [postsLoading, setPostsLoading] = useState(false);
  const [postsError, setPostsError] = useState<string | null>(null);

  const [species, setSpecies] = useState<FishSpecies[]>([]);
  const [speciesLoading, setSpeciesLoading] = useState(false);
  const [speciesError, setSpeciesError] = useState<string | null>(null);

  const [areas, setAreas] = useState<FishingLocation[]>([]);
  const [areasLoading, setAreasLoading] = useState(false);
  const [areasError, setAreasError] = useState<string | null>(null);

  const [equipment, setEquipment] = useState<Equipment[]>([]);
  const [equipmentLoading, setEquipmentLoading] = useState(false);
  const [equipmentError, setEquipmentError] = useState<string | null>(null);

  const [users, setUsers] = useState<User[]>([]);
  const [usersLoading, setUsersLoading] = useState(false);
  const [usersError, setUsersError] = useState<string | null>(null);

  // Admin stats
  const [adminStats, setAdminStats] = useState<any>(null);
  const [statsLoading, setStatsLoading] = useState(false);
  const [statsError, setStatsError] = useState<string | null>(null);

  // Fetch posts
  useEffect(() => {
    if (activeTab === 'catches' || activeTab === 'posts' || activeTab === 'overview' || activeTab === 'analytics') {
      setPostsLoading(true);
      setPostsError(null);
      supabase.from('posts').select('*').order('created_at', { ascending: false })
        .then(({ data, error }) => {
          if (error) setPostsError(error.message);
          else setPosts(data || []);
          setPostsLoading(false);
        });
    }
  }, [activeTab]);

  // Fetch fish species
  useEffect(() => {
    if (activeTab === 'fish-species') {
      setSpeciesLoading(true);
      setSpeciesError(null);
      apiClient.getSpecies()
        .then(res => {
          if (res.success) setSpecies(res.data || []);
          else setSpeciesError('Veri alınamadı');
        })
        .catch(e => setSpeciesError(e.message))
        .finally(() => setSpeciesLoading(false));
    }
  }, [activeTab]);

  // Fetch fishing areas
  useEffect(() => {
    if (activeTab === 'fishing-areas') {
      setAreasLoading(true);
      setAreasError(null);
      supabase.from('fishing_areas').select('*').order('created_at', { ascending: false })
        .then(({ data, error }) => {
          if (error) setAreasError(error.message);
          else setAreas(data || []);
          setAreasLoading(false);
        });
    }
  }, [activeTab]);

  // Fetch equipment
  useEffect(() => {
    if (activeTab === 'equipment') {
      setEquipmentLoading(true);
      setEquipmentError(null);
      supabase.from('equipment').select('*').order('created_at', { ascending: false })
        .then(({ data, error }) => {
          if (error) setEquipmentError(error.message);
          else setEquipment(data || []);
          setEquipmentLoading(false);
        });
    }
  }, [activeTab]);

  // Fetch users
  useEffect(() => {
    if (activeTab === 'users') {
      setUsersLoading(true);
      setUsersError(null);
      supabase.from('users').select('*').order('created_at', { ascending: false })
        .then(({ data, error }) => {
          if (error) setUsersError(error.message);
          else setUsers(data || []);
          setUsersLoading(false);
        });
    }
  }, [activeTab]);

  // Fetch admin stats (example: total users, posts, etc.)
  useEffect(() => {
    if (activeTab === 'overview' || activeTab === 'analytics') {
      setStatsLoading(true);
      setStatsError(null);
      Promise.all([
        supabase.from('users').select('id', { count: 'exact', head: true }),
        supabase.from('posts').select('id', { count: 'exact', head: true }),
        supabase.from('users').select('id, is_pro', { count: 'exact' }),
        supabase.from('posts').select('created_at'),
      ]).then(([usersRes, postsRes, proRes, postsTodayRes]) => {
        setAdminStats({
          totalUsers: usersRes.count || 0,
          totalPosts: postsRes.count || 0,
          proUsers: (proRes.data || []).filter((u: any) => u.is_pro).length,
          newUsersToday: (usersRes.data || []).filter((u: any) => {
            const today = new Date();
            const created = new Date(u.created_at);
            return created.toDateString() === today.toDateString();
          }).length,
          postsToday: (postsTodayRes.data || []).filter((p: any) => {
            const today = new Date();
            const created = new Date(p.created_at);
            return created.toDateString() === today.toDateString();
          }).length,
        });
      }).catch(e => setStatsError(e.message)).finally(() => setStatsLoading(false));
    }
  }, [activeTab]);

  // Loading state
  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Yetki kontrolü yapılıyor...</p>
          </div>
        </div>
      </Layout>
    );
  }

  // Admin değilse bu component render edilmez (useAdminAuth redirect yapar)
  if (!isAdmin) {
    return null;
  }

  return (
    <Layout>
      {/* Admin Page Header */}
      <div className="py-6">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2 flex items-center gap-3">
              <Icon name="dashboard" size="lg" className="text-red-500" />
              Admin Panel
            </h1>
            <p className="text-muted-foreground">
              Fishivo Yönetim Paneli • Sistem Kontrolü
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="outline">
              <Icon name="settings" size="sm" className="mr-2" />
              Ayarlar
            </Button>
            <Button variant="primary">
              <Icon name="plus" size="sm" className="mr-2" />
              Yeni Kullanıcı
            </Button>
            <Button variant="secondary">
              <Icon name="settings" size="sm" className="mr-2" />
              Sistem Ayarları
            </Button>
          </div>
        </div>

        {/* System Status */}
        <Card className="p-6 mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-foreground mb-2">Sistem Durumu</h3>
              <p className="text-sm text-muted-foreground">Tüm sistemler normal çalışıyor</p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-sm text-foreground">API</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-sm text-foreground">Database</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-sm text-foreground">Storage</span>
              </div>
            </div>
          </div>
        </Card>

        {/* Admin Tab Navigation */}
        <div className="flex gap-2 mb-6 flex-wrap">
          {[
            { id: 'overview', label: 'Genel Bakış', icon: 'dashboard' },
            { id: 'catches', label: 'Avlar', icon: 'fish' },
            { id: 'fish-species', label: 'Balık Türleri', icon: 'fish' },
            { id: 'fishing-areas', label: 'Spot Yönetimi', icon: 'map' },
            { id: 'equipment', label: 'Ekipman Yönetimi', icon: 'settings' },
            { id: 'users', label: 'Kullanıcı Yönetimi', icon: 'profile' },
            { id: 'reports', label: 'Şikayetler', icon: 'warning' },
            { id: 'settings', label: 'Sistem Ayarları', icon: 'settings' }
          ].map((tab) => (
            <Button
              key={tab.id}
              variant={activeTab === tab.id ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setActiveTab(tab.id)}
              className="flex items-center gap-2"
            >
              <Icon name={tab.icon as any} size="xs" />
              {tab.label}
            </Button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {activeTab === 'overview' && (
              <div className="space-y-6">
                <Card className="p-6">
                  <h3 className="font-semibold mb-4 flex items-center gap-2">
                    <Icon name="dashboard" size="sm" className="text-primary" />
                    Sistem Özeti
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h4 className="font-medium text-foreground">Kullanıcı İstatistikleri</h4>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                          <span className="text-sm text-foreground">Toplam Kullanıcı</span>
                          <span className="font-semibold text-blue-500">{adminStats?.totalUsers || 0}</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                          <span className="text-sm text-foreground">Pro Üyeler</span>
                          <span className="font-semibold text-yellow-500">{adminStats?.proUsers || 0}</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                          <span className="text-sm text-foreground">Bugün Kayıt</span>
                          <span className="font-semibold text-green-500">+{adminStats?.newUsersToday || 0}</span>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <h4 className="font-medium text-foreground">İçerik İstatistikleri</h4>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                          <span className="text-sm text-foreground">Toplam Post</span>
                          <span className="font-semibold text-blue-500">{adminStats?.totalPosts || 0}</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                          <span className="text-sm text-foreground">Bugün Paylaşım</span>
                          <span className="font-semibold text-green-500">+{adminStats?.postsToday || 0}</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                          <span className="text-sm text-foreground">Bekleyen Şikayet</span>
                          <span className="font-semibold text-red-500">{adminStats?.pendingReports || 0}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>

                <Card className="p-6">
                  <h3 className="font-semibold mb-4 flex items-center gap-2">
                    <Icon name="star" size="sm" className="text-primary" />
                    Hızlı Eylemler
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Button variant="outline" className="h-16 flex-col">
                      <Icon name="plus" size="md" className="mb-2" />
                      <span>Yeni Kullanıcı Ekle</span>
                    </Button>
                    <Button variant="outline" className="h-16 flex-col">
                      <Icon name="settings" size="md" className="mb-2" />
                      <span>Sistem Ayarları</span>
                    </Button>
                  </div>
                </Card>
              </div>
            )}

            {activeTab === 'catches' && (
              <div className="space-y-6">
                <Card className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-semibold flex items-center gap-2">
                      <Icon name="fish" size="sm" className="text-primary" />
                      Av Kayıtları
                    </h3>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Icon name="search" size="sm" className="mr-2" />
                        Post Ara
                      </Button>
                      <Button variant="outline" size="sm">
                        <Icon name="filter" size="sm" className="mr-2" />
                        Filtrele
                      </Button>
                      <Button variant="destructive" size="sm">
                        <Icon name="trash" size="sm" className="mr-2" />
                        Toplu Sil
                      </Button>
                    </div>
                  </div>

                  {/* Mobile Posts List */}
                  <div className="space-y-4">
                    {posts.map((post) => (
                      <div key={post.id} className="border rounded-lg p-4 bg-muted/30">
                        <div className="flex gap-4">
                          {/* Post Image */}
                          <div className="w-24 h-24 bg-muted rounded-lg overflow-hidden flex-shrink-0">
                            <img
                              src={post.image}
                              alt="Post"
                              className="w-full h-full object-cover"
                            />
                          </div>

                          {/* Post Content */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between mb-2">
                              <div className="flex items-center gap-2">
                                <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center text-primary text-sm font-semibold">
                                  {post.user.avatar}
                                </div>
                                <div>
                                  <p className="font-medium text-foreground">{post.user.name}</p>
                                  <p className="text-xs text-muted-foreground">{post.user.username}</p>
                                </div>
                                <div className="flex gap-1">
                                  {post.status === 'flagged' && <Badge variant="warning">Şikayetli</Badge>}
                                  {post.status === 'reported' && <Badge variant="destructive">Raporlu</Badge>}
                                  {post.status === 'active' && <Badge variant="success">Aktif</Badge>}
                                </div>
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {new Date(post.created_at).toLocaleDateString('tr-TR')}
                              </div>
                            </div>

                            <p className="text-sm text-foreground mb-2">{post.content}</p>

                            {/* Catch Details */}
                            <div className="flex items-center gap-4 mb-2 text-xs text-muted-foreground">
                              <span>🐟 {post.catch_details.species}</span>
                              <span>⚖️ {post.catch_details.weight}kg</span>
                              <span>📏 {post.catch_details.length}cm</span>
                              <span>📍 {post.location.name}</span>
                            </div>

                            {/* Engagement Stats */}
                            <div className="flex items-center gap-4 text-xs text-muted-foreground">
                              <span>❤️ {post.likes} beğeni</span>
                              <span>💬 {post.comments} yorum</span>
                              {post.reports > 0 && (
                                <span className="text-red-500">🚨 {post.reports} şikayet</span>
                              )}
                            </div>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex flex-col gap-2">
                            <Button variant="outline" size="sm">
                              <Icon name="eye" size="sm" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Icon name="edit" size="sm" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Icon name="map" size="sm" />
                            </Button>
                            <Button variant="destructive" size="sm">
                              <Icon name="trash" size="sm" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>
              </div>
            )}


            {activeTab === 'fish-species' && (
              <div className="space-y-6">
                <Card className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-semibold flex items-center gap-2">
                      <Icon name="fish" size="sm" className="text-primary" />
                      Balık Türleri Yönetimi
                    </h3>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Icon name="search" size="sm" className="mr-2" />
                        Tür Ara
                      </Button>
                      <Button variant="outline" size="sm">
                        <Icon name="filter" size="sm" className="mr-2" />
                        Filtrele
                      </Button>
                      <Button variant="primary" size="sm">
                        <Icon name="plus" size="sm" className="mr-2" />
                        Yeni Tür Ekle
                      </Button>
                    </div>
                  </div>

                  {/* Fish Species List */}
                  <div className="space-y-4">
                    {species.map((fish) => (
                      <div key={fish.id} className="border rounded-lg p-4 bg-muted/30">
                        <div className="flex gap-4">
                          {/* Fish Image */}
                          <div className="w-24 h-24 bg-muted rounded-lg overflow-hidden flex-shrink-0">
                            <img
                              src={fish.image}
                              alt={fish.name}
                              className="w-full h-full object-cover"
                            />
                          </div>

                          {/* Fish Details */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between mb-2">
                              <div>
                                <h4 className="font-medium text-foreground">{fish.name}</h4>
                                <p className="text-sm text-muted-foreground italic">{fish.scientificName}</p>
                                <p className="text-sm text-foreground mt-1">{fish.description}</p>
                              </div>
                            </div>

                            {/* Fish Specs */}
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3 text-xs">
                              <div className="p-2 bg-muted/50 rounded">
                                <span className="text-muted-foreground">Boyut:</span>
                                <p className="font-medium">{fish.minWeight}-{fish.maxWeight} cm</p>
                              </div>
                              <div className="p-2 bg-muted/50 rounded">
                                <span className="text-muted-foreground">Ortalama Ağırlık:</span>
                                <p className="font-medium">{fish.averageLength} kg</p>
                              </div>
                              <div className="p-2 bg-muted/50 rounded">
                                <span className="text-muted-foreground">Zorluk:</span>
                                <p className="font-medium">{fish.difficulty}</p>
                              </div>
                              <div className="p-2 bg-muted/50 rounded">
                                <span className="text-muted-foreground">Sezon:</span>
                                <p className="font-medium">{Array.isArray(fish.season) ? fish.season.join(', ') : fish.season}</p>
                              </div>
                            </div>

                            {/* Habitat & Bait */}
                            <div className="flex flex-wrap gap-2 mb-2">
                              <span className="text-xs text-muted-foreground">Habitat:</span>
                              {(fish.habitat ? fish.habitat.split(',').map((h) => (
                                <Badge key={h} variant="outline" className="text-xs">🏞️ {h}</Badge>
                              )) : [])}
                            </div>
                            <div className="flex flex-wrap gap-2">
                              <span className="text-xs text-muted-foreground">Yem:</span>
                              {(fish.baitTypes || []).map((b) => (
                                <Badge key={b} variant="outline" className="text-xs">🎣 {b}</Badge>
                              ))}
                            </div>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex flex-col gap-2">
                            <Button variant="outline" size="sm">
                              <Icon name="eye" size="sm" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Icon name="edit" size="sm" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Icon name="copy" size="sm" />
                            </Button>
                            <Button variant="destructive" size="sm">
                              <Icon name="trash" size="sm" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>
              </div>
            )}

            {activeTab === 'fishing-areas' && (
              <div className="space-y-6">
                <Card className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-semibold flex items-center gap-2">
                      <Icon name="map" size="sm" className="text-primary" />
                      Spot Yönetimi
                    </h3>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Icon name="search" size="sm" className="mr-2" />
                                                  Spot Ara
                      </Button>
                      <Button variant="outline" size="sm">
                        <Icon name="filter" size="sm" className="mr-2" />
                        Filtrele
                      </Button>
                      <Button variant="primary" size="sm">
                        <Icon name="plus" size="sm" className="mr-2" />
                                                  Yeni Spot Ekle
                      </Button>
                    </div>
                  </div>

                  {/* Fishing Areas List */}
                  <div className="space-y-4">
                    {areas.map((area) => (
                      <div key={area.id} className="border rounded-lg p-4 bg-muted/30">
                        <div className="flex gap-4">
                          {/* Area Image */}
                          <div className="w-24 h-24 bg-muted rounded-lg overflow-hidden flex-shrink-0">
                            <img
                              src={area.image}
                              alt={area.name}
                              className="w-full h-full object-cover"
                            />
                          </div>

                          {/* Area Details */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between mb-2">
                              <div>
                                <h4 className="font-medium text-foreground">{area.name}</h4>
                                <p className="text-sm text-muted-foreground">{area.region || '-'} • {area.waterType === 'saltwater' ? 'Deniz' : area.waterType === 'freshwater' ? 'Tatlı Su' : '-'}</p>
                                <p className="text-sm text-foreground mt-1">{area.description}</p>
                              </div>
                            </div>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex flex-col gap-2">
                            <Button variant="outline" size="sm">
                              <Icon name="eye" size="sm" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Icon name="map" size="sm" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Icon name="edit" size="sm" />
                            </Button>
                            {area.status === 'pending' && (
                              <Button variant="success" size="sm">
                                <Icon name="check" size="sm" />
                              </Button>
                            )}
                            <Button variant="destructive" size="sm">
                              <Icon name="trash" size="sm" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>
              </div>
            )}

            {activeTab === 'equipment' && (
              <div className="space-y-6">
                <Card className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-semibold flex items-center gap-2">
                      <Icon name="settings" size="sm" className="text-primary" />
                      Ekipman Yönetimi
                    </h3>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Icon name="search" size="sm" className="mr-2" />
                        Ekipman Ara
                      </Button>
                      <Button variant="outline" size="sm">
                        <Icon name="filter" size="sm" className="mr-2" />
                        Filtrele
                      </Button>
                      <Button variant="primary" size="sm">
                        <Icon name="plus" size="sm" className="mr-2" />
                        Yeni Ekipman Ekle
                      </Button>
                    </div>
                  </div>

                  {/* Equipment Categories */}
                  <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
                    {['Makine', 'Olta', 'Yem', 'İğne', 'Suni Yem'].map((category) => (
                      <div key={category} className="p-3 bg-muted/30 rounded-lg text-center">
                        <div className="text-lg font-semibold text-foreground">
                          {equipment.filter(eq => eq.category === category).length}
                        </div>
                        <div className="text-sm text-muted-foreground">{category}</div>
                      </div>
                    ))}
                  </div>

                  {/* Equipment List */}
                  <div className="space-y-4">
                    {equipment.map((equipment) => (
                      <div key={equipment.id} className="border rounded-lg p-4 bg-muted/30">
                        <div className="flex gap-4">
                          {/* Equipment Image */}
                          <div className="w-24 h-24 bg-muted rounded-lg overflow-hidden flex-shrink-0">
                            <img
                              src={equipment.image}
                              alt={equipment.name}
                              className="w-full h-full object-cover"
                            />
                          </div>

                          {/* Equipment Details */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between mb-2">
                              <div>
                                <h4 className="font-medium text-foreground">{equipment.name}</h4>
                                <p className="text-sm text-muted-foreground">{equipment.brand} • {equipment.model}</p>
                                <p className="text-sm text-foreground mt-1">{equipment.description}</p>
                              </div>
                              <div className="flex gap-1 flex-col items-end">
                                <div className="flex gap-1">
                                  <Badge variant="primary">{equipment.category}</Badge>
                                  <Badge variant="outline">{equipment.subcategory}</Badge>
                                </div>
                                <div className="flex gap-1">
                                  {equipment.status === 'active' && <Badge variant="success">Aktif</Badge>}
                                  {equipment.status === 'draft' && <Badge variant="warning">Taslak</Badge>}
                                  <Badge variant="secondary">{equipment.difficulty_level}</Badge>
                                </div>
                              </div>
                            </div>

                            {/* Price & Rating */}
                            <div className="flex items-center gap-4 mb-3">
                              <div className="text-sm">
                                <span className="text-muted-foreground">Fiyat: </span>
                                <span className="font-medium text-green-600">
                                  ₺{equipment.price_range.min} - ₺{equipment.price_range.max}
                                </span>
                              </div>
                              <div className="text-sm">
                                <span className="text-muted-foreground">Rating: </span>
                                <span className="font-semibold text-yellow-600">
                                  ⭐ {equipment.user_rating}/5 ({equipment.review_count} değerlendirme)
                                </span>
                              </div>
                            </div>

                            {/* Specifications */}
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-3 text-xs">
                              {Object.entries(equipment.specifications).slice(0, 4).map(([key, value]) => (
                                <div key={key} className="p-2 bg-muted/50 rounded">
                                  <span className="text-muted-foreground capitalize">
                                    {key.replace('_', ' ')}:
                                  </span>
                                  <p className="font-medium">{value}</p>
                                </div>
                              ))}
                            </div>

                            {/* Suitable Fish & Fishing Type */}
                            <div className="flex flex-wrap gap-2 mb-2">
                              <span className="text-xs text-muted-foreground">Uygun Balıklar:</span>
                              {equipment.suitable_for.map((fish) => (
                                <Badge key={fish} variant="outline" className="text-xs">🐟 {fish}</Badge>
                              ))}
                            </div>
                            <div className="flex flex-wrap gap-2 mb-2">
                              <span className="text-xs text-muted-foreground">Balıkçılık Türü:</span>
                              {equipment.fishing_type.map((type) => (
                                <Badge key={type} variant="outline" className="text-xs">🎣 {type}</Badge>
                              ))}
                            </div>

                            {/* Pros & Cons */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
                              <div>
                                <span className="text-green-600 font-medium">Artıları:</span>
                                <ul className="text-muted-foreground ml-2">
                                  {equipment.pros.map((pro, index) => (
                                    <li key={index}>• {pro}</li>
                                  ))}
                                </ul>
                              </div>
                              <div>
                                <span className="text-red-600 font-medium">Eksileri:</span>
                                <ul className="text-muted-foreground ml-2">
                                  {equipment.cons.map((con, index) => (
                                    <li key={index}>• {con}</li>
                                  ))}
                                </ul>
                              </div>
                            </div>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex flex-col gap-2">
                            <Button variant="outline" size="sm">
                              <Icon name="eye" size="sm" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Icon name="edit" size="sm" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Icon name="copy" size="sm" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Icon name="star" size="sm" />
                            </Button>
                            <Button variant="destructive" size="sm">
                              <Icon name="trash" size="sm" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>
              </div>
            )}

            {activeTab === 'users' && (
              <div className="space-y-6">
                <Card className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-semibold flex items-center gap-2">
                      <Icon name="profile" size="sm" className="text-primary" />
                      Kullanıcı Yönetimi
                    </h3>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Icon name="search" size="sm" className="mr-2" />
                        Kullanıcı Ara
                      </Button>
                      <Button variant="primary" size="sm">
                        <Icon name="plus" size="sm" className="mr-2" />
                        Yeni Kullanıcı
                      </Button>
                    </div>
                  </div>

                  {/* Kullanıcı Listesi */}
                  <div className="space-y-3">
                    {users.map((user) => (
                      <div key={user.id} className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
                        <div className="flex items-center gap-4">
                          <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center text-primary font-semibold">
                            {user.name.split(' ').map(n => n[0]).join('')}
                          </div>
                          <div>
                            <div className="flex items-center gap-2">
                              <p className="font-medium text-foreground">{user.name}</p>
                              {user.isPro && <Badge variant="pro">PRO</Badge>}
                              <Badge variant={user.status === 'active' ? 'success' : 'destructive'}>
                                {user.status === 'active' ? 'Aktif' : 'Yasaklı'}
                              </Badge>
                            </div>
                            <p className="text-sm text-muted-foreground">{user.email}</p>
                            <p className="text-xs text-muted-foreground">Katılım: {user.joinDate}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm">
                            <Icon name="edit" size="sm" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Icon name="star" size="sm" />
                          </Button>
                          <Button variant="destructive" size="sm">
                            <Icon name="ban" size="sm" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>
              </div>
            )}

            {activeTab === 'posts' && (
              <div className="space-y-6">
                <Card className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-semibold flex items-center gap-2">
                      <Icon name="fish" size="sm" className="text-primary" />
                      Post Yönetimi
                    </h3>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Icon name="search" size="sm" className="mr-2" />
                        Post Ara
                      </Button>
                      <Button variant="destructive" size="sm">
                        <Icon name="trash" size="sm" className="mr-2" />
                        Toplu Sil
                      </Button>
                    </div>
                  </div>

                  {/* Post Listesi */}
                  <div className="space-y-3">
                    {posts.map((post) => (
                      <div key={post.id} className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <p className="font-medium text-foreground">{post.user}</p>
                            {post.reports > 0 && (
                              <Badge variant="destructive">{post.reports} Şikayet</Badge>
                            )}
                          </div>
                          <p className="text-sm text-foreground mb-2">{post.content}</p>
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <span>{post.date}</span>
                            <span>{post.likes} beğeni</span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm">
                            <Icon name="eye" size="sm" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Icon name="edit" size="sm" />
                          </Button>
                          <Button variant="destructive" size="sm">
                            <Icon name="trash" size="sm" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>
              </div>
            )}

            {activeTab === 'analytics' && (
              <div className="space-y-6">
                <Card className="p-6">
                  <h3 className="font-semibold mb-4 flex items-center gap-2">
                    <Icon name="dashboard" size="sm" className="text-primary" />
                    Mobile App Analytics
                  </h3>

                  {/* Mobile App Stats */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div className="p-4 bg-muted/30 rounded-lg text-center">
                      <div className="text-2xl font-bold text-blue-500 mb-1">1,247</div>
                      <div className="text-sm text-foreground">Toplam İndirme</div>
                      <div className="text-xs text-green-500">+23 bugün</div>
                    </div>
                    <div className="p-4 bg-muted/30 rounded-lg text-center">
                      <div className="text-2xl font-bold text-green-500 mb-1">892</div>
                      <div className="text-sm text-foreground">Aktif Kullanıcı</div>
                      <div className="text-xs text-green-500">+15 bugün</div>
                    </div>
                    <div className="p-4 bg-muted/30 rounded-lg text-center">
                      <div className="text-2xl font-bold text-orange-500 mb-1">3,456</div>
                      <div className="text-sm text-foreground">Toplam Post</div>
                      <div className="text-xs text-green-500">+67 bugün</div>
                    </div>
                    <div className="p-4 bg-muted/30 rounded-lg text-center">
                      <div className="text-2xl font-bold text-purple-500 mb-1">234</div>
                      <div className="text-sm text-foreground">Yeni Spot</div>
                      <div className="text-xs text-green-500">+8 bugün</div>
                    </div>
                  </div>

                  {/* Feature Usage */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium text-foreground mb-3">Özellik Kullanımı</h4>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                          <span className="text-sm text-foreground">📸 Fotoğraf Çekme</span>
                          <span className="font-semibold text-blue-500">89%</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                          <span className="text-sm text-foreground">🗺️ Harita Kullanımı</span>
                          <span className="font-semibold text-green-500">76%</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                          <span className="text-sm text-foreground">📍 Spot Ekleme</span>
                          <span className="font-semibold text-orange-500">45%</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                          <span className="text-sm text-foreground">💬 Mesajlaşma</span>
                          <span className="font-semibold text-purple-500">32%</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium text-foreground mb-3">App Performance</h4>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                          <span className="text-sm text-foreground">Ortalama Session</span>
                          <span className="font-semibold text-blue-500">12.5 dk</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                          <span className="text-sm text-foreground">App Açılış Süresi</span>
                          <span className="font-semibold text-green-500">2.1 sn</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                          <span className="text-sm text-foreground">Crash Rate</span>
                          <span className="font-semibold text-red-500">0.2%</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                          <span className="text-sm text-foreground">Retention (7 gün)</span>
                          <span className="font-semibold text-purple-500">68%</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>

                {/* Top Content */}
                <Card className="p-6">
                  <h3 className="font-semibold mb-4">En Popüler İçerikler</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium text-foreground mb-3">En Çok Beğenilen Postlar</h4>
                      <div className="space-y-2">
                        {posts.slice(0, 3).map((post, index) => (
                          <div key={post.id} className="flex items-center gap-3 p-2 bg-muted/30 rounded-lg">
                            <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center text-primary text-xs font-semibold">
                              {index + 1}
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm text-foreground truncate">{post.content}</p>
                              <p className="text-xs text-muted-foreground">{post.likes} beğeni</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium text-foreground mb-3">En Popüler Spotlar</h4>
                      <div className="space-y-2">
                        {areas.slice(0, 3).map((area, index) => (
                          <div key={area.id} className="flex items-center gap-3 p-2 bg-muted/30 rounded-lg">
                            <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center text-primary text-xs font-semibold">
                              {index + 1}
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm text-foreground truncate">{area.name}</p>
                              <p className="text-xs text-muted-foreground">{area.catches_count} yakalama</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            )}

            {activeTab === 'reports' && (
              <div className="space-y-6">
                <Card className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-semibold flex items-center gap-2">
                      <Icon name="warning" size="sm" className="text-primary" />
                      Şikayet Yönetimi
                    </h3>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Icon name="search" size="sm" className="mr-2" />
                        Şikayet Ara
                      </Button>
                      <Button variant="success" size="sm">
                        <Icon name="check" size="sm" className="mr-2" />
                        Toplu Çöz
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-4">
                    {posts.map((post) => (
                      <div key={post.id} className="border rounded-lg p-4 bg-muted/30">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h4 className="font-medium text-foreground">{post.type}</h4>
                            <p className="text-sm text-muted-foreground">{post.target}</p>
                          </div>
                          <Badge variant={post.status === 'pending' ? 'warning' : post.status === 'resolved' ? 'success' : 'info'}>
                            {post.status === 'pending' ? 'Bekliyor' : post.status === 'resolved' ? 'Çözüldü' : 'İnceleniyor'}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Şikayet Eden: {post.reporter}</span>
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm">
                              <Icon name="eye" size="sm" />
                            </Button>
                            <Button variant="success" size="sm">
                              <Icon name="check" size="sm" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>
              </div>
            )}

            {activeTab === 'settings' && (
              <div className="space-y-6">
                <Card className="p-6">
                  <h3 className="font-semibold mb-4 flex items-center gap-2">
                    <Icon name="settings" size="sm" className="text-primary" />
                    Sistem Ayarları
                  </h3>

                  <div className="space-y-6">
                    <div>
                      <h4 className="font-medium text-foreground mb-3">Genel Ayarlar</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                          <div>
                            <p className="font-medium text-foreground">Yeni Kullanıcı Kayıtları</p>
                            <p className="text-sm text-muted-foreground">Yeni kullanıcıların kayıt olmasına izin ver</p>
                          </div>
                          <Button variant="success" size="sm">Açık</Button>
                        </div>
                        <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                          <div>
                            <p className="font-medium text-foreground">Post Moderasyonu</p>
                            <p className="text-sm text-muted-foreground">Yeni postları otomatik olarak onayla</p>
                          </div>
                          <Button variant="warning" size="sm">Manuel</Button>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium text-foreground mb-3">Hızlı Eylemler</h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Button variant="outline" className="h-16 flex-col">
                          <Icon name="download" size="md" className="mb-2" />
                          <span>Veritabanı Yedekle</span>
                        </Button>
                        <Button variant="outline" className="h-16 flex-col">
                          <Icon name="trash" size="md" className="mb-2" />
                          <span>Cache Temizle</span>
                        </Button>
                        <Button variant="outline" className="h-16 flex-col">
                          <Icon name="settings" size="md" className="mb-2" />
                          <span>Sistem Yeniden Başlat</span>
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            )}
          </div>

          {/* Admin Sidebar */}
          <div className="space-y-6">
            {/* System Status */}
            <Card className="p-6">
              <h3 className="font-semibold mb-4 flex items-center gap-2">
                <Icon name="dashboard" size="sm" className="text-primary" />
                Sistem Durumu
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-foreground">Server</span>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-xs text-green-600">Online</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-foreground">Database</span>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-xs text-green-600">Connected</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-foreground">Storage</span>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <span className="text-xs text-yellow-600">85% Full</span>
                  </div>
                </div>
              </div>
            </Card>

            {/* Quick Admin Actions */}
            <Card className="p-6">
              <h3 className="font-semibold mb-4">Admin Eylemler</h3>
              <div className="space-y-2">
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Icon name="plus" size="sm" className="mr-2" />
                  Yeni Kullanıcı Ekle
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Icon name="warning" size="sm" className="mr-2" />
                  Şikayetleri İncele
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Icon name="settings" size="sm" className="mr-2" />
                  Sistem Ayarları
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Icon name="download" size="sm" className="mr-2" />
                  Rapor İndir
                </Button>
              </div>
            </Card>

            {/* Recent Admin Activity */}
            <Card className="p-6">
              <h3 className="font-semibold mb-4 flex items-center gap-2">
                <Icon name="clock" size="sm" className="text-primary" />
                Son Admin İşlemleri
              </h3>
              <div className="space-y-3">
                <div className="text-xs text-muted-foreground">
                  <p className="font-medium">Kullanıcı yasaklandı</p>
                  <p>spam_user123 • 2 saat önce</p>
                </div>
                <div className="text-xs text-muted-foreground">
                  <p className="font-medium">Post silindi</p>
                  <p>Uygunsuz içerik • 4 saat önce</p>
                </div>
                <div className="text-xs text-muted-foreground">
                  <p className="font-medium">Pro üyelik verildi</p>
                  <p>ahmet_balikci • 1 gün önce</p>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
}
