'use client';

import { useState, useEffect } from 'react';

export default function SimpleThemeToggle() {
  const [isDark, setIsDark] = useState(true);

  useEffect(() => {
    // Check localStorage for saved theme
    const savedTheme = localStorage.getItem('fishivo-theme');
    if (savedTheme) {
      const darkMode = savedTheme === 'dark';
      setIsDark(darkMode);
      document.documentElement.setAttribute('data-theme', savedTheme);
    } else {
      // Default to dark mode
      document.documentElement.setAttribute('data-theme', 'dark');
    }
  }, []);

  useEffect(() => {
    // Save to localStorage
    localStorage.setItem('fishivo-theme', isDark ? 'dark' : 'light');
    document.documentElement.setAttribute('data-theme', isDark ? 'dark' : 'light');
  }, [isDark]);

  const toggleTheme = () => {
    setIsDark(!isDark);
  };

  return (
    <button
      onClick={toggleTheme}
      className="p-3 rounded-lg transition-all duration-200 hover:scale-105"
      style={{
        background: 'var(--bg-tertiary)',
        border: '2px solid var(--border-light)',
      }}
      aria-label="Toggle theme"
    >
      {isDark ? (
        // Sun icon for light mode
        <svg 
          className="w-5 h-5" 
          fill="var(--text-primary)" 
          viewBox="0 0 20 20"
          style={{ filter: 'drop-shadow(0 0 4px rgba(255, 193, 7, 0.3))' }}
        >
          <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
        </svg>
      ) : (
        // Moon icon for dark mode
        <svg 
          className="w-5 h-5" 
          fill="var(--text-primary)" 
          viewBox="0 0 20 20"
          style={{ filter: 'drop-shadow(0 0 4px rgba(96, 165, 250, 0.3))' }}
        >
          <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
        </svg>
      )}
    </button>
  );
} 