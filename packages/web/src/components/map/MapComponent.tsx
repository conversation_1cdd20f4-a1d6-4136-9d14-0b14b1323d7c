'use client';

import React, { useEffect, useRef } from 'react';
import mapboxgl from 'mapbox-gl';
import MapboxGeocoder from '@mapbox/mapbox-gl-geocoder';
import 'mapbox-gl/dist/mapbox-gl.css';
import '@mapbox/mapbox-gl-geocoder/dist/mapbox-gl-geocoder.css';

interface MapComponentProps {
  onLocationClick?: (lat: number, lng: number) => void;
  mapStyle?: string;
  onZoomIn?: () => void;
  onZoomOut?: () => void;
  onLocate?: () => void;
  onResetNorth?: () => void;
  onMapReady?: (map: any) => void;
}

const MapComponent = ({
  onLocationClick,
  mapStyle = 'streets-v12',
  onZoomIn,
  onZoomOut,
  onLocate,
  onResetNorth,
  onMapReady
}: MapComponentProps) => {
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const mapRef = useRef<mapboxgl.Map | null>(null);
  const currentMarkerRef = useRef<mapboxgl.Marker | null>(null);
  const userLocationMarkerRef = useRef<mapboxgl.Marker | null>(null);

  useEffect(() => {
    console.log('🗺️ MapComponent başlatılıyor...');
    
    // MAPBOX DOKÜMANTASYONU: Access token ayarla
    mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_TOKEN || 'pk.eyJ1IjoiZmlzaGl2byIsImEiOiJjbWJsaWphZWwwbjdpMmtxeTMwaGU5Zm4yIn0.LUiv6j3SGgFjTAJfpuuwDA';

    // MAPBOX DOKÜMANTASYONU: Map oluştur
    mapRef.current = new mapboxgl.Map({
      container: mapContainerRef.current!, // container ID
      center: [29.0100, 41.0082], // İstanbul koordinatları [lng, lat]
      zoom: 10, // başlangıç zoom
      style: `mapbox://styles/mapbox/${mapStyle}` // dinamik style
    });

    console.log('✅ Mapbox harita oluşturuldu');

    // MAPBOX DOKÜMANTASYONU: Geocoder ekleme (Orijinal stil)
    mapRef.current.addControl(
      new MapboxGeocoder({
        accessToken: mapboxgl.accessToken,
        mapboxgl: mapboxgl
      }),
      'top-left'
    );

    console.log('✅ Mapbox geocoder eklendi');

    // MAPBOX DOKÜMANTASYONU: GeolocateControl ekleme
    mapRef.current.addControl(
      new mapboxgl.GeolocateControl({
        positionOptions: {
          enableHighAccuracy: true
        },
        trackUserLocation: true,
        showUserHeading: true
      }),
      'bottom-right'
    );

    console.log('✅ Mapbox GeolocateControl eklendi');

    // MAPBOX DOKÜMANTASYONU: NavigationControl ekleme (Zoom + Compass)
    mapRef.current.addControl(new mapboxgl.NavigationControl(), 'bottom-right');

    console.log('✅ Mapbox NavigationControl eklendi');

    // Map referansını parent'a gönder
    if (onMapReady) {
      onMapReady(mapRef.current);
    }

    // Mapbox orijinal kontrolleri eklendi

    console.log('✅ Mapbox kontrolleri eklendi');

    // MAPBOX DOKÜMANTASYONU: Haritaya tıklayınca marker ekleme
    mapRef.current.on('click', (e) => {
      const { lng, lat } = e.lngLat;

      console.log('📍 Haritaya tıklandı:', { lat, lng });

      // Önceki marker'ı kaldır
      if (currentMarkerRef.current) {
        currentMarkerRef.current.remove();
      }

      // MAPBOX DOKÜMANTASYONU: Yeni marker oluştur
      currentMarkerRef.current = new mapboxgl.Marker({
        color: '#3B82F6', // Website'in primary rengi
        scale: 0.8 // Küçültüldü
      })
        .setLngLat([lng, lat])
        .addTo(mapRef.current!);

      // Callback'i çağır
      if (onLocationClick) {
        onLocationClick(lat, lng);
      }
    });

    // Cleanup function
    return () => {
      if (currentMarkerRef.current) {
        currentMarkerRef.current.remove();
      }
      if (userLocationMarkerRef.current) {
        userLocationMarkerRef.current.remove();
      }
      if (mapRef.current) {
        mapRef.current.remove();
        console.log('🧹 Mapbox harita temizlendi');
      }
    };
  }, []);

  // MAPBOX DOKÜMANTASYONU: Style değiştirme
  useEffect(() => {
    if (!mapRef.current) return;

    console.log('🎨 Style değiştiriliyor:', mapStyle);

    // MAPBOX DOKÜMANTASYONU: setStyle kullan
    mapRef.current.setStyle(`mapbox://styles/mapbox/${mapStyle}`);
  }, [mapStyle]);

  // MAPBOX DOKÜMANTASYONU: Kontrol fonksiyonları
  const handleZoomIn = () => {
    if (mapRef.current) {
      mapRef.current.zoomIn();
      if (onZoomIn) onZoomIn();
    }
  };

  const handleZoomOut = () => {
    if (mapRef.current) {
      mapRef.current.zoomOut();
      if (onZoomOut) onZoomOut();
    }
  };

  const handleLocate = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          if (mapRef.current) {
            // MAPBOX DOKÜMANTASYONU: Kullanıcı konumuna git
            mapRef.current.flyTo({
              center: [longitude, latitude],
              zoom: 15
            });

            // MAPBOX DOKÜMANTASYONU: Önceki kullanıcı marker'ını kaldır
            if (userLocationMarkerRef.current) {
              userLocationMarkerRef.current.remove();
            }

            // MAPBOX DOKÜMANTASYONU: Kullanıcı konumu marker'ı oluştur
            userLocationMarkerRef.current = new mapboxgl.Marker({
              color: '#1E90FF', // Mavi renk (kullanıcı konumu)
              scale: 1.0
            })
              .setLngLat([longitude, latitude])
              .addTo(mapRef.current);

            console.log('📍 Kullanıcı konumu bulundu:', { latitude, longitude });
          }
          if (onLocate) onLocate();
        },
        (error) => {
          console.error('❌ Konum alınamadı:', error);
          alert('Konum alınamadı. Lütfen konum izni verin.');
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        }
      );
    } else {
      alert('Tarayıcınız konum özelliğini desteklemiyor.');
    }
  };

  const handleResetNorth = () => {
    if (mapRef.current) {
      mapRef.current.resetNorth();
      if (onResetNorth) onResetNorth();
    }
  };

  return (
    <>
      <div
        style={{ height: '100%' }}
        ref={mapContainerRef}
        className="map-container w-full h-full"
      />
      {/* Minimal CSS - Border radius + Attribution küçültme */}
      <style jsx global>{`
        .mapboxgl-ctrl-geocoder {
          border-radius: 0.75rem !important;
        }

        .mapboxgl-ctrl-geocoder .suggestions {
          border-radius: 0.75rem !important;
        }

        /* Mapbox attribution küçültme */}
        .mapboxgl-ctrl-attrib {
          font-size: 10px !important;
          opacity: 0.6 !important;
          background: rgba(255, 255, 255, 0.8) !important;
          padding: 2px 6px !important;
          border-radius: 4px !important;
        }

        .mapboxgl-ctrl-attrib a {
          font-size: 10px !important;
          color: #666 !important;
        }
      `}</style>
    </>
  );
};

export default MapComponent;
