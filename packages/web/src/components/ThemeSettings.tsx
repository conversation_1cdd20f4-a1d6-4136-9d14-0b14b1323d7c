'use client';

import { useState, useEffect } from 'react';
import { Sun, Moon, Monitor } from 'lucide-react';

type ThemeMode = 'light' | 'dark' | 'system';

export default function ThemeSettings() {
  const [currentTheme, setCurrentTheme] = useState<ThemeMode>('system');
  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>('dark');

  useEffect(() => {
    const savedTheme = localStorage.getItem('fishivo-theme-mode') as ThemeMode;
    if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
      setCurrentTheme(savedTheme);
    }

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    setSystemTheme(mediaQuery.matches ? 'dark' : 'light');

    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? 'dark' : 'light');
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);
    return () => mediaQuery.removeEventListener('change', handleSystemThemeChange);
  }, []);

  useEffect(() => {
    const applyTheme = () => {
      let actualTheme: 'light' | 'dark';
      
      if (currentTheme === 'system') {
        actualTheme = systemTheme;
      } else {
        actualTheme = currentTheme;
      }

      document.documentElement.setAttribute('data-theme', actualTheme);
      localStorage.setItem('fishivo-theme', actualTheme);
    };

    applyTheme();
  }, [currentTheme, systemTheme]);

  const handleThemeChange = (themeMode: ThemeMode) => {
    setCurrentTheme(themeMode);
    localStorage.setItem('fishivo-theme-mode', themeMode);
  };

  const themes = [
    {
      id: 'light' as ThemeMode,
      icon: <Sun className="w-4 h-4" />,
      label: 'Açık'
    },
    {
      id: 'dark' as ThemeMode,
      icon: <Moon className="w-4 h-4" />,
      label: 'Koyu'
    },
    {
      id: 'system' as ThemeMode,
      icon: <Monitor className="w-4 h-4" />,
      label: 'Sistem'
    }
  ];

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <div>
          <h4 className="font-medium text-foreground text-sm">Tema</h4>
          <p className="text-xs text-muted-foreground">Görünüm tercihinizi seçin</p>
        </div>
      </div>
      
      <div className="flex bg-muted/30 rounded-lg p-1 w-fit">
        {themes.map((theme) => (
          <button
            key={theme.id}
            onClick={() => handleThemeChange(theme.id)}
            className={`
              flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200
              ${currentTheme === theme.id
                ? 'bg-background text-foreground shadow-sm' 
                : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
              }
            `}
          >
            {theme.icon}
            <span className="text-xs">{theme.label}</span>
          </button>
        ))}
      </div>
      
      {currentTheme === 'system' && (
        <p className="text-xs text-muted-foreground">
          Şu an: {systemTheme === 'dark' ? 'Koyu' : 'Açık'} tema kullanılıyor
        </p>
      )}
    </div>
  );
} 