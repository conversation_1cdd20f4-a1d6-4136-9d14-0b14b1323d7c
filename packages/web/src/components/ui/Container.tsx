import React from 'react';
import { cn } from '../../lib/utils';

interface ContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | 'full';
  centered?: boolean;
}

const Container = React.forwardRef<HTMLDivElement, ContainerProps>(
  ({ className, size = 'lg', centered = true, children, ...props }, ref) => {
    const sizes = {
      sm: 'max-w-4xl',
      md: 'max-w-6xl',
      lg: 'max-w-7xl',
      xl: 'max-w-[1400px]',
      '2xl': 'max-w-[1900px]',
      '3xl': 'max-w-[1800px]',
      full: 'max-w-full'
    };

    const centerClass = centered ? 'mx-auto' : '';

    return (
      <div
        className={cn(
          'px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16',
          sizes[size],
          centerClass,
          className
        )}
        ref={ref}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Container.displayName = "Container";

export { Container };
export type { ContainerProps }; 