import React from 'react';
import { cn } from '../../lib/utils';
import {
  Home,
  LayoutDashboard,
  Map,
  User,
  Plus,
  Search,
  Filter,
  Settings,
  X,
  Menu,
  Fish,
  Waves,
  MapPin,
  Cloud,
  Heart,
  MessageCircle,
  Share,
  Star,
  Check,
  AlertTriangle,
  Info,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  Satellite,
  Sun,
  Moon,
  Navigation,
  Anchor,
  Activity,
  Calendar,
  Clock,
  Target,
  Compass,
  Wind,
  Thermometer,
  Eye,
  Zap,
  TrendingUp,
  Trophy,
  Award,
  Camera,
  Download,
  Upload,
  Globe,
  Wifi,
  Volume2,
  Bell,
  Lock,
  Unlock,
  Edit,
  Trash2,
  MoreHorizontal,
  MoreVertical,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  ChevronDown,
  RefreshCw,
  Bookmark,
  Tag,
  Layers,
  Grid,
  List,
  Image,
  Video,
  Music,
  File,
  Folder,
  Database,
  Server,
  Shield,
  Key,
  Mail,
  Phone,
  Link,
  ExternalLink,
  Copy,
  Scissors,
  Save,
  FolderOpen,
  Maximize,
  Minimize,
  RotateCcw,
  RotateCw,
  ZoomIn,
  ZoomOut,
  Move,
  MousePointer,
  Type,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Bold,
  Italic,
  Underline,
  PaintBucket,
  Palette,
  Ruler,
  Square,
  Circle,
  Triangle,
  Hexagon
} from 'lucide-react';

interface IconProps extends React.SVGAttributes<SVGElement> {
  name: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  className?: string;
}

const iconSizes = {
  xs: 12,
  sm: 16,
  md: 20,
  lg: 24,
  xl: 32,
  '2xl': 40
};

const icons = {
  // Navigation & Layout
  home: Home,
  dashboard: LayoutDashboard,
  map: Map,
  profile: User,
  user: User,
  
  // Actions
  plus: Plus,
  search: Search,
  filter: Filter,
  settings: Settings,
  close: X,
  menu: Menu,
  edit: Edit,
  delete: Trash2,
  save: Save,
  copy: Copy,
  download: Download,
  upload: Upload,
  refresh: RefreshCw,
  
  // Fishing & Marine
  fish: Fish,
  water: Waves,
  waves: Waves,
  anchor: Anchor,
  compass: Compass,
  navigation: Navigation,
  
  // Location & Geography
  location: MapPin,
  pin: MapPin,
  satellite: Satellite,
  globe: Globe,
  layers: Layers,
  
  // Weather & Environment
  weather: Cloud,
  cloud: Cloud,
  sun: Sun,
  moon: Moon,
  wind: Wind,
  temperature: Thermometer,
  
  // Social & Communication
  heart: Heart,
  message: MessageCircle,
  share: Share,
  mail: Mail,
  phone: Phone,
  
  // Status & Feedback
  star: Star,
  check: Check,
  warning: AlertTriangle,
  info: Info,
  bell: Bell,
  
  // Navigation Arrows
  arrowLeft: ArrowLeft,
  arrowRight: ArrowRight,
  arrowUp: ArrowUp,
  arrowDown: ArrowDown,
  chevronLeft: ChevronLeft,
  chevronRight: ChevronRight,
  chevronUp: ChevronUp,
  chevronDown: ChevronDown,
  
  // Activity & Performance
  activity: Activity,
  trending: TrendingUp,
  target: Target,
  trophy: Trophy,
  award: Award,
  zap: Zap,
  
  // Time & Calendar
  calendar: Calendar,
  clock: Clock,
  
  // Media & Files
  camera: Camera,
  image: Image,
  video: Video,
  music: Music,
  file: File,
  folder: Folder,
  folderOpen: FolderOpen,
  
  // Technology & System
  database: Database,
  server: Server,
  wifi: Wifi,
  volume: Volume2,
  
  // Security
  shield: Shield,
  lock: Lock,
  unlock: Unlock,
  key: Key,
  
  // Interface Elements
  eye: Eye,
  link: Link,
  externalLink: ExternalLink,
  bookmark: Bookmark,
  tag: Tag,
  grid: Grid,
  list: List,
  more: MoreHorizontal,
  moreVertical: MoreVertical,
  
  // Controls
  maximize: Maximize,
  minimize: Minimize,
  rotate: RotateCw,
  rotateLeft: RotateCcw,
  zoomIn: ZoomIn,
  zoomOut: ZoomOut,
  move: Move,
  cursor: MousePointer,
  
  // Text & Formatting
  type: Type,
  alignLeft: AlignLeft,
  alignCenter: AlignCenter,
  alignRight: AlignRight,
  bold: Bold,
  italic: Italic,
  underline: Underline,
  
  // Design Tools
  paint: PaintBucket,
  palette: Palette,
  ruler: Ruler,
  
  // Shapes
  square: Square,
  circle: Circle,
  triangle: Triangle,
  hexagon: Hexagon
};

const Icon: React.FC<IconProps> = ({ 
  name, 
  size = 'md', 
  className,
  ...props 
}) => {
  const IconComponent = icons[name as keyof typeof icons];
  
  if (!IconComponent) {
    console.warn(`Icon "${name}" not found in Lucide React icons`);
    return null;
  }

  return (
    <IconComponent
      size={iconSizes[size]}
      className={cn('stroke-current', className)}
      {...props}
    />
  );
};

export { Icon };
export type { IconProps }; 