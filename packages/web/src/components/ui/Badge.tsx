import React from 'react';
import { cn } from '../../lib/utils';

interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  variant?: 'default' | 'pro' | 'verified' | 'success' | 'warning' | 'danger' | 'destructive' | 'info' | 'outline' | 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  dot?: boolean;
}

const Badge = React.forwardRef<HTMLSpanElement, BadgeProps>(
  ({ className, variant = 'default', size = 'md', dot = false, children, ...props }, ref) => {
    const baseStyles = "inline-flex items-center font-medium rounded-full transition-all duration-200";
    
    const variants = {
      default: "bg-muted text-muted-foreground border border-border",
      pro: "bg-gradient-to-r from-yellow-500 to-orange-500 text-black font-bold shadow-lg",
      verified: "bg-blue-600 text-white",
      success: "bg-green-600 text-white",
      warning: "bg-yellow-600 text-black",
      danger: "bg-red-600 text-white",
      destructive: "bg-red-600 text-white",
      info: "bg-blue-500 text-white",
      outline: "border border-border text-foreground bg-transparent",
      primary: "bg-blue-600 text-white",
      secondary: "bg-gray-600 text-white"
    };

    const sizes = {
      sm: "px-3 py-1 text-xs",
      md: "px-4 py-1.5 text-sm",
      lg: "px-5 py-2 text-base"
    };

    const dotStyles = dot ? "w-2 h-2 rounded-full" : "";

    return (
      <span
        className={cn(
          baseStyles,
          variants[variant],
          sizes[size],
          dotStyles,
          className
        )}
        ref={ref}
        {...props}
      >
        {children}
      </span>
    );
  }
);

Badge.displayName = "Badge";

export { Badge };
export type { BadgeProps }; 