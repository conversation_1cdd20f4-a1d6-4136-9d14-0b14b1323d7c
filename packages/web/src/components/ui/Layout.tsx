import React from 'react';
import { Container } from './Container';
import { cn } from '../../lib/utils';

interface LayoutProps {
  children: React.ReactNode;
  className?: string;
  containerSize?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | 'full';
  showContainer?: boolean;
}

const Layout: React.FC<LayoutProps> = ({
  children,
  className,
  containerSize = '2xl',
  showContainer = true
}) => {
  return (
    <main className={cn('min-h-screen bg-background text-foreground', className)}>
      {showContainer ? (
        <Container size={containerSize}>
          {children}
        </Container>
      ) : (
        children
      )}
    </main>
  );
};

export { Layout };
export type { LayoutProps }; 