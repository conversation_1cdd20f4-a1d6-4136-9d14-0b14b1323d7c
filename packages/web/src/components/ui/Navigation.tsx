'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Container } from './Container';
import { Button } from './Button';
import { Icon } from './Icon';
import { cn } from '../../lib/utils';

const Navigation: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();

  const navItems = [
    { href: '/', label: 'Anasayfa', icon: 'home' },
    { href: '/dashboard', label: 'Dashboard', icon: 'dashboard' },
    { href: '/map', label: '<PERSON><PERSON>', icon: 'map' },
    { href: '/profile', label: 'Profil', icon: 'profile' },
  ];

  const isActive = (path: string) => pathname === path;

  return (
    <nav className="sticky top-0 z-50 bg-background/80 backdrop-blur-md border-b border-border">
      <Container size="2xl">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <Icon name="fish" size="lg" className="text-primary" />
            <span className="text-xl font-bold">Fishivo</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center gap-2.5">
            {navItems.map((item) => (
              <Link key={item.href} href={item.href}>
                <Button
                  variant={isActive(item.href) ? "primary" : "ghost"}
                  size="sm"
                  className="flex items-center space-x-1"
                >
                  <Icon name={item.icon as any} size="sm" />
                  <span>{item.label}</span>
                </Button>
              </Link>
            ))}
          </div>

          {/* Mobile menu button */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <Icon name={isMobileMenuOpen ? "close" : "menu"} size="md" />
          </Button>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-border py-4">
            <div className="flex flex-col space-y-2">
              {navItems.map((item) => (
                <Link key={item.href} href={item.href}>
                  <Button
                    variant={isActive(item.href) ? "primary" : "ghost"}
                    size="sm"
                    className="w-full justify-start flex items-center space-x-1"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <Icon name={item.icon as any} size="sm" />
                    <span>{item.label}</span>
                  </Button>
                </Link>
              ))}
            </div>
          </div>
        )}
      </Container>
    </nav>
  );
};

export { Navigation }; 