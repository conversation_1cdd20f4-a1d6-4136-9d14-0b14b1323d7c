'use client';

import Link from 'next/link';
import { Fish, Home, BarChart3, Map, Users, MapPin, Star, User, Plus } from 'lucide-react';

export default function Header() {
  return (
    <header className="fishivo-header">
      <div className="container flex items-center justify-between py-4">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-3 hover:opacity-80 transition-opacity">
          <Fish className="w-7 h-7 text-blue-600" />
          <h1 className="text-heading font-bold text-xl">Fishivo</h1>
        </Link>

        {/* Navigation */}
        <nav className="hidden md:flex items-center space-x-2">
          <Link href="/" className="fishivo-nav-link flex items-center gap-2">
            <Home className="w-4 h-4 text-muted-foreground" />
            Ana Sayfa
          </Link>
          <Link href="/dashboard" className="fishivo-nav-link flex items-center gap-2">
            <BarChart3 className="w-4 h-4 text-muted-foreground" />
            Kontrol Paneli
          </Link>
          <Link href="/map" className="fishivo-nav-link flex items-center gap-2">
            <Map className="w-4 h-4 text-muted-foreground" />
            Harita
          </Link>
          <Link href="/species" className="fishivo-nav-link flex items-center gap-2">
            <Fish className="w-4 h-4 text-muted-foreground" />
            Türler
          </Link>
          <Link href="/spots" className="fishivo-nav-link flex items-center gap-2">
            <MapPin className="w-4 h-4 text-muted-foreground" />
            Spotlar
          </Link>
        </nav>

        {/* Actions */}
        <div className="flex items-center space-x-3">
          <Link href="/pro" className="bg-yellow-500 text-black px-3 py-2 rounded-lg font-bold hover:bg-yellow-400 transition-colors text-sm flex items-center gap-2">
            <Star className="w-4 h-4 text-black" />
            Pro
          </Link>
          <Link href="/add-catch" className="fishivo-button flex items-center gap-2">
            <Plus className="w-4 h-4 text-white" />
            Balık Ekle
          </Link>
          <Link href="/profile" className="fishivo-button-secondary hidden sm:inline-flex items-center gap-2">
            <User className="w-4 h-4 text-muted-foreground" />
            Profil
          </Link>
        </div>

        {/* Mobile Menu Button */}
        <button 
          className="md:hidden p-2 text-muted hover:text-primary transition-colors rounded"
          aria-label="Menu"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </div>
    </header>
  );
} 