import React from 'react';
import Link from 'next/link';
import { Fish, MapPin, Map, Heart, MessageCircle, Share } from 'lucide-react';

interface FishPost {
  id: number;
  user: {
    name: string;
    username: string;
    avatar: string;
  };
  fish: {
    species: string;
    weight: string;
    length: string;
    image?: string;
  };
  location: {
    name: string;
    coordinates: { lat: number; lng: number };
  };
  caption: string;
  timestamp: string;
  likes: number;
  comments: number;
  weather?: string;
}

interface FishPostCardProps {
  post: FishPost;
}

export default function FishPostCard({ post }: FishPostCardProps) {
  return (
    <div className="fishivo-card">
      {/* User Header */}
      <div className="flex items-center space-x-3 mb-6">
        <div 
          className="rounded-full flex items-center justify-center text-white font-semibold"
          style={{
            width: '48px',
            height: '48px',
            background: 'linear-gradient(135deg, #121212 0%, #333333 100%)',
            fontSize: '16px'
          }}
        >
          {post.user.avatar}
        </div>
        <div className="flex-1">
          <h3 className="text-heading font-semibold">{post.user.name}</h3>
          <p className="text-muted text-sm">{post.user.username}</p>
        </div>
        <div className="text-muted text-sm">
          {post.timestamp}
        </div>
      </div>

      {/* Fish Image */}
      <div 
        className="mb-6 rounded-xl flex items-center justify-center"
        style={{
          height: '280px',
          background: 'var(--bg-tertiary)',
          border: '2px dashed var(--border-medium)'
        }}
      >
        {post.fish.image ? (
          <img 
            src={post.fish.image} 
            alt={`${post.fish.species} yakalama`}
            className="w-full h-full object-cover rounded-xl"
          />
        ) : (
          <div className="text-center">
            <Fish className="w-16 h-16 text-muted-foreground mx-auto mb-3" />
            <span className="text-muted">Balık Fotoğrafı</span>
          </div>
        )}
      </div>

      {/* Fish Details */}
      <div 
        className="mb-6 p-6 rounded-xl"
        style={{ background: 'var(--bg-secondary)' }}
      >
        <div className="grid grid-cols-3 gap-6 text-center">
          <div>
            <p className="text-muted text-sm font-medium mb-1">Tür</p>
            <p className="text-heading font-semibold text-lg">{post.fish.species}</p>
          </div>
          <div>
            <p className="text-muted text-sm font-medium mb-1">Ağırlık</p>
            <p className="text-heading font-semibold text-lg">{post.fish.weight}</p>
          </div>
          <div>
            <p className="text-muted text-sm font-medium mb-1">Uzunluk</p>
            <p className="text-heading font-semibold text-lg">{post.fish.length}</p>
          </div>
        </div>
      </div>

      {/* Location & Weather */}
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <MapPin className="w-4 h-4 text-muted-foreground" />
          <span className="text-body font-medium">{post.location.name}</span>
        </div>
        {post.weather && (
          <div className="text-muted text-sm">
            {post.weather}
          </div>
        )}
      </div>

      {/* Caption */}
      <p className="text-body mb-6 leading-relaxed">{post.caption}</p>

      {/* Actions */}
      <div 
        className="flex items-center justify-between pt-6"
        style={{ borderTop: '1px solid var(--border-light)' }}
      >
        <div className="flex items-center space-x-6">
          <button className="flex items-center space-x-2 text-muted-foreground hover:text-red-500 transition-colors p-2 rounded">
            <Heart className="w-5 h-5 text-muted-foreground" />
            <span className="font-medium">{post.likes}</span>
          </button>
          <button className="flex items-center space-x-2 text-muted-foreground hover:text-blue-500 transition-colors p-2 rounded">
            <MessageCircle className="w-5 h-5 text-muted-foreground" />
            <span className="font-medium">{post.comments}</span>
          </button>
          <button className="text-muted-foreground hover:text-green-500 transition-colors p-2 rounded">
            <Share className="w-5 h-5 text-muted-foreground" />
          </button>
        </div>
        <Link
          href={`/map?lat=${post.location.coordinates.lat}&lng=${post.location.coordinates.lng}`}
          className="fishivo-button-secondary text-sm px-4 py-2 flex items-center gap-2"
        >
          <Map className="w-4 h-4 text-muted-foreground" />
          Haritada Gör
        </Link>
      </div>
    </div>
  );
} 