import { API_CONFIG } from '../../../shared/config';
import { ApiResponse, PaginatedResponse, Post, User, CreatePostRequest, UpdatePostRequest, LoginRequest, RegisterRequest, AuthResponse } from '../../../shared/types';

class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor() {
    this.baseURL = API_CONFIG.baseURL;
    
    // Get token from localStorage if available
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('fishivo_token');
    }
  }

  setToken(token: string) {
    this.token = token;
    if (typeof window !== 'undefined') {
      localStorage.setItem('fishivo_token', token);
    }
  }

  clearToken() {
    this.token = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('fishivo_token');
    }
  }

  private async request<T = any>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Auth endpoints
  async login(credentials: LoginRequest): Promise<ApiResponse<AuthResponse>> {
    const response = await this.request<AuthResponse>('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });

    if (response.success && response.data?.session.access_token) {
      this.setToken(response.data.session.access_token);
    }

    return response;
  }

  async register(userData: RegisterRequest): Promise<ApiResponse<AuthResponse>> {
    const response = await this.request<AuthResponse>('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });

    if (response.success && response.data?.session.access_token) {
      this.setToken(response.data.session.access_token);
    }

    return response;
  }

  async logout(): Promise<ApiResponse> {
    const response = await this.request('/api/auth/logout', {
      method: 'POST',
    });

    this.clearToken();
    return response;
  }

  // User endpoints
  async getCurrentUser(): Promise<ApiResponse<User>> {
    return this.request<User>('/api/users/me');
  }

  async updateProfile(updates: Partial<User>): Promise<ApiResponse<User>> {
    return this.request<User>('/api/users/me', {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async getUserById(userId: string): Promise<ApiResponse<Partial<User>>> {
    return this.request<Partial<User>>(`/api/users/${userId}`);
  }

  // Post endpoints
  async getPosts(page = 1, limit = 10, userId?: string): Promise<ApiResponse<PaginatedResponse<Post>>> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (userId) {
      params.append('user_id', userId);
    }

    return this.request<PaginatedResponse<Post>>(`/api/posts?${params}`);
  }

  async getPostById(postId: number): Promise<ApiResponse<Post>> {
    return this.request<Post>(`/api/posts/${postId}`);
  }

  async createPost(postData: CreatePostRequest): Promise<ApiResponse<Post>> {
    return this.request<Post>('/api/posts', {
      method: 'POST',
      body: JSON.stringify(postData),
    });
  }

  async updatePost(postId: number, updates: UpdatePostRequest): Promise<ApiResponse<Post>> {
    return this.request<Post>(`/api/posts/${postId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async deletePost(postId: number): Promise<ApiResponse> {
    return this.request(`/api/posts/${postId}`, {
      method: 'DELETE',
    });
  }

  // Species endpoints
  async getSpecies(): Promise<ApiResponse<any[]>> {
    return this.request('/api/species');
  }

  async getSpeciesById(speciesId: number): Promise<ApiResponse<any>> {
    return this.request(`/api/species/${speciesId}`);
  }

  // Upload endpoints
  async uploadImage(file: File): Promise<ApiResponse<{ url: string; filename: string; size: number }>> {
    const formData = new FormData();
    formData.append('image', file);

    const headers: HeadersInit = {};
    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(`${this.baseURL}/api/upload/image`, {
        method: 'POST',
        headers,
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('Upload failed:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const apiClient = new ApiClient();
