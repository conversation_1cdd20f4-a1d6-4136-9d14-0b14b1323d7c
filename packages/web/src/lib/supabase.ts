import { createClient } from '@supabase/supabase-js';

// Environment validation for client-side
function getRequiredEnvVar(name: string): string {
  const value = process.env[name];
  if (!value) {
    console.error(`❌ Required environment variable ${name} is not set`);
    throw new Error(`Required environment variable ${name} is not set`);
  }
  return value;
}

// Secure Supabase configuration for web client
const supabaseUrl = getRequiredEnvVar('NEXT_PUBLIC_SUPABASE_URL');
const supabaseAnonKey = getRequiredEnvVar('NEXT_PUBLIC_SUPABASE_ANON_KEY');

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce', // Enhanced security for OAuth flows
  },
  global: {
    headers: {
      'X-Client-Info': 'fishivo-web@1.0.0',
    },
  },
});
