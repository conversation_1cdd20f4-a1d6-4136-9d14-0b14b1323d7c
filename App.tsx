import React from 'react';
import { StatusBar, LogBox } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { SafeAreaProvider } from 'react-native-safe-area-context';

// ViewTagResolver hatasını geçici olarak gizle (patch uygulanana kadar)
LogBox.ignoreLogs([
  'Mapbox [error] ViewTagResolver | view: null found with tag',
  'ViewTagResolver',
  '`new NativeEventEmitter()` was called with a non-null argument without the required `addListener` method',
  '`new NativeEventEmitter()` was called with a non-null argument without the required `removeListeners` method'
]);

// Location Service
import { LocationProvider } from './src/services/LocationService';

// Auth Context
import { AuthProvider } from './src/contexts/AuthContext';

// Navigation
import { AppNavigator } from './src/navigation';



export default function App() {
  return (
    <SafeAreaProvider>
      <AuthProvider>
        <LocationProvider>
          <NavigationContainer>
            <AppNavigator />
          </NavigationContainer>
          <StatusBar barStyle="light-content" backgroundColor="#000" />
        </LocationProvider>
      </AuthProvider>
    </SafeAreaProvider>
  );
}


