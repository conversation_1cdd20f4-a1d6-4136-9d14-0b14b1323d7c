# Fishivo - Unit System Database Migration

Fishivo balıkçılık sosyal platformu için ölçü birimleri (Unit System) veritabanı migration scriptleri.

## 📋 Proje <PERSON>

**Fishivo**, Türkçe bir balıkçılık sosyal platformudur:
- **Platform**: React Native 0.79.3 mobil uygulaması (iOS & Android)
- **Backend**: Supabase PostgreSQL + Node.js API (**Fly.io deployment**)
- **Web**: Next.js web sitesi (**Fly.io deployment**)
- **Database**: Supabase PostgreSQL (cloud)
- **Unit System**: 7 farklı ölçü kategorisinde uluslararası birim desteği

## 🌐 **Deployment Architecture**

```
🏗️ FISHIVO ARCHITECTURE
┌─────────────────────────────────────────────────────────┐
│                     MOBIL                               │
│           📱 React Native 0.79.3                       │
│              iOS & Android                              │
└─────────────────────┬───────────────────────────────────┘
                      │
    ┌─────────────────┼─────────────────┐
    │                 │                 │
    ▼                 ▼                 ▼
┏━━━━━━━━━━━━━┓   ┏━━━━━━━━━━━┓   ┏━━━━━━━━━━━━━━━┓
┃   FLY.IO    ┃   ┃  FLY.IO   ┃   ┃   SUPABASE    ┃
┃             ┃   ┃           ┃   ┃               ┃
┃ 🚀 Backend  ┃   ┃ 🌐 Web    ┃   ┃ 🗄️ Database   ┃
┃ Node.js API ┃   ┃ Next.js   ┃   ┃ PostgreSQL    ┃
┃             ┃   ┃           ┃   ┃ + Auth + File ┃
┗━━━━━━━━━━━━━┛   ┗━━━━━━━━━━━┛   ┗━━━━━━━━━━━━━━━┛
```

## 🎯 Unit System Özellikleri

Fishivo'da desteklenen ölçü birimleri:

### 1. **Ağırlık (Weight)** - Balık ağırlığı
- `kg` - Kilogram (varsayılan TR)
- `g` - Gram  
- `lbs` - Pound (US/UK)
- `oz` - Ounce

### 2. **Uzunluk (Length)** - Balık boyu
- `cm` - Santimetre (varsayılan TR)
- `m` - Metre
- `inch` - İnç (US/UK)
- `ft` - Feet

### 3. **Mesafe (Distance)** - Lokasyon mesafeleri
- `km` - Kilometre (varsayılan TR)
- `m` - Metre
- `miles` - Mil (US/UK)
- `nm` - Deniz Mili

### 4. **Sıcaklık (Temperature)** - Su ve hava sıcaklığı
- `celsius` - Santigrat (varsayılan TR)
- `fahrenheit` - Fahrenheit (US)

### 5. **Derinlik (Depth)** - Su derinliği
- `meters` - Metre (varsayılan TR)
- `feet` - Feet (US/UK)
- `fathoms` - Kulaç (geleneksel)

### 6. **Hız (Speed)** - Rüzgar ve tekne hızı
- `kmh` - Kilometre/Saat (varsayılan TR)
- `mph` - Mil/Saat (US/UK)
- `knots` - Knot (denizcilik)

### 7. **Basınç (Pressure)** - Hava basıncı
- `hpa` - Hektopaskal (varsayılan TR)
- `inhg` - İnç Civa (US)
- `mbar` - Millibar
- `mmhg` - Milimetre Civa

## 🗄️ Database Schema

Unit system şu tablolardan oluşur:

- **`unit_categories`** - Ölçü kategorileri (ağırlık, uzunluk vb.)
- **`unit_definitions`** - Birim tanımları ve dönüşüm faktörleri
- **`user_unit_preferences`** - Kullanıcı tercih edilen birimleri
- **`unit_conversion_rules`** - Otomatik dönüşüm kuralları
- **`unit_validation_rules`** - Birim değer doğrulama kuralları
- **`regional_unit_defaults`** - Ülke bazlı varsayılan birimler
- **`unit_conversion_cache`** - Performans için dönüşüm cache'i
- **`unit_usage_analytics`** - Birim kullanım istatistikleri

## 📁 Migration Dosyaları

| Dosya | Tür | Açıklama |
|-------|-----|----------|
| `unit_system_complete.sql` | SQL | Tam schema + veri (691 satır) |
| `cleanup_tables.sql` | SQL | Mevcut tabloları temizle |
| `create_exec_sql_function.sql` | SQL | exec_sql fonksiyonu |
| `simple_migrate.js` | Node.js | Basit curl-based migration |
| `direct_migrate.js` | Node.js | REST API migration |
| `supabase_direct.js` | Node.js | Gelişmiş migration + hata yönetimi |

## 🚀 Migration Nasıl Çalıştırılır

### Seçenek 1: Supabase Dashboard (Önerilen)
```sql
-- 1. Supabase Dashboard > SQL Editor'da çalıştır:
-- cleanup_tables.sql (gerekirse)
-- unit_system_complete.sql
```

### Seçenek 2: Otomatik Script (Local Development)
```bash
# Ana dizindeki .env dosyasında tanımla:
export SUPABASE_URL="https://your-project.supabase.co"
export SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# Migration çalıştır:
cd scripts/database-migration
node supabase_direct.js
```

### Seçenek 3: Fly.io Production Environment
```bash
# Fly.io'da environment variables set et:
fly secrets set SUPABASE_URL="https://your-project.supabase.co"
fly secrets set SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# Fly.io backend container'ında migration çalıştır:
fly ssh console -a fishivo-backend
cd scripts/database-migration
node supabase_direct.js
```

## 🔧 Environment Variables

### Local Development (.env)
```bash
# Ana dizin .env dosyasına ekle:
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### Fly.io Production
```bash
# Backend ve Web için Fly.io secrets:
fly secrets set SUPABASE_URL="https://your-project.supabase.co" -a fishivo-backend
fly secrets set SUPABASE_SERVICE_ROLE_KEY="your-key" -a fishivo-backend
fly secrets set SUPABASE_URL="https://your-project.supabase.co" -a fishivo-web  
fly secrets set SUPABASE_ANON_KEY="your-anon-key" -a fishivo-web
```

## 📱 Mobil Uygulama Entegrasyonu

Unit system React Native uygulamada şu şekilde kullanılır:

### AsyncStorage Yönetimi
```typescript
// src/types/units.ts
export const UNITS_STORAGE_KEY = '@fishivo_user_units';
export const defaultUnits: UserUnits = {
  weight: 'kg',      // Türkiye varsayılanları
  length: 'cm',
  distance: 'km',
  temperature: 'celsius',
  depth: 'meters',
  speed: 'kmh',
  pressure: 'hpa'
};
```

### Settings Screen
- `src/screens/UnitsSettingsScreen.tsx` - Kullanıcı birim tercihleri
- Gerçek zamanlı değişiklik algılama
- Türkçe/İngilizce birim isimleri

### Data Files  
Projede kullanılan gerçek veri dosyaları:
- `src/data/unitDefinitions.json` - Birim tanımları (539 satır)
- `src/data/userUnitPreferences.json` - Kullanıcı tercihleri (303 satır)
- `src/data/unitConversionUtils.json` - Dönüşüm yardımcıları (374 satır)

## 🔍 Troubleshooting

### SSL Sertifika Sorunları
```bash
# supabase_direct.js SSL bypass özelliği kullanır:
rejectUnauthorized: false
```

### Network Bağlantı Sorunları (Fly.io)
```bash
# Fly.io'da debugging:
fly logs -a fishivo-backend
fly ssh console -a fishivo-backend

# DNS ayarları kontrol et:
nslookup your-project.supabase.co
```

### Authentication Hataları
- Service role key'in doğru olduğunu kontrol et
- Supabase dashboard'da key permissions kontrol et
- Fly.io secrets'ın doğru set olduğunu kontrol et

### Fly.io Specific Issues
```bash
# Fly.io status kontrol:
fly status -a fishivo-backend
fly status -a fishivo-web

# Secrets kontrol:
fly secrets list -a fishivo-backend

# Logs monitoring:
fly logs -a fishivo-backend --real-time
```

## 🌐 Production Deployment Flow

### 1. Database Migration (Önce)
```bash
# Local'den production Supabase'e migration:
node supabase_direct.js
```

### 2. Fly.io Backend Deploy
```bash
cd packages/backend
fly deploy -a fishivo-backend
```

### 3. Fly.io Web Deploy  
```bash
cd packages/web
fly deploy -a fishivo-web
```

### 4. Mobile App Build
```bash
# Android
npm run build:android

# iOS  
npm run build:ios
```

## 📊 Gerçek Kullanım İstatistikleri

Fishivo uygulamasında:
- **7 ölçü kategorisi** aktif kullanımda
- **26 farklı birim** desteği
- **Türkiye odaklı** varsayılan birimler (metric sistem)
- **ABD/UK** uyumluluğu (imperial sistem)
- **Denizcilik** birimleri (nautical mile, knots, fathoms)
- **Fly.io global edge** performans optimizasyonu

## ⚠️ Önemli Notlar

1. **Production Güvenliği**: Migration öncesi mutlaka database backup alın
2. **Test Ortamı**: İlk önce development environment'da test edin  
3. **Idempotent Scripts**: Scriptler güvenli şekilde tekrar çalıştırılabilir
4. **RLS Policies**: Row Level Security otomatik aktivasyon
5. **Cache System**: Performans için otomatik conversion cache
6. **Fly.io Monitoring**: Production'da log monitoring aktif tutun

## 🔗 İlgili Dosyalar

- [`/UNIT_SYSTEM_DESIGN.md`](../../UNIT_SYSTEM_DESIGN.md) - Detaylı sistem tasarımı
- [`/src/screens/UnitsSettingsScreen.tsx`](../../src/screens/UnitsSettingsScreen.tsx) - Unit ayarları ekranı
- [`/src/types/units.ts`](../../src/types/units.ts) - TypeScript type tanımları
- [`/packages/backend/`](../../packages/backend/) - Node.js API backend (Fly.io)
- [`/packages/web/`](../../packages/web/) - Next.js web sitesi (Fly.io)

---

**Fishivo** - Türkiye'nin balıkçılık sosyal platformu 🎣  
**Powered by**: React Native + Supabase + Fly.io 