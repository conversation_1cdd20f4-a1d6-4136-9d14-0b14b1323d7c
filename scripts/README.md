# Fishivo Scripts Directory

**Fishivo** balıkçılık sosyal platformu için utility scriptleri.

## 📋 Proje <PERSON>

**Fishivo** - React Native 0.79.3 tabanlı Türkçe balıkçılık sosyal platformu:
- **Mobil**: iOS & Android React Native uygulaması  
- **Backend**: Supabase PostgreSQL + Node.js API (**Fly.io deployment**)
- **Web**: Next.js destekleyici web sitesi (**Fly.io deployment**)
- **Database**: Supabase PostgreSQL ile comprehensive unit system

## 🌐 **Architecture Overview**

```
🏗️ FISHIVO - DEPLOYMENT ARCHITECTURE
┌─────────────────────────────────────────────────────────┐
│                📱 MOBILE APPS                           │
│         React Native 0.79.3 (iOS & Android)            │
└─────────────────────┬───────────────────────────────────┘
                      │ API Calls
      ┌───────────────┼───────────────┐
      │               │               │
      ▼               ▼               ▼
┏━━━━━━━━━━━━━┓   ┏━━━━━━━━━━━┓   ┏━━━━━━━━━━━━━━━┓
┃   FLY.IO    ┃   ┃  FLY.IO   ┃   ┃   SUPABASE    ┃
┃   GLOBAL    ┃   ┃   EDGE    ┃   ┃    CLOUD      ┃
┃             ┃   ┃           ┃   ┃               ┃
┃ 🚀 Backend  ┃   ┃ 🌐 Web    ┃   ┃ 🗄️ Database   ┃
┃ Node.js API ┃   ┃ Next.js   ┃   ┃ PostgreSQL    ┃
┃ + Sockets   ┃   ┃ Dashboard ┃   ┃ + Auth + File ┃
┃ + GraphQL   ┃   ┃ Landing   ┃   ┃ + Realtime    ┃
┗━━━━━━━━━━━━━┛   ┗━━━━━━━━━━━┛   ┗━━━━━━━━━━━━━━━┛
```

## 📁 Script Kategorileri

### 🗄️ **Database Migration** (`database-migration/`)
Supabase veritabanı kurulum ve migration scriptleri:

| Script | Tür | Açıklama |
|--------|-----|----------|
| `unit_system_complete.sql` | SQL | Tam unit system şeması (691 satır) |
| `cleanup_tables.sql` | SQL | Tablo temizleme |
| `supabase_direct.js` | Node.js | Gelişmiş migration tool |
| `simple_migrate.js` | Node.js | Basit migration |
| `direct_migrate.js` | Node.js | REST API migration |

📖 **Detaylar**: [database-migration/README.md](./database-migration/README.md)

### 📱 **React Native Development**
Mobil uygulama geliştirme scriptleri:

| Script | Tür | Açıklama |
|--------|-----|----------|
| `quick-run.sh` | Bash | Hızlı Android çalıştırma |
| `run-android.sh` | Bash | Android build ve run |
| `add-screen-container.js` | Node.js | Screen component oluşturucu |

## 🚀 Hızlı Kullanım

### Database Migration (Local Development)
```bash
cd scripts/database-migration
export SUPABASE_URL="https://your-project.supabase.co"
export SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"
node supabase_direct.js
```

### Production Migration (Fly.io)
```bash
# Fly.io'da secrets set et:
fly secrets set SUPABASE_URL="https://xxx.supabase.co" -a fishivo-backend
fly secrets set SUPABASE_SERVICE_ROLE_KEY="xxx" -a fishivo-backend

# Migration çalıştır:
fly ssh console -a fishivo-backend
cd scripts/database-migration && node supabase_direct.js
```

### Android Development
```bash
# Hızlı Android çalıştırma
./scripts/quick-run.sh

# Tam Android build ve run
./scripts/run-android.sh
```

### Screen Component Oluşturma
```bash
# Yeni screen component oluştur
node scripts/add-screen-container.js ScreenName
```

## 🔧 Environment Setup

### Local Development (.env - root)
```bash
# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
SUPABASE_ANON_KEY=your-anon-key

# React Native Configuration  
ANDROID_HOME=/path/to/android/sdk
JAVA_HOME=/path/to/java

# API Configuration
API_BASE_URL=https://your-api.com
```

### Fly.io Production Secrets
```bash
# Backend secrets:
fly secrets set SUPABASE_URL="https://xxx.supabase.co" -a fishivo-backend
fly secrets set SUPABASE_SERVICE_ROLE_KEY="xxx" -a fishivo-backend
fly secrets set NODE_ENV="production" -a fishivo-backend

# Web secrets:
fly secrets set NEXT_PUBLIC_SUPABASE_URL="https://xxx.supabase.co" -a fishivo-web
fly secrets set NEXT_PUBLIC_SUPABASE_ANON_KEY="xxx" -a fishivo-web
fly secrets set NODE_ENV="production" -a fishivo-web
```

## 📊 Fishivo Unit System Özellikleri

Scripts özellikle **Unit System** migration'ı için tasarlanmış:

### Desteklenen Ölçü Birimleri:
- **Ağırlık**: kg, g, lbs, oz (balık ağırlığı)
- **Uzunluk**: cm, m, inch, ft (balık boyu)  
- **Mesafe**: km, m, miles, nm (lokasyon)
- **Sıcaklık**: celsius, fahrenheit (su/hava)
- **Derinlik**: meters, feet, fathoms (su derinliği)
- **Hız**: kmh, mph, knots (rüzgar/tekne)
- **Basınç**: hpa, inhg, mbar, mmhg (hava basıncı)

### Database Schema:
- 8 ana tablo (unit_categories, unit_definitions, user_unit_preferences vb.)
- Regional defaults (Türkiye/ABD/UK)
- Conversion cache sistemi
- RLS (Row Level Security) politikaları

## 🌐 Deployment Pipeline

### 1. Database Setup (İlk)
```bash
# Local'den production Supabase'e migration:
cd scripts/database-migration
node supabase_direct.js
```

### 2. Fly.io Backend Deploy
```bash
cd packages/backend
fly deploy -a fishivo-backend
```

### 3. Fly.io Web Deploy
```bash
cd packages/web
fly deploy -a fishivo-web
```

### 4. Mobile Apps
```bash
# Android release build:
npm run build:android

# iOS release build:  
npm run build:ios
```

## ⚠️ Kullanım Kuralları

1. **README Oku**: Her klasördeki README'yi mutlaka oku
2. **Development Test**: İlk önce development environment'da test et
3. **Backup Al**: Migration öncesi database backup al
4. **Environment Check**: Environment variable'ların set olduğunu kontrol et
5. **Network Check**: Supabase bağlantısını doğrula

## 🐛 Troubleshooting

### SSL Certificate Issues
```bash
# supabase_direct.js SSL bypass kullanır
rejectUnauthorized: false
```

### Android Build Issues  
```bash
# React Native cache temizle
npx react-native-clean-project

# Android clean build
cd android && ./gradlew clean && cd ..
```

### Supabase Connection Issues
- URL ve Service Role Key'i kontrol et
- Firewall/DNS ayarlarını kontrol et
- Dashboard'da API ayarlarını kontrol et

### Fly.io Issues
```bash
# Status kontrol:
fly status -a fishivo-backend
fly status -a fishivo-web

# Logs monitoring:
fly logs -a fishivo-backend --real-time
fly logs -a fishivo-web --real-time

# SSH debugging:
fly ssh console -a fishivo-backend
fly ssh console -a fishivo-web
```

## 📈 Script Geliştirme Best Practices

- ✅ **Descriptive filenames** kullan
- ✅ **Error handling** ekle  
- ✅ **Idempotent** scripts yaz
- ✅ **Logging** ekle
- ✅ **Environment validation** yap
- ✅ **Clear documentation** yaz
- ✅ **Fly.io compatibility** sağla

## 🔗 İlgili Dosyalar

- [`/UNIT_SYSTEM_DESIGN.md`](../UNIT_SYSTEM_DESIGN.md) - Unit system tasarım dökümanı
- [`/src/screens/UnitsSettingsScreen.tsx`](../src/screens/UnitsSettingsScreen.tsx) - Unit ayarları UI
- [`/packages/backend/`](../packages/backend/) - Node.js API backend (Fly.io)
- [`/packages/web/`](../packages/web/) - Next.js web sitesi (Fly.io)

---

**Fishivo** - Türkiye'nin balıkçılık sosyal platformu 🎣  
**Powered by**: React Native + Supabase + Fly.io 