#!/bin/bash

echo "🚀 Fishivo Android Build & Run Script"
echo "======================================"

# Emülatör kontrolü
echo "📱 Checking emulator..."
if ! adb devices | grep -q "emulator-5554"; then
    echo "❌ Emulator not running. Starting emulator..."
    $ANDROID_HOME/emulator/emulator -avd Medium_Phone_API_36.0 -wipe-data &
    echo "⏳ Waiting for emulator to start..."
    sleep 30
fi

# Backend kontrolü
echo "🔧 Checking backend..."
if ! curl -s http://localhost:4000/health > /dev/null; then
    echo "❌ Backend not running. Please start backend first:"
    echo "   cd packages/backend && npm run dev"
    exit 1
fi

# Metro bundler kontrolü
echo "📦 Checking Metro bundler..."
if ! curl -s http://localhost:8090 > /dev/null; then
    echo "❌ Metro not running. Starting Metro..."
    npm start &
    echo "⏳ Waiting for Metro to start..."
    sleep 10
fi

# Clean build
echo "🧹 Cleaning previous build..."
cd android
./gradlew clean
cd ..

# Build and install
echo "🔨 Building and installing app..."
npx react-native run-android --device emulator-5554

# Launch app
echo "🚀 Launching Fishivo..."
sleep 3
adb shell am start -n com.fishivo/.MainActivity

echo "✅ Fishivo is now running on emulator!"
echo "📱 Backend: http://localhost:4000"
echo "📦 Metro: http://localhost:8090" 