diff --git a/node_modules/@rnmapbox/maps/android/.project b/node_modules/@rnmapbox/maps/android/.project
new file mode 100644
index 0000000..60e85ca
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/.project
@@ -0,0 +1,23 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<projectDescription>
+	<name>rnmapbox_maps</name>
+	<comment>Project rnmapbox_maps created by Buildship.</comment>
+	<projects>
+	</projects>
+	<buildSpec>
+		<buildCommand>
+			<name>org.eclipse.jdt.core.javabuilder</name>
+			<arguments>
+			</arguments>
+		</buildCommand>
+		<buildCommand>
+			<name>org.eclipse.buildship.core.gradleprojectbuilder</name>
+			<arguments>
+			</arguments>
+		</buildCommand>
+	</buildSpec>
+	<natures>
+		<nature>org.eclipse.jdt.core.javanature</nature>
+		<nature>org.eclipse.buildship.core.gradleprojectnature</nature>
+	</natures>
+</projectDescription>
diff --git a/node_modules/@rnmapbox/maps/android/build/generated/source/buildConfig/debug/com/rnmapbox/rnmbx/BuildConfig.java b/node_modules/@rnmapbox/maps/android/build/generated/source/buildConfig/debug/com/rnmapbox/rnmbx/BuildConfig.java
new file mode 100644
index 0000000..6be09da
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/build/generated/source/buildConfig/debug/com/rnmapbox/rnmbx/BuildConfig.java
@@ -0,0 +1,12 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package com.rnmapbox.rnmbx;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "com.rnmapbox.rnmbx";
+  public static final String BUILD_TYPE = "debug";
+  // Field from default config.
+  public static final boolean IS_NEW_ARCHITECTURE_ENABLED = false;
+}
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml b/node_modules/@rnmapbox/maps/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..c8865b5
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml
@@ -0,0 +1,11 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.rnmapbox.rnmbx" >
+
+    <uses-sdk android:minSdkVersion="24" />
+
+    <uses-permission android:name="android.permission.INTERNET" />
+    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
+    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json b/node_modules/@rnmapbox/maps/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json
new file mode 100644
index 0000000..545af46
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.rnmapbox.rnmbx",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties b/node_modules/@rnmapbox/maps/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties
new file mode 100644
index 0000000..1211b1e
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties
@@ -0,0 +1,6 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
+coreLibraryDesugaringEnabled=false
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json b/node_modules/@rnmapbox/maps/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar b/node_modules/@rnmapbox/maps/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar
new file mode 100644
index 0000000..5788a59
Binary files /dev/null and b/node_modules/@rnmapbox/maps/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar differ
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt b/node_modules/@rnmapbox/maps/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt
new file mode 100644
index 0000000..e6bb791
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt
@@ -0,0 +1,8 @@
+int drawable empty 0x0
+int drawable empty_drawable 0x0
+int drawable red_marker 0x0
+int id annotation_img 0x0
+int id annotation_layout 0x0
+int id annotation_view_container 0x0
+int layout annotation 0x0
+int string app_name 0x0
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/compiled_local_resources/debug/compileDebugLibraryResources/out/drawable-xxhdpi-v4_red_marker.png.flat b/node_modules/@rnmapbox/maps/android/build/intermediates/compiled_local_resources/debug/compileDebugLibraryResources/out/drawable-xxhdpi-v4_red_marker.png.flat
new file mode 100644
index 0000000..de15fdf
Binary files /dev/null and b/node_modules/@rnmapbox/maps/android/build/intermediates/compiled_local_resources/debug/compileDebugLibraryResources/out/drawable-xxhdpi-v4_red_marker.png.flat differ
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/compiled_local_resources/debug/compileDebugLibraryResources/out/drawable_empty.xml.flat b/node_modules/@rnmapbox/maps/android/build/intermediates/compiled_local_resources/debug/compileDebugLibraryResources/out/drawable_empty.xml.flat
new file mode 100644
index 0000000..5526dc1
Binary files /dev/null and b/node_modules/@rnmapbox/maps/android/build/intermediates/compiled_local_resources/debug/compileDebugLibraryResources/out/drawable_empty.xml.flat differ
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/compiled_local_resources/debug/compileDebugLibraryResources/out/drawable_empty_drawable.png.flat b/node_modules/@rnmapbox/maps/android/build/intermediates/compiled_local_resources/debug/compileDebugLibraryResources/out/drawable_empty_drawable.png.flat
new file mode 100644
index 0000000..e2f4968
Binary files /dev/null and b/node_modules/@rnmapbox/maps/android/build/intermediates/compiled_local_resources/debug/compileDebugLibraryResources/out/drawable_empty_drawable.png.flat differ
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/compiled_local_resources/debug/compileDebugLibraryResources/out/layout_annotation.xml.flat b/node_modules/@rnmapbox/maps/android/build/intermediates/compiled_local_resources/debug/compileDebugLibraryResources/out/layout_annotation.xml.flat
new file mode 100644
index 0000000..d6a30e3
Binary files /dev/null and b/node_modules/@rnmapbox/maps/android/build/intermediates/compiled_local_resources/debug/compileDebugLibraryResources/out/layout_annotation.xml.flat differ
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties b/node_modules/@rnmapbox/maps/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..7155234
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
@@ -0,0 +1,5 @@
+#Fri Jun 13 02:38:05 TRT 2025
+com.rnmapbox.rnmbx.rnmapbox_maps-main-6\:/drawable-xxhdpi/red_marker.png=/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/build/intermediates/packaged_res/debug/packageDebugResources/drawable-xxhdpi-v4/red_marker.png
+com.rnmapbox.rnmbx.rnmapbox_maps-main-6\:/drawable/empty_drawable.png=/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/build/intermediates/packaged_res/debug/packageDebugResources/drawable/empty_drawable.png
+com.rnmapbox.rnmbx.rnmapbox_maps-main-6\:/drawable/empty.xml=/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/build/intermediates/packaged_res/debug/packageDebugResources/drawable/empty.xml
+com.rnmapbox.rnmbx.rnmapbox_maps-main-6\:/layout/annotation.xml=/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/build/intermediates/packaged_res/debug/packageDebugResources/layout/annotation.xml
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/incremental/debug/packageDebugResources/merged.dir/values/values.xml b/node_modules/@rnmapbox/maps/android/build/intermediates/incremental/debug/packageDebugResources/merged.dir/values/values.xml
new file mode 100644
index 0000000..9c03766
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/build/intermediates/incremental/debug/packageDebugResources/merged.dir/values/values.xml
@@ -0,0 +1,4 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <string name="app_name">RNMBX</string>
+</resources>
\ No newline at end of file
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml b/node_modules/@rnmapbox/maps/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
new file mode 100644
index 0000000..deaf52e
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/res"><file name="empty" path="/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/res/drawable/empty.xml" qualifiers="" type="drawable"/><file name="empty_drawable" path="/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/res/drawable/empty_drawable.png" qualifiers="" type="drawable"/><file name="annotation" path="/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/res/layout/annotation.xml" qualifiers="" type="layout"/><file path="/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/res/values/strings.xml" qualifiers=""><string name="app_name">RNMBX</string></file><file name="red_marker" path="/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/res/drawable-xxhdpi/red_marker.png" qualifiers="xxhdpi-v4" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/build/generated/res/resValues/debug"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/incremental/mergeDebugAssets/merger.xml b/node_modules/@rnmapbox/maps/android/build/intermediates/incremental/mergeDebugAssets/merger.xml
new file mode 100644
index 0000000..59692bd
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/build/intermediates/incremental/mergeDebugAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/debug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/build/intermediates/shader_assets/debug/compileDebugShaders/out"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml b/node_modules/@rnmapbox/maps/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
new file mode 100644
index 0000000..7606213
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/jniLibs"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/debug/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/incremental/mergeDebugShaders/merger.xml b/node_modules/@rnmapbox/maps/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
new file mode 100644
index 0000000..e312cf7
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/debug/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt b/node_modules/@rnmapbox/maps/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt
new file mode 100644
index 0000000..826dbbd
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt
@@ -0,0 +1,10 @@
+R_DEF: Internal format may change without notice
+local
+drawable empty
+drawable empty_drawable
+drawable red_marker
+id annotation_img
+id annotation_layout
+id annotation_view_container
+layout annotation
+string app_name
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt b/node_modules/@rnmapbox/maps/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..b24d661
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,17 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="com.rnmapbox.rnmbx" >
+4
+5    <uses-sdk android:minSdkVersion="24" />
+6
+7    <uses-permission android:name="android.permission.INTERNET" />
+7-->/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/AndroidManifest.xml:2:5-67
+7-->/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/AndroidManifest.xml:2:22-64
+8    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
+8-->/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/AndroidManifest.xml:3:5-80
+8-->/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/AndroidManifest.xml:3:22-78
+9    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
+9-->/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/AndroidManifest.xml:4:5-78
+9-->/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/AndroidManifest.xml:4:22-76
+10
+11</manifest>
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml b/node_modules/@rnmapbox/maps/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml
new file mode 100644
index 0000000..c8865b5
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml
@@ -0,0 +1,11 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.rnmapbox.rnmbx" >
+
+    <uses-sdk android:minSdkVersion="24" />
+
+    <uses-permission android:name="android.permission.INTERNET" />
+    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
+    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json b/node_modules/@rnmapbox/maps/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt b/node_modules/@rnmapbox/maps/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt
new file mode 100644
index 0000000..08f4ebe
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt
@@ -0,0 +1 @@
+0 Warning/Error
\ No newline at end of file
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/packaged_res/debug/packageDebugResources/drawable-xxhdpi-v4/red_marker.png b/node_modules/@rnmapbox/maps/android/build/intermediates/packaged_res/debug/packageDebugResources/drawable-xxhdpi-v4/red_marker.png
new file mode 100644
index 0000000..be782e1
Binary files /dev/null and b/node_modules/@rnmapbox/maps/android/build/intermediates/packaged_res/debug/packageDebugResources/drawable-xxhdpi-v4/red_marker.png differ
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/packaged_res/debug/packageDebugResources/drawable/empty.xml b/node_modules/@rnmapbox/maps/android/build/intermediates/packaged_res/debug/packageDebugResources/drawable/empty.xml
new file mode 100644
index 0000000..1f83bff
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/build/intermediates/packaged_res/debug/packageDebugResources/drawable/empty.xml
@@ -0,0 +1,5 @@
+<?xml version="1.0" encoding="utf-8"?>
+<shape xmlns:android="http://schemas.android.com/apk/res/android" android:shape="rectangle">
+  <size android:height="1dp" android:width="1dp" />
+  <solid android:color="@android:color/transparent" />
+</shape>
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/packaged_res/debug/packageDebugResources/drawable/empty_drawable.png b/node_modules/@rnmapbox/maps/android/build/intermediates/packaged_res/debug/packageDebugResources/drawable/empty_drawable.png
new file mode 100644
index 0000000..9da19ea
Binary files /dev/null and b/node_modules/@rnmapbox/maps/android/build/intermediates/packaged_res/debug/packageDebugResources/drawable/empty_drawable.png differ
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/packaged_res/debug/packageDebugResources/layout/annotation.xml b/node_modules/@rnmapbox/maps/android/build/intermediates/packaged_res/debug/packageDebugResources/layout/annotation.xml
new file mode 100644
index 0000000..4994bd0
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/build/intermediates/packaged_res/debug/packageDebugResources/layout/annotation.xml
@@ -0,0 +1,17 @@
+<?xml version="1.0" encoding="utf-8"?>
+<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
+    android:id="@+id/annotation_layout"
+    android:layout_width="wrap_content"
+    android:layout_height="wrap_content">
+
+    <ImageView
+        android:id="@+id/annotation_img"
+        android:layout_width="match_parent"
+        android:layout_height="match_parent" />
+
+    <LinearLayout
+        android:orientation="vertical"
+        android:id="@+id/annotation_view_container"
+        android:layout_width="wrap_content"
+        android:layout_height="wrap_content" />
+</RelativeLayout>
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/packaged_res/debug/packageDebugResources/values/values.xml b/node_modules/@rnmapbox/maps/android/build/intermediates/packaged_res/debug/packageDebugResources/values/values.xml
new file mode 100644
index 0000000..9c03766
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/build/intermediates/packaged_res/debug/packageDebugResources/values/values.xml
@@ -0,0 +1,4 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <string name="app_name">RNMBX</string>
+</resources>
\ No newline at end of file
diff --git a/node_modules/@rnmapbox/maps/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt b/node_modules/@rnmapbox/maps/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt
new file mode 100644
index 0000000..af46f7d
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt
@@ -0,0 +1,9 @@
+com.rnmapbox.rnmbx
+drawable empty
+drawable empty_drawable
+drawable red_marker
+id annotation_img
+id annotation_layout
+id annotation_view_container
+layout annotation
+string app_name
diff --git a/node_modules/@rnmapbox/maps/android/build/kotlin/compileDebugKotlin/cacheable/dirty-sources.txt b/node_modules/@rnmapbox/maps/android/build/kotlin/compileDebugKotlin/cacheable/dirty-sources.txt
new file mode 100644
index 0000000..216b972
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/build/kotlin/compileDebugKotlin/cacheable/dirty-sources.txt
@@ -0,0 +1,145 @@
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/extensions/Point.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/sources/RNMBXShapeSource.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/Logger.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/layers/RNMBXBackgroundLayer.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/GeoJSONUtils.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/layers/RNMBXFillExtrusionLayer.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/layers/RNMBXLineLayer.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/camera/RNMBXCamera.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/images/RNMBXImageManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/camera/RNMBXViewport.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/modules/RNMBXLogging.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/PropertyChanges.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/location/LocationManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/events/MapChangeEvent.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/annotation/RNMBXMarkerViewContent.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/mapview/NativeMapViewModule.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/mapbox-v11-compat/v11/com/rnmapbox/rnmbx/v11compat/OfflineManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/mapbox-v11-compat/v11/com/rnmapbox/rnmbx/v11compat/Image.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/sources/RNMBXShapeSourceModule.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/LatLngBounds.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/mapbox-v11-compat/v11/com/rnmapbox/rnmbx/v11compat/Feature.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/BitmapUtils.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/sources/RNMBXSource.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/images/RNMBXImage.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/extensions/Geometry.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/light/RNMBXLightManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/layers/RNMBXCircleLayerManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/events/MapUserTrackingModeEvent.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/mapbox-v11-compat/v11/com/rnmapbox/rnmbx/v11compat/Cancelable.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/mapbox-v11-compat/v11/com/rnmapbox/rnmbx/v11compat/Location.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/images/RNMBXImages.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/modules/RNMBXSnapshotModule.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/sources/AbstractSourceConsumer.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/location/RNMBXNativeUserLocation.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/AbstractEvent.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/annotation/RNMBXMarkerViewManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/camera/CameraUpdateQueue.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/camera/CameraStop.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/modules/RNMBXTileStoreModule.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/sources/RNMBXTileSource.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/model/RNMBXModelsManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/extensions/FeatureCollection.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/RNMBXStyleFactory.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/modules/RNMBXOfflineModuleLegacy.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/modules/RNMBXOfflineModule.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/images/RNMBXImageModule.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/mapbox-v11-compat/v11/com/rnmapbox/rnmbx/v11compat/MapboxMap.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/lifecycle-compat/v25/com/rnmapbox/rnmbx/components/mapview/LifecycleCompat.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/layers/RNMBXModelLayerManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/modules/RNMBXLocationModule.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/sources/RNMBXVectorSourceManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/writeableMapArrayOf.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/mapview/RNMBXMapViewManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/light/RNMBXLight.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/layers/RNMBXSymbolLayer.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/sources/RNMBXVectorSource.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/mapbox-v11-compat/v11/com/rnmapbox/rnmbx/v11compat/Light.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/layers/RNMBXRasterLayerManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/shapeAnimators/RNMBXChangeLineOffsetsShapeAnimatorModule.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/mapbox-v11-compat/v11/com/rnmapbox/rnmbx/v11compat/ResourceOption.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/events/AbstractEvent.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/events/constants/EventTypes.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/layers/RNMBXSkyLayer.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/layers/RNMBXBackgroundLayerManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/layers/RNMBXHeatmapLayer.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/extensions/ReadableMap.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/layers/RNMBXLineLayerManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/model/RNMBXModels.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/layers/RNMBXFillExtrusionLayerManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/extensions/ReadableArray.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/sources/RNMBXRasterDemSourceManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/location/RNMBXCustomLocationProvider.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/RNMBXStyleImportManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/annotation/RNMBXCallout.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/sources/RNMBXShapeSourceManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/mapview/RNMBXMapView.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/sources/RNMBXRasterSource.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/camera/RNMBXCameraManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/atmosphere/RNMBXAtmosphere.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/events/constants/EventKeys.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/location/RNMBXCustomLocationProviderManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/mapbox-v11-compat/v11/com/rnmapbox/rnmbx/v11compat/HttpInterceptor.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/camera/RNMBXCameraModule.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/annotation/RNMBXMarkerViewContentManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/AbstractEventEmitter.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/camera/RNMBXViewportModule.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/RNMBXStyle.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/annotation/RNMBXPointAnnotationManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/mapbox-v11-compat/v11/com/rnmapbox/rnmbx/v11compat/Layer.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/annotation/RNMBXCalloutManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/modules/CustomHttpHeaders.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/location/RNMBXNativeUserLocationManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/layers/RNMBXCircleLayer.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/layers/RNMBXSymbolLayerManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/sources/RNMBXRasterSourceManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/events/IEvent.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/mapbox-v11-compat/v11/com/rnmapbox/rnmbx/v11compat/OrnamentSettings.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/annotation/RNMBXMarkerView.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/annotation/RNMBXPointAnnotation.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/extensions/JSONObject.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/rn-compat/rn75/com/rnmapbox/rnmbx/rncompat/ReadableMap.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/extensions/Dynamic.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/rn-compat/rn75/com/rnmapbox/rnmbx/rncompat/Dynamic.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/atmosphere/RNMBXAtmosphereManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/layers/RNMBXRasterLayer.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/shapeAnimators/ShapeAnimatorCommon.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/ViewTagResolver.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/mapbox-v11-compat/v11/com/rnmapbox/rnmbx/v11compat/Snapshot.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/annotation/RNMBXPointAnnotationCoordinator.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/layers/RNMBXModelLayer.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/AbstractMapFeature.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/location/LocationComponentManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/mapbox-v11-compat/v11/com/rnmapbox/rnmbx/v11compat/Event.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/terrain/RNMBXTerrain.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/extensions/Value.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/RNMBXStyleImport.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/images/ImageManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/layers/RNMBXLayer.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/modules/RNMBXModule.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/mapbox-v11-compat/v11/com/rnmapbox/rnmbx/v11compat/StyleFactory.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/layers/RNMBXFillLayer.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/layers/RNMBXFillLayerManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/location/UserTrackingMode.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/annotation/RNMBXPointAnnotationModule.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/events/LocationEvent.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/RNMBXPackage.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/extensions/CoordinateBounds.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/layers/RNMBXSkyLayerManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/images/RNMBXImagesManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/camera/RNMBXVIewportManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/terrain/RNMBXTerrainManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/sources/RNMBXRasterDemSource.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/sources/RNMBXImageSourceManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/layers/RNMBXHeatmapLayerManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/mapbox-v11-compat/v11/com/rnmapbox/rnmbx/v11compat/style.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/ImageEntry.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/shapeAnimators/AnimatableElement.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/sources/RNMBXImageSource.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/RNMBXStyleValue.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/mapbox-v11-compat/v11/com/rnmapbox/rnmbx/v11compat/Annotation.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/utils/DownloadMapImageTask.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/shapeAnimators/RNMBXMovePointShapeAnimatorModule.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/styles/sources/RNMBXTileSourceManager.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/camera/CameraUpdateItem.kt
+/Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/java/com/rnmapbox/rnmbx/components/mapview/helpers/CameraChangeTracker.kt
\ No newline at end of file
diff --git a/node_modules/@rnmapbox/maps/android/build/kotlin/compileDebugKotlin/local-state/build-history.bin b/node_modules/@rnmapbox/maps/android/build/kotlin/compileDebugKotlin/local-state/build-history.bin
new file mode 100644
index 0000000..d4b9192
Binary files /dev/null and b/node_modules/@rnmapbox/maps/android/build/kotlin/compileDebugKotlin/local-state/build-history.bin differ
diff --git a/node_modules/@rnmapbox/maps/android/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/@rnmapbox/maps/android/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..8816c2a
--- /dev/null
+++ b/node_modules/@rnmapbox/maps/android/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,29 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/AndroidManifest.xml:1:1-5:12
+INJECTED from /Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/AndroidManifest.xml:1:1-5:12
+	package
+		ADDED from /Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/AndroidManifest.xml:1:70-98
+		INJECTED from /Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/AndroidManifest.xml:1:11-69
+uses-permission#android.permission.INTERNET
+ADDED from /Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/AndroidManifest.xml:2:5-67
+	android:name
+		ADDED from /Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/AndroidManifest.xml:2:22-64
+uses-permission#android.permission.ACCESS_COARSE_LOCATION
+ADDED from /Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/AndroidManifest.xml:3:5-80
+	android:name
+		ADDED from /Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/AndroidManifest.xml:3:22-78
+uses-permission#android.permission.ACCESS_FINE_LOCATION
+ADDED from /Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/AndroidManifest.xml:4:5-78
+	android:name
+		ADDED from /Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/AndroidManifest.xml:4:22-76
+uses-sdk
+INJECTED from /Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/Desktop/fishivo/mobile/node_modules/@rnmapbox/maps/android/src/main/AndroidManifest.xml
diff --git a/node_modules/@rnmapbox/maps/android/src/main/lifecycle-compat/v25/com/rnmapbox/rnmbx/components/mapview/LifecycleCompat.kt b/node_modules/@rnmapbox/maps/android/src/main/lifecycle-compat/v25/com/rnmapbox/rnmbx/components/mapview/LifecycleCompat.kt
index 80e49ed..f347c88 100644
--- a/node_modules/@rnmapbox/maps/android/src/main/lifecycle-compat/v25/com/rnmapbox/rnmbx/components/mapview/LifecycleCompat.kt
+++ b/node_modules/@rnmapbox/maps/android/src/main/lifecycle-compat/v25/com/rnmapbox/rnmbx/components/mapview/LifecycleCompat.kt
@@ -5,7 +5,7 @@ import android.view.View
 import androidx.lifecycle.Lifecycle
 import androidx.lifecycle.LifecycleOwner
 import androidx.lifecycle.LifecycleRegistry
-import androidx.lifecycle.ViewTreeLifecycleOwner
+import androidx.lifecycle.setViewTreeLifecycleOwner
 
 /**
  * Lifecycle compatibility for Lifecycle 2.5 and older which uses getLifecycle() method
@@ -34,13 +34,12 @@ class RNMBXLifeCycle {
                     }
                 }
 
-                // Lifecycle 2.5 and older uses method syntax
-                override fun getLifecycle(): Lifecycle {
-                    return lifecycleRegistry
-                }
+                // Lifecycle 2.6+ uses property syntax
+                override val lifecycle: Lifecycle
+                    get() = lifecycleRegistry
 
             }
-            ViewTreeLifecycleOwner.set(view, lifecycleOwner)
+            view.setViewTreeLifecycleOwner(lifecycleOwner)
         }
         lifecycleOwner?.handleLifecycleEvent(Lifecycle.Event.ON_START)
     }
