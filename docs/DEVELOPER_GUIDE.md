# FISHIVO - DEVELOPER GUIDE

## 📋 İçindekiler
1. [Development Setup](#development-setup)
2. [<PERSON>](#hook-kullanımı)
3. [Component Integration](#component-integration)
4. [State Management](#state-management)
5. [Testing Strategies](#testing-strategies)
6. [Debugging](#debugging)
7. [Performance Tips](#performance-tips)

---

## 🚀 Development Setup

### 📦 Dependencies

```json
{
  "dependencies": {
    "@react-native-async-storage/async-storage": "^1.19.0",
    "react": "^18.2.0",
    "react-native": "^0.72.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.0",
    "jest": "^29.0.0",
    "typescript": "^5.0.0"
  }
}
```

### 🛠 Initial Setup

```bash
# 1. Install dependencies
npm install

# 2. iOS setup
cd ios && pod install && cd ..

# 3. Start Metro bundler
npm start

# 4. Run on device
npm run ios
npm run android
```

### 📁 Project Structure

```
src/
├── hooks/
│   └── useUnits.ts                    # ⭐ Ana birim hook'u
├── utils/
│   └── unitConversion.ts              # 🔄 Conversion utilities
├── data/
│   ├── unitDefinitions.json          # 📊 Birim tanımları
│   ├── userUnitPreferences.json      # 👤 Kullanıcı tercihleri
│   └── settingsConfiguration.json    # ⚙️ Ayar config
├── screens/
│   ├── AddCatchScreen.tsx            # ➕ Input conversion örneği
│   ├── UnitsSettingsScreen.tsx       # ⚙️ Settings implementation
│   └── HomeScreen.tsx                # 📱 Display conversion örneği
└── components/
    ├── CatchCard.tsx                 # 🎣 Display component örneği
    └── ...
```

---

## 🎣 Hook Kullanımı

### 🔧 useUnits Hook - Complete Guide

```typescript
import { useUnits } from '../hooks/useUnits';

const MyComponent = () => {
  const {
    // 📊 State
    units,                    // Kullanıcının birim tercihleri
    isLoading,               // Hook yükleniyor mu?
    
    // 🔤 Unit Getters
    getWeightUnit,           // "kg", "lbs", etc.
    getLengthUnit,           // "cm", "inch", etc.
    getTemperatureUnit,      // "°C", "°F", etc.
    
    // 🎨 Simple Formatters (conversion yok)
    formatWeight,            // Sadece unit symbol ekler
    formatLength,            // Sadece unit symbol ekler
    formatTemperature,       // Sadece unit symbol ekler
    
    // 🔄 Smart Converters (gerçek conversion)
    convertFromUserUnit,     // User unit → Base unit (kayıt için)
    convertToUserUnit,       // Base unit → User unit (display için)
    convertAndFormat,        // Base unit → Formatted string
    convertToBaseUnit,       // Any unit → Base unit
  } = useUnits();

  // Hook yüklenene kadar loading göster
  if (isLoading) {
    return <LoadingSpinner />;
  }

  // ... component logic
};
```

### 📝 Input Scenarios (Kayıt Oluştururken)

```typescript
// Scenario 1: Basit input
const AddCatchBasic = () => {
  const { convertFromUserUnit, getWeightUnit } = useUnits();
  const [weight, setWeight] = useState('');

  const handleSave = async () => {
    // Kullanıcının girdiği değeri base unit'e çevir
    const weightInKg = convertFromUserUnit(parseFloat(weight), 'weight');
    
    await saveCatch({
      weight: {
        value: weightInKg,      // Backend için
        unit: 'kg',             // Base unit
        originalUnit: units.weight,
        displayValue: `${weight} ${getWeightUnit()}`
      }
    });
  };

  return (
    <TextInput
      placeholder={`Ağırlık (${getWeightUnit()})`}
      value={weight}
      onChangeText={setWeight}
      keyboardType="decimal-pad"
    />
  );
};
```

```typescript
// Scenario 2: Gelişmiş input validation
const AddCatchAdvanced = () => {
  const { convertFromUserUnit, units } = useUnits();
  const [measurements, setMeasurements] = useState({
    weight: '',
    length: '',
    depth: ''
  });
  const [errors, setErrors] = useState({});

  const validateAndConvert = (category, value) => {
    const numValue = parseFloat(value);
    
    // Validation
    if (isNaN(numValue) || numValue <= 0) {
      setErrors(prev => ({ ...prev, [category]: 'Geçersiz değer' }));
      return null;
    }

    // Convert to base unit
    const baseValue = convertFromUserUnit(numValue, category);
    
    // Range check for base unit
    const ranges = {
      weight: { min: 0.001, max: 1000 }, // kg
      length: { min: 0.1, max: 1000 },   // cm
      depth: { min: 0.1, max: 11000 }    // meters
    };

    const range = ranges[category];
    if (baseValue < range.min || baseValue > range.max) {
      setErrors(prev => ({ 
        ...prev, 
        [category]: `Değer ${range.min}-${range.max} arasında olmalı`
      }));
      return null;
    }

    // Clear error
    setErrors(prev => ({ ...prev, [category]: null }));
    return baseValue;
  };

  const handleSave = async () => {
    const weightInKg = validateAndConvert('weight', measurements.weight);
    const lengthInCm = validateAndConvert('length', measurements.length);
    
    if (!weightInKg || !lengthInCm) return;

    await saveCatch({
      weight: { value: weightInKg, unit: 'kg' },
      length: { value: lengthInCm, unit: 'cm' }
    });
  };

  return (
    <View>
      <InputField
        category="weight"
        value={measurements.weight}
        onChangeText={(value) => setMeasurements(prev => ({ ...prev, weight: value }))}
        error={errors.weight}
      />
      <InputField
        category="length" 
        value={measurements.length}
        onChangeText={(value) => setMeasurements(prev => ({ ...prev, length: value }))}
        error={errors.length}
      />
    </View>
  );
};
```

### 👀 Display Scenarios (Veri Gösterirken)

```typescript
// Scenario 1: Basit display  
const CatchCardBasic = ({ catch }) => {
  const { convertAndFormat } = useUnits();

  return (
    <View>
      <Text>{catch.species}</Text>
      <Text>{convertAndFormat(catch.weight.value, 'weight')}</Text>
      <Text>{convertAndFormat(catch.length.value, 'length')}</Text>
    </View>
  );
};
```

```typescript
// Scenario 2: Backward compatibility (eski string format)
const CatchCardCompatible = ({ catch }) => {
  const { convertAndFormat } = useUnits();

  const formatMeasurement = (measurement, category) => {
    // Yeni object format
    if (typeof measurement === 'object' && measurement.value) {
      return convertAndFormat(measurement.value, category);
    }
    
    // Eski string format - parse edip convert et
    if (typeof measurement === 'string') {
      const match = measurement.match(/^([\d.]+)\s*([a-zA-Z°]+)$/);
      if (match) {
        const value = parseFloat(match[1]);
        const unit = normalizeUnit(match[2]);
        
        // Eğer base unit ise direkt convert et
        if (isBaseUnit(unit, category)) {
          return convertAndFormat(value, category);
        }
      }
      // Parse edilemezse olduğu gibi döndür
      return measurement;
    }
    
    return '';
  };

  return (
    <View>
      <Text>{formatMeasurement(catch.weight, 'weight')}</Text>
      <Text>{formatMeasurement(catch.length, 'length')}</Text>
    </View>
  );
};
```

---

## 🧩 Component Integration

### 🎯 Component Hierarchy

```typescript
// App Level - Units Provider
const App = () => {
  return (
    <AuthProvider>
      <UnitsProvider>  {/* Global units context */}
        <NavigationContainer>
          <AppNavigator />
        </NavigationContainer>
      </UnitsProvider>
    </AuthProvider>
  );
};

// Screen Level - useUnits hook
const CatchListScreen = () => {
  const { convertAndFormat } = useUnits();
  const [catches, setCatches] = useState([]);

  const formatCatchesForDisplay = (rawCatches) => {
    return rawCatches.map(catch => ({
      ...catch,
      displayWeight: convertAndFormat(catch.weight.value, 'weight'),
      displayLength: convertAndFormat(catch.length.value, 'length')
    }));
  };

  return (
    <FlatList
      data={formatCatchesForDisplay(catches)}
      renderItem={({ item }) => <CatchCard catch={item} />}
    />
  );
};

// Component Level - Formatted props
const CatchCard = ({ catch }) => {
  // Bu noktada conversion yapılmış
  return (
    <View>
      <Text>{catch.displayWeight}</Text>
      <Text>{catch.displayLength}</Text>
    </View>
  );
};
```

### 🔄 Real-time Updates

```typescript
// Settings değiştiğinde otomatik update
const CatchDisplay = ({ catch }) => {
  const { convertAndFormat, units } = useUnits();
  const [displayValues, setDisplayValues] = useState({});

  // Units değiştiğinde yeniden hesapla
  useEffect(() => {
    setDisplayValues({
      weight: convertAndFormat(catch.weight.value, 'weight'),
      length: convertAndFormat(catch.length.value, 'length'),
      depth: convertAndFormat(catch.depth.value, 'depth')
    });
  }, [catch, units]); // units dependency'si önemli!

  return (
    <View>
      <Text>Ağırlık: {displayValues.weight}</Text>
      <Text>Boy: {displayValues.length}</Text>
      <Text>Derinlik: {displayValues.depth}</Text>
    </View>
  );
};
```

### 🎨 Custom Hooks

```typescript
// Özel hook - Measurement formatting
const useMeasurementFormatter = () => {
  const { convertAndFormat } = useUnits();

  const formatCatch = useCallback((catch) => {
    return {
      ...catch,
      formattedWeight: convertAndFormat(catch.weight.value, 'weight'),
      formattedLength: convertAndFormat(catch.length.value, 'length')
    };
  }, [convertAndFormat]);

  const formatSpot = useCallback((spot) => {
    return {
      ...spot,
      formattedDepth: convertAndFormat(spot.depth.value, 'depth'),
      formattedDistance: convertAndFormat(spot.distance.value, 'distance')
    };
  }, [convertAndFormat]);

  return { formatCatch, formatSpot };
};

// Kullanım
const CatchList = () => {
  const { formatCatch } = useMeasurementFormatter();
  const [catches, setCatches] = useState([]);

  const formattedCatches = useMemo(() => {
    return catches.map(formatCatch);
  }, [catches, formatCatch]);

  return (
    <FlatList
      data={formattedCatches}
      renderItem={({ item }) => <CatchCard catch={item} />}
    />
  );
};
```

---

## 🗃 State Management

### 📊 Local State Patterns

```typescript
// Pattern 1: Single measurement state
const [weight, setWeight] = useState('');
const [weightInKg, setWeightInKg] = useState(0);

const handleWeightChange = (value) => {
  setWeight(value);
  const converted = convertFromUserUnit(parseFloat(value), 'weight');
  setWeightInKg(converted);
};

// Pattern 2: Multiple measurements state
const [measurements, setMeasurements] = useState({
  weight: { input: '', converted: 0 },
  length: { input: '', converted: 0 },
  depth: { input: '', converted: 0 }
});

const updateMeasurement = (category, value) => {
  const converted = convertFromUserUnit(parseFloat(value), category);
  setMeasurements(prev => ({
    ...prev,
    [category]: { input: value, converted }
  }));
};
```

### 🎯 Redux Integration

```typescript
// Actions
const updateUserUnits = (units) => ({
  type: 'UPDATE_USER_UNITS',
  payload: units
});

const convertCatchData = (catches) => ({
  type: 'CONVERT_CATCH_DATA', 
  payload: catches
});

// Reducer
const unitsReducer = (state = initialState, action) => {
  switch (action.type) {
    case 'UPDATE_USER_UNITS':
      return {
        ...state,
        userUnits: action.payload,
        // Mevcut data'yı yeni units ile convert et
        formattedCatches: state.rawCatches.map(catch => 
          convertCatchForUser(catch, action.payload)
        )
      };
    default:
      return state;
  }
};

// Middleware - Auto conversion
const conversionMiddleware = (store) => (next) => (action) => {
  const result = next(action);
  
  if (action.type === 'FETCH_CATCHES_SUCCESS') {
    const { userUnits } = store.getState().units;
    const converted = action.payload.map(catch => 
      convertCatchForUser(catch, userUnits)
    );
    store.dispatch({ type: 'SET_FORMATTED_CATCHES', payload: converted });
  }
  
  return result;
};
```

### 🔄 Context Pattern

```typescript
// UnitsContext.tsx
const UnitsContext = createContext();

export const UnitsProvider = ({ children }) => {
  const unitsHook = useUnits();
  
  // Global conversion cache
  const [conversionCache, setConversionCache] = useState(new Map());
  
  const cachedConvertAndFormat = useCallback((value, category) => {
    const key = `${value}_${category}_${unitsHook.units[category]}`;
    
    if (conversionCache.has(key)) {
      return conversionCache.get(key);
    }
    
    const result = unitsHook.convertAndFormat(value, category);
    setConversionCache(prev => new Map(prev.set(key, result)));
    return result;
  }, [unitsHook, conversionCache]);

  const contextValue = {
    ...unitsHook,
    convertAndFormat: cachedConvertAndFormat
  };

  return (
    <UnitsContext.Provider value={contextValue}>
      {children}
    </UnitsContext.Provider>
  );
};
```

---

## 🧪 Testing Strategies

### ⚡ Unit Tests

```typescript
// useUnits hook test
import { renderHook, act } from '@testing-library/react-hooks';
import { useUnits } from '../hooks/useUnits';

describe('useUnits', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    AsyncStorage.clear();
  });

  test('should load default units', async () => {
    const { result, waitForNextUpdate } = renderHook(() => useUnits());
    
    await waitForNextUpdate();
    
    expect(result.current.units.weight).toBe('kg');
    expect(result.current.units.length).toBe('cm');
    expect(result.current.isLoading).toBe(false);
  });

  test('should convert from user unit to base unit', async () => {
    const { result, waitForNextUpdate } = renderHook(() => useUnits());
    
    await waitForNextUpdate();
    
    // lbs to kg conversion
    act(() => {
      const converted = result.current.convertFromUserUnit(5.5, 'weight');
      expect(converted).toBeCloseTo(2.495, 2);
    });
  });

  test('should format measurement for display', async () => {
    const { result, waitForNextUpdate } = renderHook(() => useUnits());
    
    await waitForNextUpdate();
    
    act(() => {
      const formatted = result.current.convertAndFormat(2.5, 'weight');
      expect(formatted).toBe('2.5 kg');
    });
  });
});
```

### 🎬 Integration Tests

```typescript
// Component integration test
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import AddCatchScreen from '../screens/AddCatchScreen';

describe('AddCatchScreen Integration', () => {
  test('should save catch with converted units', async () => {
    const mockSaveCatch = jest.fn();
    
    const { getByPlaceholderText, getByText } = render(
      <AddCatchScreen saveCatch={mockSaveCatch} />
    );

    // Input in user units (lbs)
    fireEvent.changeText(getByPlaceholderText('Ağırlık (lbs)'), '5.5');
    fireEvent.changeText(getByPlaceholderText('Boy (inch)'), '25.6');
    
    fireEvent.press(getByText('Kaydet'));

    await waitFor(() => {
      expect(mockSaveCatch).toHaveBeenCalledWith({
        weight: { value: 2.495, unit: 'kg' },
        length: { value: 65.0, unit: 'cm' }
      });
    });
  });
});
```

### 🔍 E2E Tests

```typescript
// Detox E2E test
describe('Units System E2E', () => {
  beforeAll(async () => {
    await device.launchApp();
  });

  test('user can change units and see updated values', async () => {
    // Navigate to settings
    await element(by.id('settings-tab')).tap();
    await element(by.id('units-settings')).tap();
    
    // Change weight unit to lbs
    await element(by.id('weight-unit-selector')).tap();
    await element(by.text('Pound (lbs)')).tap();
    
    // Go back to catch list
    await element(by.id('back-button')).tap();
    await element(by.id('home-tab')).tap();
    
    // Check if values are converted
    await expect(element(by.id('catch-weight-0'))).toHaveText('5.51 lbs');
  });
});
```

---

## 🔍 Debugging

### 🛠 Debug Utilities

```typescript
// Debug hook
const useUnitsDebug = () => {
  const unitsHook = useUnits();
  
  useEffect(() => {
    console.log('🔧 Units Debug:', {
      currentUnits: unitsHook.units,
      isLoading: unitsHook.isLoading
    });
  }, [unitsHook.units, unitsHook.isLoading]);

  const debugConvertAndFormat = (value, category) => {
    console.log(`🔄 Converting ${value} ${category}:`);
    console.log(`  From base unit to: ${unitsHook.units[category]}`);
    
    const result = unitsHook.convertAndFormat(value, category);
    console.log(`  Result: ${result}`);
    
    return result;
  };

  return {
    ...unitsHook,
    convertAndFormat: debugConvertAndFormat
  };
};

// Conversion tracer
const traceConversion = (value, fromUnit, toUnit, category) => {
  console.group(`🔄 Conversion Trace: ${value} ${fromUnit} → ${toUnit}`);
  
  try {
    const result = convertUnit(value, fromUnit, toUnit, category);
    console.log(`✅ Success: ${result} ${toUnit}`);
    console.groupEnd();
    return result;
  } catch (error) {
    console.error(`❌ Error:`, error);
    console.groupEnd();
    throw error;
  }
};
```

### 📊 Performance Monitoring

```typescript
// Performance tracker
const useConversionPerformance = () => {
  const [stats, setStats] = useState({
    totalConversions: 0,
    averageTime: 0,
    slowConversions: []
  });

  const trackedConvertAndFormat = (value, category) => {
    const startTime = performance.now();
    
    const result = convertAndFormat(value, category);
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    setStats(prev => {
      const newTotal = prev.totalConversions + 1;
      const newAverage = (prev.averageTime * prev.totalConversions + duration) / newTotal;
      
      const slowConversions = duration > 10 
        ? [...prev.slowConversions, { value, category, duration }].slice(-10)
        : prev.slowConversions;
      
      return {
        totalConversions: newTotal,
        averageTime: newAverage,
        slowConversions
      };
    });

    if (duration > 10) {
      console.warn(`🐌 Slow conversion: ${duration}ms for ${value} ${category}`);
    }

    return result;
  };

  return { stats, convertAndFormat: trackedConvertAndFormat };
};
```

### 🚨 Error Boundaries

```typescript
// Units Error Boundary
class UnitsErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    if (error.message.includes('conversion') || error.message.includes('unit')) {
      console.error('🔧 Units system error:', error, errorInfo);
      
      // Fallback to default units
      this.setState({ 
        hasError: false, 
        error: null,
        fallbackMode: true 
      });
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <View style={styles.errorContainer}>
          <Text>Birim sistemi hatası oluştu</Text>
          <Button 
            title="Varsayılan birimleri kullan"
            onPress={() => this.setState({ hasError: false, fallbackMode: true })}
          />
        </View>
      );
    }

    return this.props.children;
  }
}
```

---

## ⚡ Performance Tips

### 🚀 Optimization Strategies

```typescript
// 1. Memoization
const CatchCard = React.memo(({ catch }) => {
  const { convertAndFormat } = useUnits();
  
  const formattedValues = useMemo(() => ({
    weight: convertAndFormat(catch.weight.value, 'weight'),
    length: convertAndFormat(catch.length.value, 'length')
  }), [catch.weight.value, catch.length.value, convertAndFormat]);

  return (
    <View>
      <Text>{formattedValues.weight}</Text>
      <Text>{formattedValues.length}</Text>
    </View>
  );
});

// 2. Batch processing
const useBatchConversion = () => {
  const { convertAndFormat } = useUnits();
  
  const convertBatch = useCallback((items, fields) => {
    return items.map(item => {
      const converted = {};
      fields.forEach(field => {
        converted[field] = convertAndFormat(item[field].value, field);
      });
      return { ...item, ...converted };
    });
  }, [convertAndFormat]);

  return { convertBatch };
};

// 3. Virtual list optimization
const CatchVirtualList = () => {
  const { convertBatch } = useBatchConversion();
  const [rawCatches, setRawCatches] = useState([]);
  
  const getItem = useCallback((data, index) => {
    const item = data[index];
    // Lazy conversion - sadece görünür itemlar için
    if (!item.converted) {
      item.converted = {
        weight: convertAndFormat(item.weight.value, 'weight'),
        length: convertAndFormat(item.length.value, 'length')
      };
    }
    return item;
  }, [convertAndFormat]);

  return (
    <VirtualizedList
      data={rawCatches}
      getItem={getItem}
      getItemCount={() => rawCatches.length}
      renderItem={({ item }) => <CatchCard catch={item} />}
    />
  );
};
```

### 💾 Caching Strategies

```typescript
// LRU Cache implementation
class ConversionCache {
  constructor(maxSize = 100) {
    this.cache = new Map();
    this.maxSize = maxSize;
  }

  get(key) {
    if (this.cache.has(key)) {
      const value = this.cache.get(key);
      this.cache.delete(key);
      this.cache.set(key, value);
      return value;
    }
    return null;
  }

  set(key, value) {
    if (this.cache.has(key)) {
      this.cache.delete(key);
    } else if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }
}

// Cached conversion hook
const useCachedConversion = () => {
  const { convertAndFormat: originalConvert } = useUnits();
  const cacheRef = useRef(new ConversionCache());

  const cachedConvertAndFormat = useCallback((value, category) => {
    const key = `${value}_${category}_${units[category]}`;
    
    let result = cacheRef.current.get(key);
    if (result === null) {
      result = originalConvert(value, category);
      cacheRef.current.set(key, result);
    }
    
    return result;
  }, [originalConvert, units]);

  return { convertAndFormat: cachedConvertAndFormat };
};
```

---

## 📚 Advanced Patterns

### 🎯 Custom Hooks Library

```typescript
// hooks/useFormattedMeasurements.ts
export const useFormattedMeasurements = (data) => {
  const { convertAndFormat } = useUnits();
  
  return useMemo(() => {
    if (!data) return null;
    
    return {
      ...data,
      formattedWeight: data.weight ? convertAndFormat(data.weight.value, 'weight') : '',
      formattedLength: data.length ? convertAndFormat(data.length.value, 'length') : '',
      formattedDepth: data.depth ? convertAndFormat(data.depth.value, 'depth') : ''
    };
  }, [data, convertAndFormat]);
};

// hooks/useInputConversion.ts
export const useInputConversion = (category) => {
  const { convertFromUserUnit, getUserUnitSymbol } = useUnits();
  const [input, setInput] = useState('');
  const [converted, setConverted] = useState(0);

  const handleInputChange = useCallback((value) => {
    setInput(value);
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      setConverted(convertFromUserUnit(numValue, category));
    } else {
      setConverted(0);
    }
  }, [convertFromUserUnit, category]);

  return {
    input,
    converted,
    handleInputChange,
    unitSymbol: getUserUnitSymbol(category),
    isValid: !isNaN(parseFloat(input)) && parseFloat(input) > 0
  };
};
```

---

*Son Güncelleme: 20 Aralık 2024*
*Developer Guide v1.0* 