# FISHIVO - TROUBLESHOOTING GUIDE

## 📋 İçindekiler
1. [<PERSON><PERSON><PERSON>şılan Sorunlar](#sık-karşılaşılan-sorunlar)
2. [Debugging Steps](#debugging-steps)
3. [Error Messages](#error-messages)
4. [Performance Issues](#performance-issues)
5. [FAQ](#faq)

---

## 🚨 Sık Karşılaşılan Sorunlar

### ❌ Problem 1: "Conversion sonucu NaN çıkıyor"

**Belirtiler:**
```typescript
const result = convertUnit('abc', 'kg', 'lbs', 'weight'); 
console.log(result); // NaN
```

**Sebep:** Input validation eksikliği

**Çözüm:**
```typescript
// ❌ Yanlış
const convertWeight = (value) => {
  return convertUnit(value, 'kg', 'lbs', 'weight');
};

// ✅ Doğru
const convertWeight = (value) => {
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(numValue) || numValue <= 0) {
    console.warn(`Invalid value for conversion: ${value}`);
    return 0;
  }
  
  return convertUnit(numValue, 'kg', 'lbs', 'weight');
};
```

---

### ❌ Problem 2: "Units değiştiğinde display update olmuyor"

**Belirtiler:**
- Kullanıcı birim değiştiriyor ama UI güncellenmiyor
- Eski birim değerleri gösteriliyor

**Sebep:** useEffect dependency eksikliği

**Çözüm:**
```typescript
// ❌ Yanlış - dependency eksik
const CatchCard = ({ catch }) => {
  const { convertAndFormat } = useUnits();
  const [displayWeight, setDisplayWeight] = useState('');

  useEffect(() => {
    setDisplayWeight(convertAndFormat(catch.weight.value, 'weight'));
  }, [catch.weight.value]); // units dependency eksik!

  return <Text>{displayWeight}</Text>;
};

// ✅ Doğru - units dependency eklendi
const CatchCard = ({ catch }) => {
  const { convertAndFormat, units } = useUnits();
  const [displayWeight, setDisplayWeight] = useState('');

  useEffect(() => {
    setDisplayWeight(convertAndFormat(catch.weight.value, 'weight'));
  }, [catch.weight.value, units.weight, convertAndFormat]); // ✅

  return <Text>{displayWeight}</Text>;
};

// 🚀 En iyisi - useMemo kullan
const CatchCard = ({ catch }) => {
  const { convertAndFormat } = useUnits();

  const displayWeight = useMemo(() => {
    return convertAndFormat(catch.weight.value, 'weight');
  }, [catch.weight.value, convertAndFormat]);

  return <Text>{displayWeight}</Text>;
};
```

---

### ❌ Problem 3: "Backend'den gelen eski string format"

**Belirtiler:**
```javascript
// Backend response
{
  "weight": "2.5 kg",  // String format
  "length": "65 cm"
}
```

**Sebep:** Legacy data format

**Çözüm:**
```typescript
// Migration utility
const parseStringMeasurement = (value, category) => {
  if (typeof value !== 'string') return value;
  
  const match = value.match(/^([\d.]+)\s*([a-zA-Z°]+)$/);
  if (!match) {
    console.warn(`Cannot parse measurement: ${value}`);
    return { value: 0, unit: getBaseUnit(category) };
  }
  
  const numValue = parseFloat(match[1]);
  const unit = normalizeUnit(match[2]);
  
  return {
    value: numValue,
    unit: unit,
    originalUnit: unit,
    displayValue: value
  };
};

// Component'te kullanım
const CatchCard = ({ catch }) => {
  const { convertAndFormat } = useUnits();
  
  const formatMeasurement = (measurement, category) => {
    // Yeni format
    if (typeof measurement === 'object' && measurement.value) {
      return convertAndFormat(measurement.value, category);
    }
    
    // Eski format - parse et
    const parsed = parseStringMeasurement(measurement, category);
    return convertAndFormat(parsed.value, category);
  };

  return (
    <View>
      <Text>{formatMeasurement(catch.weight, 'weight')}</Text>
      <Text>{formatMeasurement(catch.length, 'length')}</Text>
    </View>
  );
};
```

---

### ❌ Problem 4: "AsyncStorage units yüklenmiyor"

**Belirtiler:**
```typescript
const { units, isLoading } = useUnits();
console.log(units); // Her zaman default values
console.log(isLoading); // Her zaman true
```

**Sebep:** AsyncStorage permission veya initialization sorunu

**Debug Steps:**
```typescript
// 1. AsyncStorage test et
const testAsyncStorage = async () => {
  try {
    await AsyncStorage.setItem('test_key', 'test_value');
    const value = await AsyncStorage.getItem('test_key');
    console.log('AsyncStorage test:', value); // "test_value" olmalı
    await AsyncStorage.removeItem('test_key');
  } catch (error) {
    console.error('AsyncStorage error:', error);
  }
};

// 2. Units key'ini kontrol et
const checkUnitsStorage = async () => {
  try {
    const units = await AsyncStorage.getItem('@fishivo_user_units');
    console.log('Stored units:', units);
    if (units) {
      console.log('Parsed units:', JSON.parse(units));
    }
  } catch (error) {
    console.error('Units storage error:', error);
  }
};

// 3. Manual units set et
const setManualUnits = async () => {
  const testUnits = {
    weight: 'lbs',
    length: 'inch',
    temperature: 'fahrenheit'
  };
  
  try {
    await AsyncStorage.setItem('@fishivo_user_units', JSON.stringify(testUnits));
    console.log('Manual units set successfully');
  } catch (error) {
    console.error('Manual units set error:', error);
  }
};
```

**Çözüm:**
```typescript
// Improved useUnits with error handling
export const useUnits = () => {
  const [units, setUnits] = useState(defaultUnits);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadUnits();
  }, []);

  const loadUnits = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const savedUnits = await AsyncStorage.getItem(UNITS_STORAGE_KEY);
      
      if (savedUnits) {
        const parsedUnits = JSON.parse(savedUnits);
        
        // Validation
        if (isValidUnitsObject(parsedUnits)) {
          setUnits(parsedUnits);
        } else {
          console.warn('Invalid units object, using defaults');
          setUnits(defaultUnits);
        }
      } else {
        console.log('No saved units found, using defaults');
        setUnits(defaultUnits);
      }
    } catch (error) {
      console.error('Error loading units:', error);
      setError(error);
      setUnits(defaultUnits); // Fallback
    } finally {
      setIsLoading(false);
    }
  };

  const isValidUnitsObject = (units) => {
    const requiredKeys = ['weight', 'length', 'temperature'];
    return requiredKeys.every(key => 
      units.hasOwnProperty(key) && typeof units[key] === 'string'
    );
  };

  return { units, isLoading, error, /* ... other functions */ };
};
```

---

### ❌ Problem 5: "Temperature conversion yanlış"

**Belirtiler:**
```typescript
convertUnit(0, 'celsius', 'fahrenheit', 'temperature'); // Expected: 32, Got: 0
```

**Sebep:** Temperature conversion özel formula gerektiriyor

**Kontrol:**
```typescript
// unitConversion.ts'de temperature logic'i kontrol et
export const convertUnit = (value, fromUnit, toUnit, category) => {
  if (fromUnit === toUnit) return value;
  
  if (category === 'temperature') {
    // Celsius to Fahrenheit
    if (fromUnit === 'celsius' && toUnit === 'fahrenheit') {
      return (value * 9/5) + 32;
    }
    // Fahrenheit to Celsius  
    if (fromUnit === 'fahrenheit' && toUnit === 'celsius') {
      return (value - 32) * 5/9;
    }
  }
  
  // ... diğer conversion logic
};

// Test et
console.log(convertUnit(0, 'celsius', 'fahrenheit', 'temperature')); // Should be 32
console.log(convertUnit(32, 'fahrenheit', 'celsius', 'temperature')); // Should be 0
console.log(convertUnit(100, 'celsius', 'fahrenheit', 'temperature')); // Should be 212
```

---

## 🔍 Debugging Steps

### 🛠 Step 1: Enable Debug Mode

```typescript
// Debug mode ile units hook'u kullan
const useUnitsDebug = () => {
  const unitsHook = useUnits();
  
  useEffect(() => {
    console.group('🔧 Units Debug Info');
    console.log('Current units:', unitsHook.units);
    console.log('Is loading:', unitsHook.isLoading);
    console.log('Available functions:', Object.keys(unitsHook));
    console.groupEnd();
  }, [unitsHook]);

  return unitsHook;
};

// Component'te kullan
const MyComponent = () => {
  const units = useUnitsDebug(); // Normal useUnits yerine
  // ...
};
```

### 🔬 Step 2: Conversion Trace

```typescript
// Conversion'ları trace et
const traceConversion = (value, fromUnit, toUnit, category) => {
  console.group(`🔄 Conversion: ${value} ${fromUnit} → ${toUnit}`);
  
  try {
    // Pre-checks
    console.log('Input validation:', {
      value: typeof value,
      isNumber: !isNaN(value),
      isPositive: value > 0
    });
    
    // Unit definitions
    const categoryData = unitDefinitions.categories[category];
    const fromUnitData = categoryData?.units[fromUnit];
    const toUnitData = categoryData?.units[toUnit];
    
    console.log('Unit definitions:', {
      category: !!categoryData,
      fromUnit: !!fromUnitData,
      toUnit: !!toUnitData
    });
    
    // Actual conversion
    const result = convertUnit(value, fromUnit, toUnit, category);
    console.log('✅ Result:', result);
    
    return result;
  } catch (error) {
    console.error('❌ Conversion error:', error);
    throw error;
  } finally {
    console.groupEnd();
  }
};
```

### 📊 Step 3: Performance Monitor

```typescript
// Performance monitoring
let conversionStats = {
  total: 0,
  totalTime: 0,
  slowConversions: []
};

const monitoredConvertAndFormat = (value, category) => {
  const startTime = performance.now();
  
  const result = convertAndFormat(value, category);
  
  const endTime = performance.now();
  const duration = endTime - startTime;
  
  conversionStats.total++;
  conversionStats.totalTime += duration;
  
  if (duration > 5) { // 5ms'den yavaş
    conversionStats.slowConversions.push({
      value, category, duration
    });
  }
  
  // Her 100 conversion'da bir stats logla
  if (conversionStats.total % 100 === 0) {
    console.log('📊 Conversion Stats:', {
      total: conversionStats.total,
      averageTime: conversionStats.totalTime / conversionStats.total,
      slowCount: conversionStats.slowConversions.length
    });
  }
  
  return result;
};
```

---

## 🚨 Error Messages

### Type Errors

#### `Cannot read property 'value' of undefined`
```typescript
// Sebep: Measurement object undefined
const catch = { weight: undefined };
const display = convertAndFormat(catch.weight.value, 'weight'); // ❌

// Çözüm: Null check
const display = catch.weight?.value 
  ? convertAndFormat(catch.weight.value, 'weight')
  : '';
```

#### `convertUnit is not a function`
```typescript
// Sebep: Import eksik veya yanlış
import { convertUnit } from '../utils/unitConversion'; // ✅

// Yada units hook'tan kullan
const { convertToUserUnit } = useUnits(); // ✅
```

### Runtime Errors

#### `Invalid hook call`
```typescript
// Sebep: useUnits hook'u class component'te kullanılmış
class MyComponent extends React.Component {
  render() {
    const { units } = useUnits(); // ❌ Class component'te hook kullanılamaz
    return <Text>{units.weight}</Text>;
  }
}

// Çözüm: Functional component kullan
const MyComponent = () => {
  const { units } = useUnits(); // ✅
  return <Text>{units.weight}</Text>;
};
```

#### `AsyncStorage is not available`
```typescript
// Sebep: AsyncStorage import eksik
npm install @react-native-async-storage/async-storage

// iOS için pod install
cd ios && pod install
```

---

## ⚡ Performance Issues

### 🐌 Problem: "Conversion çok yavaş"

**Kontrol Listesi:**
1. **Gereksiz re-render'lar**
   ```typescript
   // ❌ Her render'da yeni conversion
   const CatchCard = ({ catch }) => {
     const { convertAndFormat } = useUnits();
     return <Text>{convertAndFormat(catch.weight.value, 'weight')}</Text>;
   };

   // ✅ Memoized conversion
   const CatchCard = React.memo(({ catch }) => {
     const { convertAndFormat } = useUnits();
     const displayWeight = useMemo(() => 
       convertAndFormat(catch.weight.value, 'weight'),
       [catch.weight.value, convertAndFormat]
     );
     return <Text>{displayWeight}</Text>;
   });
   ```

2. **Batch processing eksikliği**
   ```typescript
   // ❌ Her item için ayrı conversion
   catches.map(catch => ({
     ...catch,
     displayWeight: convertAndFormat(catch.weight.value, 'weight')
   }));

   // ✅ Batch conversion
   const convertCatchesBatch = (catches) => {
     return catches.map(catch => {
       const conversions = {};
       ['weight', 'length', 'depth'].forEach(field => {
         if (catch[field]) {
           conversions[`display${field.charAt(0).toUpperCase() + field.slice(1)}`] = 
             convertAndFormat(catch[field].value, field);
         }
       });
       return { ...catch, ...conversions };
     });
   };
   ```

### 🔥 Problem: "Memory leak"

**Sebep:** Cache'in temizlenmemesi

**Çözüm:**
```typescript
const useUnitsWithCleanup = () => {
  const unitsHook = useUnits();
  const cacheRef = useRef(new Map());

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cacheRef.current.clear();
    };
  }, []);

  // Cleanup on units change
  useEffect(() => {
    cacheRef.current.clear();
  }, [unitsHook.units]);

  return unitsHook;
};
```

---

## ❓ FAQ

### Q1: Kullanıcı units değiştirdiğinde mevcut data nasıl güncellenir?

**A:** useUnits hook'u units değişikliğini detect eder ve tüm bağımlı component'ler re-render olur:

```typescript
// Component otomatik update olur
const { convertAndFormat } = useUnits();
const display = convertAndFormat(value, 'weight'); // Units değiştiğinde otomatik güncellenir
```

### Q2: Backend'e hangi formatta data göndermeliyim?

**A:** Her zaman base units:
```javascript
{
  "weight": { "value": 2.5, "unit": "kg" },
  "length": { "value": 65, "unit": "cm" }
}
```

### Q3: Eski string format ("2.5 kg") ile yeni format arasında migration nasıl yapılır?

**A:** Migration utility kullan:
```typescript
const migrateStringToObject = (stringValue, category) => {
  const match = stringValue.match(/^([\d.]+)\s*([a-zA-Z°]+)$/);
  if (match) {
    return {
      value: parseFloat(match[1]),
      unit: normalizeUnit(match[2])
    };
  }
  return null;
};
```

### Q4: Custom birim ekleyebilir miyim?

**A:** Evet, unitDefinitions.json'a ekleyebilirsin:
```json
{
  "categories": {
    "weight": {
      "units": {
        "stone": {
          "name": "Stone",
          "symbol": "st",
          "conversionFactor": 0.157473,
          "precision": 2,
          "regions": ["UK"]
        }
      }
    }
  }
}
```

### Q5: Temperature conversion neden farklı?

**A:** Temperature'da offset var (0°C = 32°F), diğer birimlerde sadece çarpma faktörü:
```typescript
// Linear conversion (weight, length, etc.)
kg_to_lbs = kg * 2.20462

// Offset conversion (temperature)
celsius_to_fahrenheit = (celsius * 9/5) + 32
```

### Q6: Conversion cache nasıl çalışır?

**A:** LRU (Least Recently Used) cache:
```typescript
// Key format: "value_category_targetUnit"
const key = `${2.5}_weight_${lbs}`;
cache.set(key, "5.51 lbs");

// Cache size limit (default: 100 entries)
// Eski entries otomatik silinir
```

### Q7: Real-time conversion performance'ı nasıl optimize edebilirim?

**A:** 
1. **Memoization kullan**
2. **Batch conversion yap**
3. **Virtual scrolling kullan** (büyük listeler için)
4. **Cache aktive et**

```typescript
const OptimizedCatchList = () => {
  const { convertBatch } = useBatchConversion();
  
  const formattedCatches = useMemo(() => 
    convertBatch(catches, ['weight', 'length']),
    [catches, convertBatch]
  );

  return <VirtualizedList data={formattedCatches} />;
};
```

### Q8: Units system çalışmıyorsa fallback mekanizması var mı?

**A:** Evet, error boundary ve fallback:
```typescript
const UnitsErrorBoundary = ({ children }) => {
  const [hasError, setHasError] = useState(false);

  if (hasError) {
    return <FallbackUnitsProvider>{children}</FallbackUnitsProvider>;
  }

  return <UnitsProvider>{children}</UnitsProvider>;
};

// Fallback provider sadece default units kullanır
const FallbackUnitsProvider = ({ children }) => {
  const fallbackUnits = { weight: 'kg', length: 'cm' };
  return (
    <UnitsContext.Provider value={fallbackUnits}>
      {children}
    </UnitsContext.Provider>
  );
};
```

---

## 📞 Getting Help

### 🐛 Bug Reports
Lütfen şu bilgileri dahil edin:
- **Platform**: iOS/Android
- **React Native versiyon**
- **Hata mesajı** (console log)
- **Repro steps**
- **Expected vs actual behavior**

### 💬 Community Support
- **Discord**: [discord.gg/fishivo-dev](discord.gg/fishivo-dev)
- **GitHub Discussions**: [github.com/fishivo/discussions](github.com/fishivo/discussions)
- **Stack Overflow**: Tag kullan: `fishivo` `react-native` `unit-conversion`

### 🎯 Quick Fixes

```bash
# Clear AsyncStorage
npx react-native run-ios --reset-cache
adb shell am set-debug-app -w com.fishivo

# Reset node_modules
rm -rf node_modules && npm install

# Clear Metro cache
npx react-native start --reset-cache

# Clear iOS build
cd ios && xcodebuild clean && cd ..

# Clear Android build  
cd android && ./gradlew clean && cd ..
```

---

*Son Güncelleme: 20 Aralık 2024*
*Troubleshooting Guide v1.0* 