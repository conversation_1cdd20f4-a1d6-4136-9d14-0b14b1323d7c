# FISHIVO - BİRİM SİSTEMİ DOKÜMANTASYONU

## 📋 İçindekiler
1. [<PERSON>ste<PERSON> Genel Ba<PERSON>ş](#sistem-genel-bakış)
2. [<PERSON><PERSON><PERSON>](#mimari-yapı)
3. [Desteklenen Birimler](#desteklenen-birimler)
4. [<PERSON><PERSON><PERSON><PERSON> Rehberi](#kullanım-rehberi)
5. [API Entegrasyonu](#api-entegrasyonu)
6. [<PERSON><PERSON>](#kod-örnekleri)
7. [Best Practices](#best-practices)
8. [Sorun Giderme](#sorun-giderme)

---

## 🎯 Sistem Genel Bakış

Fishivo birim sistemi, kullanıcıların kendi tercih ettikleri ölçü birimlerini kullanarak av kayıtları oluşturmalarını ve başkalarının kayıtlarını kendi birimlerinde görmelerini sağlar.

### 🎯 Temel Özellikler
- **<PERSON><PERSON><PERSON><PERSON>ı Dönüşüm** - Tü<PERSON> veriler anlık olarak dönüştürülür
- **7 Birim Kategorisi** - Ağırlık, uzunluk, mesafe, sıcaklık, derinlik, hız, basınç
- **6 Bölgesel Preset** - TR, US, UK, EU, CA, AU
- **Veri Tutarlılığı** - Backend'de standart birimler (base units)
- **Precision Kontrolü** - Her birim için uygun hassasiyet

### 🔄 Nasıl Çalışır?

```
📱 Kullanıcı Girişi     🔄 Conversion     💾 Backend     🔄 Conversion     👁 Görüntüleme
   (Kendi Birimi)   →   (Base Unit'e)  →   (Storage)  →  (Kullanıcı      →  (Kendi Birimi)
                                                            Birimine)
   
Örnek:
   5.5 lbs          →      2.495 kg     →    2.495 kg   →     2.5 kg      →    2.5 kg (TR user)
                                                              5.51 lbs     →    5.51 lbs (US user)
```

---

## 🏗 Mimari Yapı

### 📁 Dosya Yapısı

```
src/
├── hooks/
│   └── useUnits.ts                    # Ana birim hook'u
├── utils/
│   └── unitConversion.ts              # Conversion utilities
├── data/
│   ├── unitDefinitions.json          # Birim tanımları
│   ├── userUnitPreferences.json      # Kullanıcı tercihleri
│   ├── unitConversionUtils.json      # Dönüşüm formülleri
│   └── settingsConfiguration.json    # Ayar konfigürasyonu
├── screens/
│   ├── AddCatchScreen.tsx            # Av kayıt ekranı (input conversion)
│   ├── UnitsSettingsScreen.tsx       # Birim ayarları
│   └── ...                           # Diğer ekranlar (display conversion)
└── components/
    ├── CatchCard.tsx                 # Av kartı (display conversion)
    └── ...                           # Diğer bileşenler
```

### 🔧 Ana Bileşenler

#### 1. useUnits Hook
```typescript
const {
  units,                    // Kullanıcının birim tercihleri
  convertFromUserUnit,      // Kullanıcı → Base unit (kayıt için)
  convertToUserUnit,        // Base unit → Kullanıcı (gösterim için)
  convertAndFormat,         // Base unit → Formatlanmış string
  formatWeight,             // Direkt formatla (conversion yok)
  // ... diğer format fonksiyonları
} = useUnits();
```

#### 2. Base Units (Backend Storage)
```javascript
const BASE_UNITS = {
  weight: 'kg',           // Kilogram
  length: 'cm',           // Santimetre  
  distance: 'km',         // Kilometre
  temperature: 'celsius', // Celsius
  depth: 'meters',        // Metre
  speed: 'kmh',          // Kilometre/Saat
  pressure: 'hpa'        // Hektopaskal
}
```

---

## 📏 Desteklenen Birimler

### 1. Ağırlık (Weight)
| Birim | Symbol | Conversion Factor | Regions | Precision |
|-------|--------|------------------|---------|-----------|
| kg    | kg     | 1.0 (base)       | TR, EU  | 2         |
| lbs   | lbs    | 2.20462         | US, UK  | 2         |
| g     | g      | 0.001           | TR, EU  | 0         |
| oz    | oz     | 35.274          | US      | 1         |

### 2. Uzunluk (Length)
| Birim | Symbol | Conversion Factor | Regions | Precision |
|-------|--------|------------------|---------|-----------|
| cm    | cm     | 1.0 (base)       | TR, EU  | 1         |
| inch  | in     | 0.393701        | US, UK  | 1         |
| m     | m      | 0.01            | TR, EU  | 2         |
| ft    | ft     | 0.0328084       | US, UK  | 1         |

### 3. Sıcaklık (Temperature)
| Birim    | Symbol | Formula              | Regions | Precision |
|----------|--------|---------------------|---------|-----------|
| celsius  | °C     | Base unit           | TR, EU  | 1         |
| fahrenheit| °F    | (C × 9/5) + 32      | US      | 1         |

### 4. Diğer Birimler
- **Mesafe**: km, miles, m, nm
- **Derinlik**: meters, feet, fathoms  
- **Hız**: km/h, mph, knots, m/s
- **Basınç**: hPa, inHg, mbar, mmHg

---

## 💻 Kullanım Rehberi

### 🎯 Temel Kullanım

#### 1. Av Kaydı Oluştururken
```typescript
// AddCatchScreen.tsx
const { convertFromUserUnit, getWeightUnit, getLengthUnit } = useUnits();

const handleSave = () => {
  // Kullanıcının girdiği değeri base unit'e çevir
  const weightInKg = convertFromUserUnit(parseFloat(weight), 'weight');
  const lengthInCm = convertFromUserUnit(parseFloat(length), 'length');
  
  const catchData = {
    weight: {
      value: weightInKg,        // Backend için base unit
      unit: 'kg',
      originalUnit: units.weight,
      displayValue: `${weight} ${getWeightUnit()}`
    }
  };
  
  // Backend'e gönder
  await saveCatch(catchData);
};
```

#### 2. Av Verilerini Görüntülerken
```typescript
// CatchCard.tsx
const { convertAndFormat } = useUnits();

const formatCatchWeight = (weight) => {
  if (typeof weight === 'object') {
    // Backend'den gelen base unit'i kullanıcı birimine çevir
    return convertAndFormat(weight.value, 'weight');
  }
  return weight;
};

// Kullanım
<Text>{formatCatchWeight(catch.weight)}</Text>
// TR user: "2.5 kg"
// US user: "5.51 lbs"
```

### ⚙️ Birim Ayarları

#### 1. Kullanıcı Birim Tercihleri Değiştirme
```typescript
// UnitsSettingsScreen.tsx
const handleWeightUnitChange = (newUnit) => {
  const newUnits = { ...units, weight: newUnit };
  setUnits(newUnits);
  await AsyncStorage.setItem('@fishivo_user_units', JSON.stringify(newUnits));
};
```

#### 2. Bölgesel Presetler
```typescript
const REGIONAL_PRESETS = {
  TR: { weight: 'kg', length: 'cm', temperature: 'celsius' },
  US: { weight: 'lbs', length: 'inch', temperature: 'fahrenheit' },
  UK: { weight: 'lbs', length: 'inch', temperature: 'celsius' },
  EU: { weight: 'kg', length: 'cm', temperature: 'celsius' }
};
```

---

## 🔌 API Entegrasyonu

### 📤 Backend'e Veri Gönderme

#### POST /api/catches
```javascript
// Request Body - Her zaman base units
{
  "species": "Levrek",
  "weight": {
    "value": 2.495,
    "unit": "kg",                    // Base unit
    "originalUnit": "lbs",           // Kullanıcının girdiği birim
    "displayValue": "5.5 lbs"        // UI'da gösterilen format
  },
  "length": {
    "value": 65.0,
    "unit": "cm",                    // Base unit
    "originalUnit": "cm",
    "displayValue": "65 cm"
  },
  "location": "İstanbul Boğazı",
  "userId": "user123"
}
```

### 📥 Backend'den Veri Alma

#### GET /api/catches
```javascript
// Response - Base units ile gelir
{
  "catches": [
    {
      "id": "catch123",
      "species": "Levrek", 
      "weight": {
        "value": 2.495,              // Base unit değeri
        "unit": "kg"                 // Base unit
      },
      "length": {
        "value": 65.0,               // Base unit değeri  
        "unit": "cm"                 // Base unit
      }
    }
  ]
}

// Frontend'de conversion
const displayWeight = convertAndFormat(catch.weight.value, 'weight');
// TR user görür: "2.5 kg"  
// US user görür: "5.51 lbs"
```

---

## 📝 Kod Örnekleri

### 🎯 Scenario 1: Farklı Ülkelerden Kullanıcılar

```typescript
// 🇹🇷 Türk kullanıcı av kaydı oluşturuyor
const turkishUser = { weight: 'kg', length: 'cm' };
const inputWeight = 2.5; // kg
const baseWeight = convertFromUserUnit(inputWeight, 'weight'); // 2.5 kg

// 🇺🇸 Amerikalı kullanıcı aynı avı görüyor  
const americanUser = { weight: 'lbs', length: 'inch' };
const displayWeight = convertAndFormat(baseWeight, 'weight'); // "5.51 lbs"

// 🇬🇧 İngiliz kullanıcı aynı avı görüyor
const britishUser = { weight: 'g', length: 'inch' };  
const displayWeight = convertAndFormat(baseWeight, 'weight'); // "2500 g"
```

### 🎯 Scenario 2: Sıcaklık Dönüşümü

```typescript
// Weather data gösterimi
const { convertAndFormat, units } = useUnits();

// Backend'den gelen data (celsius base unit)
const weatherData = {
  temperature: { value: 18, unit: 'celsius' }
};

// Kullanıcı ayarına göre göster
const displayTemp = convertAndFormat(weatherData.temperature.value, 'temperature');

// TR user: "18°C"
// US user: "64°F"
```

### 🎯 Scenario 3: Bulk Conversion

```typescript
// Birden fazla av kaydını dönüştür
const catches = [
  { weight: { value: 2.5, unit: 'kg' }, length: { value: 65, unit: 'cm' } },
  { weight: { value: 1.8, unit: 'kg' }, length: { value: 45, unit: 'cm' } }
];

const displayCatches = catches.map(catch => ({
  ...catch,
  displayWeight: convertAndFormat(catch.weight.value, 'weight'),
  displayLength: convertAndFormat(catch.length.value, 'length')
}));
```

---

## ✅ Best Practices

### 1. 📊 Veri Saklama
- ✅ **Backend'de her zaman base units kullan**
- ✅ **Original unit bilgisini sakla** (opsiyonel)
- ✅ **Display value'yu client-side oluştur**
- ❌ **Backend'de kullanıcı birimlerini saklama**

### 2. 🔄 Conversion Timing
- ✅ **Input sırasında**: User unit → Base unit
- ✅ **Display sırasında**: Base unit → User unit  
- ✅ **Real-time**: Birim değiştiğinde otomatik update
- ❌ **Backend'de conversion yapma**

### 3. 🎯 Precision Management
```typescript
// ✅ Doğru - Birime göre precision
const formatWeight = (value, unit) => {
  const precision = getPrecisionForUnit(unit, 'weight');
  return Math.round(value * Math.pow(10, precision)) / Math.pow(10, precision);
};

// ❌ Yanlış - Sabit precision
const value = Math.round(weight * 100) / 100; // Her zaman 2 decimal
```

### 4. 🧪 Testing
```typescript
// Unit conversion test
test('weight conversion kg to lbs', () => {
  const result = convertUnit(2.5, 'kg', 'lbs', 'weight');
  expect(result).toBeCloseTo(5.51, 2);
});

// Integration test
test('user sees correct units', () => {
  setUserUnits({ weight: 'lbs' });
  const display = convertAndFormat(2.5, 'weight');
  expect(display).toBe('5.51 lbs');
});
```

---

## 🔧 Sorun Giderme

### ❓ Sık Karşılaşılan Sorunlar

#### 1. "Conversion sonucu NaN çıkıyor"
```typescript
// 🔍 Problem
const result = convertUnit('abc', 'kg', 'lbs', 'weight'); // NaN

// ✅ Çözüm - Input validation
const safeConvert = (value, from, to, category) => {
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(numValue)) return 0;
  return convertUnit(numValue, from, to, category);
};
```

#### 2. "Birim değiştiğinde display update olmuyor"
```typescript
// 🔍 Problem - useEffect eksik
const displayWeight = convertAndFormat(weight, 'weight'); // Static

// ✅ Çözüm - useEffect ile dependency
useEffect(() => {
  setDisplayWeight(convertAndFormat(weight, 'weight'));
}, [weight, units.weight]);
```

#### 3. "Backend'den gelen eski string format"
```typescript
// 🔍 Problem - "2.5 kg" string format
const weight = "2.5 kg";

// ✅ Çözüm - String parsing
const parseWeightString = (weightStr) => {
  const match = weightStr.match(/^([\d.]+)\s*([a-zA-Z]+)$/);
  if (match) {
    return {
      value: parseFloat(match[1]),
      unit: match[2].toLowerCase()
    };
  }
  return null;
};
```

### 🚨 Debug Araçları

```typescript
// Console debug için
const debugConversion = (value, from, to, category) => {
  console.log(`Converting ${value} ${from} to ${to}`);
  const result = convertUnit(value, from, to, category);
  console.log(`Result: ${result} ${to}`);
  return result;
};

// Birim kontrol
const checkUserUnits = () => {
  console.log('Current user units:', units);
  console.log('Is loading:', isLoading);
};
```

---

## 📊 Performance Optimizasyon

### 1. Memoization
```typescript
const memoizedConversion = useMemo(() => {
  return convertAndFormat(weight, 'weight');
}, [weight, units.weight]);
```

### 2. Batch Updates
```typescript
// ✅ Doğru - Batch conversion
const formatAllMeasurements = (catch) => {
  return {
    weight: convertAndFormat(catch.weight.value, 'weight'),
    length: convertAndFormat(catch.length.value, 'length'),
    depth: convertAndFormat(catch.depth.value, 'depth')
  };
};
```

---

## 🔄 Güncelleme ve Bakım

### Version History
- **v1.0** - Temel birim sistemi
- **v1.1** - Gerçek conversion eklendi
- **v1.2** - Precision kontrolü iyileştirildi

### Gelecek Özellikler
- [ ] Otomatik bölge algılama
- [ ] Kullanıcı özel birim tanımları
- [ ] Bulk data migration tool
- [ ] A/B testing for unit preferences

---

## 📞 Destek

### Geliştirici Kontakları
- **Backend Team**: <EMAIL>
- **Mobile Team**: <EMAIL>
- **DevOps**: <EMAIL>

### Dokümantasyon
- [API Reference](./API_INTEGRATION_GUIDE.md)
- [Developer Guide](./DEVELOPER_GUIDE.md)
- [Troubleshooting](./TROUBLESHOOTING.md)

---

*Son Güncelleme: 20 Aralık 2024*
*Versiyon: 1.2.0* 