# ScreenContainer Component

## 🎯 Amaç
Tüm uygulamada tutarlı padding kullanımı sağlamak ve tek yerden kontrol edebilmek.

## 🚀 Kullanım

### Basit <PERSON>m
```tsx
import { ScreenContainer } from '../components';

const MyScreen = () => {
  return (
    <SafeAreaView style={styles.container}>
      <ScreenContainer>
        {/* İçerik buraya */}
        <Text>Merhaba Dünya!</Text>
      </ScreenContainer>
    </SafeAreaView>
  );
};
```

### Özelleştirilmiş Padding
```tsx
// Sadece yatay padding
<ScreenContainer paddingVertical="none">
  <Content />
</ScreenContainer>

// Büyük padding
<ScreenContainer paddingHorizontal="lg">
  <Content />
</ScreenContainer>

// Padding yok
<ScreenContainer paddingHorizontal="none" paddingVertical="none">
  <Content />
</ScreenContainer>
```

## ⚙️ Global Kontrol

### Tek <PERSON>rden Padding Değiştirme
`packages/shared/theme/index.ts` dosyasında:

```typescript
screen: {
  paddingHorizontal: 4, // Bu değeri değiştir
  paddingVertical: 0,
},
```

Bu değeri değiştirdiğinde **TÜM UYGULAMA** otomatik olarak güncellenir!

### Mevcut Değerler
- `4px` - Çok sıkışık (mobil için ideal)
- `8px` - Normal
- `12px` - Geniş
- `16px` - Çok geniş

## 📱 Avantajlar

1. **Tutarlılık**: Tüm sayfalarda aynı padding
2. **Tek Kontrol**: Bir yerden tüm uygulamayı değiştir
3. **Responsive**: Farklı ekran boyutları için kolay ayarlama
4. **Temiz Kod**: Manuel padding stilleri gereksiz

## 🔄 Migration

### Eski Yöntem (❌)
```tsx
const styles = StyleSheet.create({
  container: {
    paddingHorizontal: theme.spacing.md, // Her dosyada tekrar
  },
});
```

### Yeni Yöntem (✅)
```tsx
<ScreenContainer>
  {/* Otomatik padding */}
</ScreenContainer>
```

## 🎨 Örnekler

### HomeScreen
```tsx
<SafeAreaView style={styles.container}>
  <Header />
  <ScreenContainer>
    <FlatList data={data} renderItem={renderItem} />
  </ScreenContainer>
</SafeAreaView>
```

### SettingsScreen
```tsx
<SafeAreaView style={styles.container}>
  <Header />
  <ScreenContainer>
    <ScrollView>
      {/* Settings content */}
    </ScrollView>
  </ScreenContainer>
</SafeAreaView>
```

## 🚨 Önemli Notlar

1. **Header'ları sarma**: Header'lar genelde kendi padding'ine sahip olmalı
2. **Full-width content**: Görseller için `paddingHorizontal="none"` kullan
3. **Nested kullanım**: ScreenContainer'ları iç içe kullanma

## 🔧 Gelişmiş Kullanım

### Conditional Padding
```tsx
<ScreenContainer 
  paddingHorizontal={isFullScreen ? 'none' : 'global'}
>
  <Content />
</ScreenContainer>
```

### Custom Style
```tsx
<ScreenContainer 
  style={{ backgroundColor: 'red' }}
  paddingHorizontal="lg"
>
  <Content />
</ScreenContainer>
``` 