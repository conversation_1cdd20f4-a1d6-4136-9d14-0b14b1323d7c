# 🔧 Fishivo Environment Setup Guide

## 📋 Overview

Fishivo projesi **3 farklı platform** için environment variable'ları kullanır:
- **React Native App** (mobil)
- **Next.js Web App** (web frontend)
- **Node.js Backend** (API server)

## 🚀 Quick Setup

### 1. Environment Dosyası Oluştur
```bash
# Root dizinde .env dosyası oluştur
cp env.example .env
```

### 2. G<PERSON>çek Değerleri Doldur
`env.example` dosyasındaki placeholder'ları gerçek değerlerle değiştir.

## 🔑 Environment Variables Explained

### 🌐 Platform-Specific Prefixes

| Prefix | Platform | Açıklama |
|--------|----------|----------|
| `NEXT_PUBLIC_` | Next.js Web App | Browser'da görülebilir |
| Prefix yok | React Native CLI | react-native-config ile |
| Prefix yok | Backend | Node.js process.env |

### 📊 Variable Categories

#### 🔐 **Database (Supabase)**
```env
# Web ve Backend için
NEXT_PUBLIC_SUPABASE_URL=https://xxx.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJxxx...

# React Native için
SUPABASE_URL=https://xxx.supabase.co
SUPABASE_ANON_KEY=eyJxxx...

# Backend için (güçlü yetki)
SUPABASE_SERVICE_ROLE_KEY=eyJxxx...
SUPABASE_JWT_SECRET=xxx...
```

#### 🔒 **Authentication & Security**
```env
JWT_SECRET=minimum_32_character_secret
SESSION_SECRET=minimum_32_character_secret
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12
ADMIN_ALLOWED_IPS=127.0.0.1,::1
```

#### 🌍 **OAuth Providers**
```env
# Google (Google Cloud Console'dan)
GOOGLE_CLIENT_ID=xxx.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-xxx

# Facebook (Facebook Developers'dan)
FACEBOOK_APP_ID=*********
FACEBOOK_APP_SECRET=xxx
```

#### ☁️ **Cloud Storage (Cloudflare R2)**
```env
CLOUDFLARE_ACCOUNT_ID=xxx
CLOUDFLARE_R2_ACCESS_KEY_ID=xxx
CLOUDFLARE_R2_SECRET_ACCESS_KEY=xxx
CLOUDFLARE_R2_BUCKET_NAME=fishivo
```

#### 🗺️ **External Services**
```env
# Mapbox (web haritalar için)
NEXT_PUBLIC_MAPBOX_TOKEN=pk.xxx
```

## 🏗️ Professional Environment Management

### 📁 File Structure
```
fishivo/
├── .env                    # Local development (GIT'e ASLA commit etme!)
├── env.example            # Template (commit edilebilir)
├── .env.development       # Development ortamı
├── .env.staging          # Staging ortamı  
├── .env.production       # Production ortamı
└── .gitignore            # .env dosyalarını ignore eder
```

### 🔄 Environment Hierarchy

1. **Development** (.env.development)
   - Local database
   - Test API keys
   - Debug mode ON

2. **Staging** (.env.staging)
   - Staging database
   - Real API keys
   - Production-like setup

3. **Production** (.env.production)
   - Production database
   - Secure API keys
   - Optimized settings

### 🛡️ Security Best Practices

#### ✅ DO:
- ✅ Use strong, unique secrets (32+ characters)
- ✅ Rotate secrets regularly
- ✅ Use different values per environment
- ✅ Keep .env files in .gitignore
- ✅ Use environment-specific configurations
- ✅ Validate required variables on startup

#### ❌ DON'T:
- ❌ Commit .env files to version control
- ❌ Share secrets in chat/email
- ❌ Use same secrets across environments
- ❌ Hardcode secrets in source code
- ❌ Use weak or default secrets

## 🔧 Platform-Specific Setup

### 📱 React Native App
```typescript
// src/services/databaseService.ts
import Config from 'react-native-config';

const SUPABASE_URL = Config.SUPABASE_URL;
const SUPABASE_ANON_KEY = Config.SUPABASE_ANON_KEY;
```

### 🌐 Next.js Web App
```typescript
// packages/web/src/lib/supabase.ts
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
```

### 🖥️ Node.js Backend
```typescript
// packages/backend/src/config.ts
const config = {
  supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
  supabaseServiceKey: process.env.SUPABASE_SERVICE_ROLE_KEY,
  jwtSecret: process.env.JWT_SECRET,
};
```

## 🚨 Troubleshooting

### ❌ Common Issues

1. **"Missing Supabase configuration"**
   - ✅ Check SUPABASE_URL and SUPABASE_ANON_KEY exist
   - ✅ Verify no extra spaces or line breaks

2. **"Invalid API key"**
   - ✅ Regenerate keys from Supabase dashboard
   - ✅ Check key format (should start with eyJ)

3. **"Cannot connect to backend"**
   - ✅ Verify API_URL points to correct port
   - ✅ Check backend is running on specified port

4. **"Maps not loading"**
   - ✅ Verify NEXT_PUBLIC_MAPBOX_TOKEN is set
   - ✅ Check Mapbox token permissions

## 📞 Support

Environment kurulumunda sorun yaşıyorsan:
1. `env.example` dosyasını kontrol et
2. `.env` dosyasındaki değerleri doğrula
3. Console'da hata mesajlarını kontrol et
4. Backend loglarını incele

---

**⚠️ Güvenlik Uyarısı:** .env dosyalarını asla version control'e commit etme! 