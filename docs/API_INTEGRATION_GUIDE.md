# FISHIVO - API ENTEGRASYON REHBERİ

## 📋 İçindekiler
1. [API Endpoint'leri](#api-endpointleri)
2. [Request/Response Formatları](#requestresponse-formatları)
3. [Authentication](#authentication)
4. [<PERSON><PERSON><PERSON>ling](#error-handling)
5. [Migration Guide](#migration-guide)
6. [Testing](#testing)

---

## 🔌 API Endpoint'leri

### 🎣 Catch (Av) Endpoints

#### POST /api/v1/catches
Yeni av kaydı oluştur

**Request Headers:**
```
Content-Type: application/json
Authorization: Bearer {jwt_token}
X-User-Units: {"weight":"kg","length":"cm","temperature":"celsius"}
```

**Request Body:**
```json
{
  "species": "Levrek",
  "weight": {
    "value": 2.495,
    "unit": "kg",
    "originalUnit": "lbs",
    "displayValue": "5.5 lbs"
  },
  "length": {
    "value": 65.0,
    "unit": "cm", 
    "originalUnit": "inch",
    "displayValue": "25.6 in"
  },
  "location": {
    "name": "İstanbul Boğazı",
    "coordinates": [28.9784, 41.0082],
    "depth": {
      "value": 15.0,
      "unit": "meters"
    }
  },
  "weather": {
    "temperature": {
      "value": 18.0,
      "unit": "celsius"
    },
    "windSpeed": {
      "value": 25.0,
      "unit": "kmh"
    },
    "pressure": {
      "value": 1013.0,
      "unit": "hpa"
    }
  },
  "technique": "spinning",
  "equipment": ["rod_123", "reel_456"],
  "bait": "live_sardine",
  "notes": "Güzel bir av deneyimi",
  "images": ["image_url_1", "image_url_2"],
  "timestamp": "2024-12-20T10:30:00Z"
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "data": {
    "id": "catch_67890",
    "species": "Levrek",
    "weight": {
      "value": 2.495,
      "unit": "kg"
    },
    "length": {
      "value": 65.0,
      "unit": "cm"
    },
    "userId": "user_12345",
    "createdAt": "2024-12-20T10:30:00Z",
    "updatedAt": "2024-12-20T10:30:00Z"
  },
  "message": "Av kaydı başarıyla oluşturuldu"
}
```

#### GET /api/v1/catches
Av kayıtlarını listele

**Query Parameters:**
```
?page=1&limit=20&userId=user_123&species=levrek&minWeight=1.0&maxWeight=5.0
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "catches": [
      {
        "id": "catch_67890",
        "user": {
          "id": "user_12345",
          "name": "Ahmet Yılmaz",
          "avatar": "avatar_url"
        },
        "species": "Levrek",
        "weight": {
          "value": 2.495,
          "unit": "kg"
        },
        "length": {
          "value": 65.0,
          "unit": "cm"
        },
        "location": {
          "name": "İstanbul Boğazı",
          "coordinates": [28.9784, 41.0082]
        },
        "images": ["image_url_1"],
        "likes": 15,
        "comments": 3,
        "createdAt": "2024-12-20T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 156,
      "totalPages": 8
    }
  }
}
```

### 📍 Spot Endpoints

#### POST /api/v1/spots  
Yeni spot oluştur

**Request Body:**
```json
{
  "name": "Galata Köprüsü Altı",
  "description": "Balık çeşitliliği yüksek spot",
  "coordinates": [28.9744, 41.0199],
  "depth": {
    "value": 12.0,
    "unit": "meters"
  },
  "waterType": "saltwater",
  "facilities": ["parking", "toilet", "cafe"],
  "access": "public",
  "difficulty": "easy",
  "bestTimes": ["morning", "evening"],
  "targetSpecies": ["levrek", "lüfer", "palamut"]
}
```

#### GET /api/v1/spots
Spot'ları listele

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "spots": [
      {
        "id": "spot_123",
        "name": "Galata Köprüsü Altı", 
        "coordinates": [28.9744, 41.0199],
        "depth": {
          "value": 12.0,
          "unit": "meters"
        },
        "rating": 4.5,
        "catchCount": 89,
        "createdBy": "user_456"
      }
    ]
  }
}
```

### ⚙️ User Settings Endpoints

#### GET /api/v1/users/me/units
Kullanıcı birim tercihlerini al

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "units": {
      "weight": "kg",
      "length": "cm", 
      "distance": "km",
      "temperature": "celsius",
      "depth": "meters",
      "speed": "kmh",
      "pressure": "hpa"
    },
    "region": "TR",
    "customSettings": {
      "showDecimals": true,
      "shortFormat": false,
      "autoConvert": true
    }
  }
}
```

#### PUT /api/v1/users/me/units
Kullanıcı birim tercihlerini güncelle

**Request Body:**
```json
{
  "units": {
    "weight": "lbs",
    "length": "inch",
    "temperature": "fahrenheit"
  },
  "region": "US"
}
```

---

## 📊 Request/Response Formatları

### 🏗 Measurement Object Structure

Backend'e gönderilen ve alınan tüm ölçüm verileri bu formatta:

```typescript
interface MeasurementValue {
  value: number;           // Numeric değer (base unit'te)
  unit: string;           // Base unit (kg, cm, celsius, etc.)
  originalUnit?: string;  // Kullanıcının girdiği birim (opsiyonel)
  displayValue?: string;  // UI'da gösterilen format (opsiyonel)
}

// Örnekler
{
  "weight": {
    "value": 2.495,
    "unit": "kg",
    "originalUnit": "lbs", 
    "displayValue": "5.5 lbs"
  },
  "temperature": {
    "value": 18.0,
    "unit": "celsius"
  }
}
```

### 🎯 Base Units Reference

| Kategori    | Base Unit | Backend Storage |
|-------------|-----------|----------------|
| weight      | kg        | kilograms      |
| length      | cm        | centimeters    |
| distance    | km        | kilometers     |
| temperature | celsius   | celsius        |
| depth       | meters    | meters         |
| speed       | kmh       | km/hour        |
| pressure    | hpa       | hectopascals   |

### 📝 Data Validation Rules

Backend validation kuralları:

```json
{
  "weight": {
    "kg": { "min": 0.001, "max": 1000, "precision": 3 },
    "value_range": "0.001 - 1000 kg"
  },
  "length": {
    "cm": { "min": 0.1, "max": 1000, "precision": 1 },
    "value_range": "0.1 - 1000 cm"
  },
  "temperature": {
    "celsius": { "min": -50, "max": 60, "precision": 1 },
    "value_range": "-50 - 60 °C"
  }
}
```

---

## 🔐 Authentication

### JWT Token Format
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### User Context Headers
```
X-User-ID: user_12345
X-User-Region: TR
X-User-Units: {"weight":"kg","length":"cm","temperature":"celsius"}
```

---

## ❌ Error Handling

### Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "INVALID_MEASUREMENT",
    "message": "Ağırlık değeri geçersiz",
    "details": {
      "field": "weight.value",
      "value": -5.0,
      "constraint": "must be positive"
    }
  },
  "timestamp": "2024-12-20T10:30:00Z"
}
```

### Common Error Codes

| Code | HTTP Status | Açıklama |
|------|-------------|----------|
| `INVALID_MEASUREMENT` | 400 | Geçersiz ölçüm değeri |
| `UNSUPPORTED_UNIT` | 400 | Desteklenmeyen birim |
| `CONVERSION_ERROR` | 400 | Birim dönüşüm hatası |
| `UNITS_NOT_FOUND` | 404 | Kullanıcı birim tercihleri bulunamadı |
| `CATCH_NOT_FOUND` | 404 | Av kaydı bulunamadı |
| `UNAUTHORIZED` | 401 | Geçersiz authentication |
| `FORBIDDEN` | 403 | Yetki yetersiz |

---

## 🔄 Migration Guide

### Eski String Format → Yeni Object Format

#### Frontend Migration
```typescript
// BEFORE (Eski format)
const catch = {
  weight: "2.5 kg",
  length: "65 cm"
};

// AFTER (Yeni format)
const catch = {
  weight: {
    value: 2.5,
    unit: "kg"
  },
  length: {
    value: 65.0,
    unit: "cm"
  }
};

// Migration utility
const migrateCatchData = (oldCatch) => {
  const parseValue = (str) => {
    const match = str.match(/^([\d.]+)\s*([a-zA-Z°]+)$/);
    return match ? {
      value: parseFloat(match[1]),
      unit: normalizeUnit(match[2])
    } : null;
  };

  return {
    ...oldCatch,
    weight: parseValue(oldCatch.weight),
    length: parseValue(oldCatch.length)
  };
};
```

#### Backend Migration Script
```sql
-- Database migration
UPDATE catches 
SET weight_value = CAST(SUBSTRING(weight, 1, POSITION(' ' IN weight)-1) AS DECIMAL),
    weight_unit = SUBSTRING(weight, POSITION(' ' IN weight)+1)
WHERE weight_value IS NULL AND weight IS NOT NULL;

UPDATE catches 
SET length_value = CAST(SUBSTRING(length, 1, POSITION(' ' IN length)-1) AS DECIMAL),
    length_unit = SUBSTRING(length, POSITION(' ' IN length)+1)  
WHERE length_value IS NULL AND length IS NOT NULL;
```

### API Versioning Strategy

#### v1 → v2 Migration
```javascript
// v1 endpoint (deprecated)
GET /api/v1/catches
// Response: { weight: "2.5 kg" }

// v2 endpoint (current)  
GET /api/v2/catches
// Response: { weight: { value: 2.5, unit: "kg" } }

// Backward compatibility
GET /api/v1/catches?format=legacy
// Response: { weight: "2.5 kg", weight_obj: { value: 2.5, unit: "kg" } }
```

---

## 🧪 Testing

### Unit Tests

#### Conversion Testing
```typescript
describe('Unit Conversion API', () => {
  test('should convert weight from lbs to kg', async () => {
    const response = await request(app)
      .post('/api/v1/catches')
      .set('X-User-Units', '{"weight":"lbs"}')
      .send({
        weight: { value: 5.5, unit: 'lbs' },
        species: 'Bass'
      });
      
    expect(response.body.data.weight.value).toBeCloseTo(2.495, 2);
    expect(response.body.data.weight.unit).toBe('kg');
  });
});
```

#### Integration Testing
```typescript
describe('End-to-End Unit System', () => {
  test('user with different units sees converted values', async () => {
    // US user creates catch
    const catch = await createCatch({
      weight: { value: 5.5, unit: 'lbs' }
    }, { units: { weight: 'lbs' } });
    
    // TR user views the same catch
    const response = await getCatch(catch.id, { 
      units: { weight: 'kg' } 
    });
    
    expect(response.weight.displayValue).toBe('2.5 kg');
  });
});
```

### API Testing with Postman

#### Collection Setup
```json
{
  "info": { "name": "Fishivo Units API" },
  "auth": {
    "type": "bearer",
    "bearer": [{ "key": "token", "value": "{{jwt_token}}" }]
  },
  "event": [
    {
      "listen": "prerequest",
      "script": {
        "exec": [
          "pm.request.headers.add({",
          "  key: 'X-User-Units',", 
          "  value: JSON.stringify(pm.environment.get('user_units'))",
          "});"
        ]
      }
    }
  ]
}
```

### Load Testing

#### Performance Benchmarks
```bash
# Artillery load test
artillery run load-test.yml

# Expected performance
# GET /api/v1/catches: < 200ms (p95)
# POST /api/v1/catches: < 500ms (p95)  
# Unit conversion overhead: < 10ms
```

---

## 📊 Monitoring & Analytics

### Metrics to Track
```typescript
// Conversion performance
const conversionMetrics = {
  conversionTime: 'avg_conversion_time_ms',
  conversionErrors: 'conversion_error_count',
  unitDistribution: 'user_unit_preferences_distribution'
};

// API performance  
const apiMetrics = {
  catchCreateTime: 'catch_create_duration_ms',
  catchListTime: 'catch_list_duration_ms',
  unitUpdateTime: 'unit_preference_update_duration_ms'
};
```

### Error Tracking
```javascript
// Sentry integration
const trackConversionError = (error, context) => {
  Sentry.captureException(error, {
    tags: {
      component: 'unit_conversion',
      from_unit: context.fromUnit,
      to_unit: context.toUnit,
      category: context.category
    },
    extra: context
  });
};
```

---

## 🔧 Development Utilities

### Swagger API Documentation
```yaml
# openapi.yml
paths:
  /api/v1/catches:
    post:
      summary: Create a new catch
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CatchRequest'
      responses:
        201:
          description: Catch created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CatchResponse'

components:
  schemas:
    MeasurementValue:
      type: object
      properties:
        value:
          type: number
          description: Numeric value in base unit
        unit:
          type: string
          description: Base unit identifier
```

### GraphQL Schema
```graphql
type Catch {
  id: ID!
  species: String!
  weight: MeasurementValue!
  length: MeasurementValue!
  user: User!
  createdAt: DateTime!
}

type MeasurementValue {
  value: Float!
  unit: String!
  displayValue(userUnits: UserUnits): String!
}

input CreateCatchInput {
  species: String!
  weight: MeasurementValueInput!
  length: MeasurementValueInput!
}
```

---


### Rate Limits
```
• Authenticated requests: 1000 req/hour
• Unauthenticated requests: 100 req/hour  
• Catch creation: 50 req/hour
• Unit updates: 10 req/hour
```

---

*Son Güncelleme: 20 Aralık 2024*
*API Versiyon: v2.1.0* 